syntax = "proto3";

package moego.admin.platform_care.v1;

import "google/protobuf/timestamp.proto";
import "moego/admin/platform_sales/v1/platform_sales_admin.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/platform_care/v1;platformcareapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.platform_care.v1";

// annual contract api
service AnnualContractApi {
  // create annual contract
  rpc CreateAnnualContract(CreateAnnualContractParams) returns (CreateAnnualContractResult);
  // list annual contracts
  rpc ListAnnualContracts(ListAnnualContractsParams) returns (ListAnnualContractsResult);
}

// create annual contract params
message CreateAnnualContractParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // subscription plan
  platform_sales.v1.SubscriptionPlan subscription_plan = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // subscription term months
  // e.g., 12 means 12 months (1 year)
  int32 subscription_term_months = 3 [(validate.rules).int32 = {gt: 0}];
  // discount percentage of subscription
  // e.g., "10" means 10%
  string discount_percentage = 4 [(validate.rules).string = {min_len: 1}];

  // number of boarding & daycare locations
  int32 bd_location_count = 5 [(validate.rules).int32 = {gte: 0}];
  // number of grooming locations
  int32 grooming_location_count = 6 [(validate.rules).int32 = {gte: 0}];
  // number of grooming vans
  int32 grooming_van_count = 7 [(validate.rules).int32 = {gte: 0}];
}

// create annual contract result
message CreateAnnualContractResult {
  // contract id
  string id = 1;
}

// list annual contracts params
message ListAnnualContractsParams {
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1;

  // owner email
  optional string owner_email = 2;
  // creator
  optional string creator = 3;
}

// list annual contracts result
message ListAnnualContractsResult {
  // annual contracts
  repeated AnnualContract annual_contracts = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// annual contract
message AnnualContract {
  // id
  string id = 1;
  // template id
  string template_id = 2;
  // metadata
  Metadata metadata = 3;
  // params
  Parameters parameters = 4;
  // content
  string content = 5;
  // creator
  string creator = 6;
  // create time
  google.protobuf.Timestamp create_time = 7;
  // update time
  google.protobuf.Timestamp update_time = 8;
  // sign time
  optional google.protobuf.Timestamp sign_time = 9;

  // metadata
  message Metadata {
    // company id
    int64 company_id = 1;
    // account id
    int64 account_id = 2;
    // subscription plan
    platform_sales.v1.SubscriptionPlan subscription_plan = 3;
  }

  // parameters for rendering
  message Parameters {
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // company name
    string company_name = 1;
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // owner name
    string owner_name = 2;
    // owner email
    string owner_email = 3;
    // address
    string address = 4;
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // subscription plan name
    string subscription_plan_name = 5;
    // subscription term months
    int32 subscription_term_months = 6;
    // discount percentage
    string discount_percentage = 7;
    // total amount
    string total_amount = 8;
    // grooming location
    optional ProductLineItem grooming_location = 9;
    // boarding & daycare location
    optional ProductLineItem bd_location = 10;
    // grooming van
    optional ProductLineItem grooming_van = 11;
  }

  // product line item
  message ProductLineItem {
    // The number of units.
    int32 quantity = 1;
    // The price per unit before any discounts.
    string unit_price = 2;
    // The final price per unit after discounts have been applied.
    string discounted_unit_price = 3;
  }
}
