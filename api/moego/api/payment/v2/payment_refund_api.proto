syntax = "proto3";

package moego.api.payment.v2;

import "google/type/money.proto";
import "moego/models/payment/v2/payment_models.proto";
import "moego/utils/v1/time_period.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/payment/v2;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.payment.v2";

// refund params
message RefundParams {
  // payment id
  int64 payment_id = 1;
  // 退款金额
  google.type.Money amount = 2;
}

// refund result
message RefundResult {
  // refund model
  models.payment.v2.RefundView refund_view = 1;
}

// get refund params
message GetRefundParams {
  // refund id
  int64 refund_id = 1;
}

// get refund result
message GetRefundResult {
  // refund view
  models.payment.v2.RefundView refund_view = 1;
}

// list refund params
message ListRefundParams {
  // filter
  message Filter {
    // customer id
    repeated int64 customer_ids = 1;
    // order id
    repeated int64 order_ids = 2;
    // order payment id
    repeated int64 order_payment_ids = 3;
    // payment id
    repeated int64 payment_ids = 4;
    // refund id list
    repeated int64 refund_ids = 5;
    // 查询时间范围
    moego.utils.v1.TimePeriod time_period = 6;
  }

  // 分页查询请求
  utils.v2.PaginationRequest pagination_request = 1;
  // filter
  Filter filter = 2;
}

// list refund result
message ListRefundResult {
  // 分页
  utils.v2.PaginationResponse pagination_request = 1;
  // 退款列表
  repeated models.payment.v2.RefundView refund_views = 2;
}

// refund service
service RefundService {
  // refund 给前端直接调用的退款
  rpc Refund(RefundParams) returns (RefundResult);
  // 查询refund
  rpc GetRefund(GetRefundParams) returns (GetRefundResult);
  // 获取退款列表
  rpc ListRefund(ListRefundParams) returns (ListRefundResult);
}
