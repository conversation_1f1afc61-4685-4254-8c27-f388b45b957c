syntax = "proto3";

package moego.api.payment.v2;

import "moego/models/payment/v2/common_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/payment/v2;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.payment.v2";

// Payment Terminal service.
service PaymentTerminalService {
  // Retrieve required data to initialize the client side terminal SDK.
  rpc RetrieveSdkData(RetrieveSdkDataParams) returns (RetrieveSdkDataResult);
}

// Request for RetrieveSdkData
message RetrieveSdkDataParams {
  // Adyen params
  message AdyenParams {
    // Setup token
    string setup_token = 1;
  }

  // 渠道
  models.payment.v2.ChannelType channel_type = 1;
  // Request params
  oneof params {
    // Data for Adyen
    AdyenParams adyen_params = 2;
  }
}

// Response for RetrieveSdkData
message RetrieveSdkDataResult {
  // adyen data
  message AdyenData {
    // SDK data
    string sdk_data = 1;
  }

  // 支付数据
  oneof data {
    // adyen data
    AdyenData adyen_data = 1;
  }
}
