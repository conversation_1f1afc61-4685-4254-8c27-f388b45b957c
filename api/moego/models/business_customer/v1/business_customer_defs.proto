syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/business_customer/v1/business_customer_address_defs.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_note_defs.proto";
import "moego/models/business_customer/v1/business_customer_preference_defs.proto";
import "moego/models/customer/v1/customer_enums.proto";
import "moego/utils/v2/list.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// create def for business customer
message BusinessCustomerCreateDef {
  // source, required
  moego.models.business_customer.v1.BusinessCustomerInfoModel.Source source = 1 [(validate.rules).enum = {
    defined_only: true
    // 未知来源和一些历史数据来源不允许再新增
    not_in: [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7
    ]
  }];

  // phone number, required
  // 手机号格式校验放在业务代码里处理, 这里只检查长度
  // 对于 DM 进来的数据, 允许手机号为空
  string phone_number = 2 [(validate.rules).string = {max_len: 20}];

  // email, default is empty
  // 邮箱格式校验放在业务代码里处理, 这里只检查长度
  string email = 3 [(validate.rules).string = {max_len: 255}];

  // avatar path, default is empty
  string avatar_path = 4 [(validate.rules).string = {
    ignore_empty: true
    uri: true
    max_len: 255
  }];

  // first name, default is empty
  // 业务上必填, 但是也兼容不填的情况, 为以后业务变更做准备
  string first_name = 5 [(validate.rules).string = {max_len: 50}];

  // last name, default is empty
  // 业务上必填, 但是也兼容不填的情况, 为以后业务变更做准备
  string last_name = 6 [(validate.rules).string = {max_len: 50}];

  // client color, default is empty
  optional string client_color = 7 [(validate.rules).string = {max_len: 50}];

  // preferred business id
  // 目前业务上是必填的, 但是也兼容 preferred business 不填的情况, 为以后业务变更做准备
  // 不填默认为 0 (没有 preferred business)
  optional int64 preferred_business_id = 8 [(validate.rules).int64 = {gt: 0}];

  // account id
  // 是否绑定一个账号, B 端场景通常不传, C 端 (主要是 Branded App) 创建时会传
  // 不填默认为 0 (没有绑定账号)
  optional int64 account_id = 9 [(validate.rules).int64 = {gt: 0}];

  // referral source id
  // 不填默认为 0 (没有 referral source id)
  optional int64 referral_source_id = 10 [(validate.rules).int64 = {gt: 0}];

  // customer tag ids
  repeated int64 tag_ids = 11 [(validate.rules).repeated = {
    unique: true
    max_items: 20
    items: {
      int64: {gt: 0}
    }
  }];

  // external id
  // 默认不填, 只有 DM 会在创建时传入
  optional string external_id = 12 [(validate.rules).string = {max_len: 20}];

  // created at
  // 默认不填, 只有 DM 会在创建时传入
  optional google.protobuf.Timestamp created_at = 13 [(validate.rules).timestamp = {
    gte: {
      seconds: 0
      nanos: 0
    }
  }];
  // updated at
  // 默认不填, 只有 DM 会在创建时传入
  optional google.protobuf.Timestamp updated_at = 14 [(validate.rules).timestamp = {
    gte: {
      seconds: 0
      nanos: 0
    }
  }];

  // birthday
  optional google.protobuf.Timestamp birthday = 15;
  // referral source desc 这个字段和 moe_customer_source 表中的name字段是不同含义，只针对 app 端 使用，web端没有写入场景
  optional string referral_source_desc = 16;
}

// update def for business customer with additional info
message BusinessCustomerWithAdditionalInfoCreateDef {
  // customer
  BusinessCustomerCreateDef customer = 1 [(validate.rules).message = {required: true}];

  // communication preference, optional
  optional moego.models.business_customer.v1.BusinessCustomerCommunicationPreferenceUpdateDef communication_preference = 2;

  // appointment preference, optional
  optional moego.models.business_customer.v1.BusinessCustomerAppointmentPreferenceUpdateDef appointment_preference = 3;

  // payment preference, optional
  optional moego.models.business_customer.v1.BusinessCustomerPaymentPreferenceUpdateDef payment_preference = 4;

  // primary address, optional
  // `is_primary` field is ignored and will be set to true.
  optional moego.models.business_customer.v1.BusinessCustomerAddressCreateDef primary_address = 5;

  // additional addresses, empty means no additional addresses
  // `is_primary` field is ignored and will be set to false.
  // if `primary_address` is not set, the first address in this list will be set as primary address.
  repeated moego.models.business_customer.v1.BusinessCustomerAddressCreateDef additional_addresses = 6 [(validate.rules).repeated = {max_items: 20}];

  // customer notes, empty means no notes
  repeated moego.models.business_customer.v1.BusinessCustomerNoteCreateDef notes = 7 [(validate.rules).repeated = {
    max_items: 200
    items: {
      message: {required: true}
    }
  }];

  // additional contacts, empty means no additional contacts
  // repeated moego.models.business_customer.v1.BusinessCustomerContactCreateDef additional_contacts = 5 [(validate.rules).repeated = {max_items: 20}];
}

// update def for business customer
message BusinessCustomerUpdateDef {
  // avatar path, optional
  // if set to empty, avatar path will be cleared
  optional string avatar_path = 1 [(validate.rules).string = {
    ignore_empty: true
    uri: true
    max_len: 255
  }];

  // first name, optional
  // if set to empty, first name will be cleared
  optional string first_name = 2 [(validate.rules).string = {max_len: 50}];

  // last name, optional
  // if set to empty, last name will be cleared
  optional string last_name = 3 [(validate.rules).string = {max_len: 50}];

  // customer tags, optional
  // if this message is not set, customer tags of the customer will not be updated
  // if this message is set but the ids field is empty, customer tags of the customer will be cleared
  optional CustomerTagList customer_tags = 4;

  // email, optional
  optional string email = 5 [(validate.rules).string = {max_len: 50}];

  // birthday
  optional google.protobuf.Timestamp birthday = 6;

  // address, optional
  optional BusinessCustomerAddressUpdateDef address = 7;

  // preferred groomer id, optional
  optional int64 preferred_groomer_id = 8 [(validate.rules).int64 = {gt: 0}];

  // Emergency contact, optional
  optional EmergencyContact emergency_contact = 9;

  // Authorized user to pickup the pet, optional
  optional PickupContact pickup_contact = 10;

  // phone number
  optional string phone_number = 11 [(validate.rules).string = {max_len: 50}];

  // referral source id
  optional int32 referral_source_id = 12 [(validate.rules).int32 = {gt: 0}];

  // preferred frequency day
  optional int32 preferred_frequency_day = 13 [(validate.rules).int32 = {gt: 0}];

  // preferred frequency type
  // deprecated by Freeman since 2025/7/22, 这个枚举定义的 number 和之前的数据没对上，use preferred_frequency_type_v2 instead
  optional moego.models.customer.v1.PreferredFrequencyType preferred_frequency_type = 14 [
    deprecated = true,
    (validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }
  ];
  // preferred frequency type v2
  optional int32 preferred_frequency_type_v2 = 17 [(validate.rules).int32 = {gte: 0}];

  // preferred day of week, 0-6, Sunday-Saturday
  optional moego.utils.v2.Int32List preferred_day = 15;

  // preferred time of day, minutes
  optional moego.utils.v2.Int32List preferred_time = 16;

  // TODO: add more fields

  // customer tag list
  message CustomerTagList {
    // customer tag ids
    repeated int64 ids = 1 [(validate.rules).repeated = {
      unique: true
      max_items: 20
      items: {
        int64: {gt: 0}
      }
    }];
  }

  // emergency contact
  message EmergencyContact {
    // customer contact id, optional
    optional int64 id = 4 [(validate.rules).int64 = {gt: 0}];
    // first name
    optional string first_name = 1 [(validate.rules).string = {max_len: 50}];
    // last name
    optional string last_name = 2 [(validate.rules).string = {max_len: 50}];
    // phone number
    optional string phone_number = 3 [(validate.rules).string = {max_len: 50}];
  }

  // pickup contact
  message PickupContact {
    // customer contact id, optional
    optional int64 id = 4 [(validate.rules).int64 = {gt: 0}];
    // first name
    optional string first_name = 1 [(validate.rules).string = {max_len: 50}];
    // last name
    optional string last_name = 2 [(validate.rules).string = {max_len: 50}];
    // phone number
    optional string phone_number = 3 [(validate.rules).string = {max_len: 50}];
  }
}
