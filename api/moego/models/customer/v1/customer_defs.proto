syntax = "proto3";

package moego.models.customer.v1;

import "google/protobuf/timestamp.proto";
import "moego/utils/v2/list.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// online booking customer definition
message CustomerDef {
  // first name
  optional string first_name = 1 [(validate.rules).string.max_len = 50];
  // last name
  optional string last_name = 2 [(validate.rules).string.max_len = 50];
  // avatar path
  optional string avatar_path = 3 [(validate.rules).string.max_len = 255];
  // phone number
  optional string phone_number = 4 [(validate.rules).string.max_len = 30];
  // email
  optional string email = 5 [(validate.rules).string.max_len = 50];
  // referral source id
  optional int32 referral_source_id = 6 [(validate.rules).int32.gt = 0];
  // preferred groomer id
  optional int32 preferred_groomer_id = 7 [(validate.rules).int32.gt = 0];
  // preferred frequency type, 0: day, 1: week, 2: month
  optional int32 preferred_frequency_type = 8 [(validate.rules).int32 = {
    in: [
      0,
      1,
      2
    ]
  }];
  // preferred frequency day
  optional int32 preferred_frequency_day = 9;
  // preferred day of week
  // Deprecated by Freeman since 2025/7/22, use preferred_days instead
  repeated int32 preferred_day = 10 [
    deprecated = true,
    (validate.rules).repeated = {
      max_items: 7
      items: {
        int32: {
          gte: 0
          lte: 6
        }
      }
    }
  ];
  // preferred time of day
  // Deprecated by Freeman since 2025/7/22, use preferred_times instead
  repeated int32 preferred_time = 11 [
    deprecated = true,
    (validate.rules).repeated = {
      max_items: 2
      items: {
        int32: {
          gte: 0
          lte: 1440
        }
      }
    }
  ];
  // preferred days of week
  optional moego.utils.v2.Int32List preferred_days = 15;
  // preferred times of day
  optional moego.utils.v2.Int32List preferred_times = 16;
  // contact
  message Contact {
    // first name
    string first_name = 1 [(validate.rules).string.max_len = 50];
    // last name
    string last_name = 2 [(validate.rules).string.max_len = 50];
    // phone number
    string phone_number = 3 [(validate.rules).string.max_len = 30];
  }
  // emergency contact
  Contact emergency_contact = 12;
  // pickup contact
  Contact pickup_contact = 13;
  // birthday, 只有定义没有实现，birthday 暂时不支持 C 端修改/删除
  // 原因是 Timestamp 没有一个合适的值适合用于表示删除，Timestamp 的零值（1970-01-01T00:00:00Z）对前端太不友好
  // 并且 C 端删除 birthday 的能力会依赖于 B 端，B 端暂时不支持删除 birthday
  optional google.protobuf.Timestamp birthday = 14;
}
