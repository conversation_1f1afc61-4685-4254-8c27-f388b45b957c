// @since 2022-06-24 15:44:42
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.errors.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1;errorspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.errors.v1";

// the error codes
// NOTE: not allow alias
enum Code {
  // reserved
  CODE_UNSPECIFIED = 0;
  // success
  CODE_SUCCESS = 200;
  // params error
  CODE_PARAMS_ERROR = 400;
  // unauthorized, means need to login again
  CODE_UNAUTHORIZED_ERROR = 401;
  // no permission to operate
  CODE_FORBIDDEN = 403;
  // rate limit quota exceeded
  CODE_TOO_MANY_REQUESTS = 429;
  // server internal error
  CODE_SERVER_ERROR = 500;

  //moego-boarding error code start 10000 end 20000
  //boarding is not used so far , so this is also used as Common Error Code
  // ORDERID_EXISTS
  CODE_ORDERID_EXISTS = 10010;
  // ROOM_ERROR
  CODE_ROOM_ERROR = 10015;
  // PRIMARY_ID_EMPTY
  CODE_PRIMARY_ID_EMPTY = 10016;
  // VERIFY_CODE_NOT_EQUAL
  CODE_VERIFY_CODE_NOT_EQUAL = 10017;
  // PARALLEL_ERROR
  CODE_PARALLEL_ERROR = 10018;

  // server forced branch mismatch
  CODE_BRANCH_MISMATCH = 10999;
  // assign item amount error
  CODE_ASSIGN_ITEM_AMOUNT_ONCE_ONLY_ERROR = 11001;
  // asign item amount data permission error
  CODE_ASSIGN_ITEM_AMOUNT_DATA_PERMISSION_ERROR = 11002;

  //moego-business error code start 20000 end 30000
  // CHECK_IN_ERROR
  CODE_CHECK_IN_ERROR = 20001;
  // CHECK_OUT_ERROR
  CODE_CHECK_OUT_ERROR = 20002;
  // PASSWORD_ERROR
  CODE_PASSWORD_ERROR = 20003;
  // ACCOUNT_NOT_FOUND
  CODE_ACCOUNT_NOT_FOUND = 20004;
  // INVALID_CODE
  CODE_INVALID_CODE = 20005;
  // STAFF_BINDING_ERROR1
  CODE_STAFF_BINDING_ERROR1 = 20006;
  // STAFF_BINDING_ERROR2
  CODE_STAFF_BINDING_ERROR2 = 20007;
  // STAFF_BINDING_ERROR3
  CODE_STAFF_BINDING_ERROR3 = 20008;
  // STAFF_BINDING_ERROR4
  CODE_STAFF_BINDING_ERROR4 = 20009;
  // DELETE_ROLE_ERROR
  CODE_DELETE_ROLE_ERROR = 20010;
  // PAYMENT_METHOD_NAME_EXISTS
  CODE_PAYMENT_METHOD_NAME_EXISTS = 20011;
  // DEFAULT_METHOD_OPER_FALSE
  CODE_DEFAULT_METHOD_OPER_FALSE = 20012;
  // FILE_SIZE_EXCEEDS_LIMIT
  CODE_FILE_SIZE_EXCEEDS_LIMIT = 20013;
  // TAX_IS_USED
  CODE_TAX_IS_USED = 20014;
  // TAG_NAME_EXIST
  CODE_TAG_NAME_EXIST = 20015;
  // TAG_NAME_NULL
  CODE_TAG_NAME_NULL = 20016;
  // THE_LAST_DELETED
  CODE_THE_LAST_DELETED = 20017;
  // THE_LAST_INACTIVE
  CODE_THE_LAST_INACTIVE = 20018;
  // PERMISSION_NOT_ENOUGH
  CODE_PERMISSION_NOT_ENOUGH = 20019;
  // NO_PERMISSION_SHOW_MESSAGE_THREAD
  CODE_NO_PERMISSION_SHOW_MESSAGE_THREAD = 20020;
  // TAX_IS_USED_RETAIL
  CODE_TAX_IS_USED_RETAIL = 20021;
  // NOT_ENOUGH_VANS_NUM
  CODE_NOT_ENOUGH_VANS_NUM = 20022;
  // PASSWORD_UPDATE_OLDPWD_ERROR
  CODE_PASSWORD_UPDATE_OLDPWD_ERROR = 20023;
  // ACCOUNT_NOT_OWN_THIS_COMPANY
  CODE_ACCOUNT_NOT_OWN_THIS_COMPANY = 20024;
  // CHECK_EMAIL_EXIST_PHP_VERSION
  CODE_CHECK_EMAIL_EXIST_PHP_VERSION = 20025;
  // COMPANY_NOT_FOUND
  CODE_COMPANY_NOT_FOUND = 20026;
  // SWITCH_BUSINESS_ERROR
  CODE_SWITCH_BUSINESS_ERROR = 20027;
  // STAFF_ONLY_BANDING_ONE_VAN(20028, "Staff can only be bound to one van, please refresh and try again.")
  CODE_STAFF_ONLY_BANDING_ONE_VAN = 20028;
  // STAFF_NOT_FOUND_IN_FROM_VAN(20029, "Staff record not found in from van")
  CODE_STAFF_NOT_FOUND_IN_FROM_VAN = 20029;
  // STAFF_FOUND_IN_TO_VAN(20030, "Staff record was found in to van")
  CODE_STAFF_FOUND_IN_TO_VAN = 20030;
  // PROCESSING_FEE_SHOULD_IN_STRIPE_PAY(20031, "This feature is for Stripe only. Please set Stripe as primary credit card provider.")
  CODE_PROCESSING_FEE_SHOULD_IN_STRIPE_PAY = 20031;
  // PROCESSING_FEE_SHOULD_IN_US_AREA(20032, "Passing credit card processing fees to clients is not available in this area.")
  CODE_PROCESSING_FEE_SHOULD_IN_US_AREA = 20032;
  // STAFF_UNLINK_REASON_OWNER(20033, "Business Owner cannot be unlink")
  CODE_STAFF_UNLINK_REASON_OWNER = 20033;
  // STAFF_UNLINK_REASON_NO_LINK(20034, "This staff is not linked to an account")
  CODE_STAFF_UNLINK_REASON_NO_LINK = 20034;
  // STAFF_UNLINK_REASON_HAS_BEEN_DELETED(20035, "Staff has been deleted")
  CODE_STAFF_UNLINK_REASON_HAS_BEEN_DELETED = 20035;
  // CAN_NOT_CREATE_STAFF(20036, "The current plan cannot create more staff.")
  CODE_CAN_NOT_CREATE_STAFF = 20036;
  // NOT_ENOUGH_VANS_NUM2(20027, "can't create new van, need upgrade") 20027 已被占用，修改为20037
  CODE_NOT_ENOUGH_VANS_NUM2 = 20037;
  // SMART_SCHEDULE_RULE_ALREADY_EXISTS
  CODE_SMART_SCHEDULE_RULE_ALREADY_EXISTS = 20038;
  // BUSINESS_NOT_FOUND
  CODE_BUSINESS_NOT_FOUND = 20039;
  // ACCOUNT_NOT_OWN_THIS_ENTERPRISE
  CODE_ACCOUNT_NOT_OWN_THIS_ENTERPRISE = 20040;
  // ENTERPRISE_NOT_OWN_THIS_COMPANY
  CODE_ENTERPRISE_NOT_OWN_THIS_COMPANY = 20041;
  // ENTERPRISE_NOT_FOUND
  CODE_ENTERPRISE_NOT_FOUND = 20042;
  // COMPANY_REACH_MAX_LIMIT
  CODE_COMPANY_REACH_MAX_LIMIT = 20043;
  // SWITCH_COMPANY_ERROR
  CODE_SWITCH_COMPANY_ERROR = 20044;
  // STAFF_NOT_FOUND
  CODE_STAFF_NOT_FOUND = 20045;
  // STAFF_WORKING_LOCATION_LIST_IS_EMPTY
  CODE_STAFF_WORKING_LOCATION_LIST_IS_EMPTY = 20046;
  // STAFF_BINDING_ERROR5
  CODE_STAFF_BINDING_EMAIL_INCORRECT = 20047;
  // STAFF LOGIN LIMIT
  CODE_STAFF_LOGIN_LIMIT = 20048;

  //moego-customer error code start 30000 end 40000
  // EMAIL_NOT_EXIST
  CODE_EMAIL_NOT_EXIST = 30000;
  // EMAIL_EXIST
  CODE_EMAIL_EXIST = 30001;
  // PHONE_EXIST
  CODE_PHONE_EXIST = 30002;
  // CUSTOMER_NOT_FOUND
  CODE_CUSTOMER_NOT_FOUND = 30003;
  // PET_NOT_FOUND
  CODE_PET_NOT_FOUND = 30004;
  // APPOINTMENT_NOT_FOUND
  CODE_APPOINTMENT_NOT_FOUND = 30005;
  // PET_HAVE_APPOINTMENT
  CODE_PET_HAVE_APPOINTMENT = 30006;
  // APPOINTMENT_SIGN_OVER
  CODE_APPOINTMENT_SIGN_OVER = 30007;
  // APPOINTMENT_PET_NOT_EXIST
  CODE_APPOINTMENT_PET_NOT_EXIST = 30008;
  // APPOINTMENT_AGREEMENT_SEND_CONTENT_NOT_CONFIG
  CODE_APPOINTMENT_AGREEMENT_SEND_CONTENT_NOT_CONFIG = 30009;
  // PET_CODE_EXISTS
  CODE_PET_CODE_EXISTS = 30010;
  // EMAIL_EXISTS_MULTI
  CODE_EMAIL_EXISTS_MULTI = 30011;
  // BIRTHDAY_FORMAT_ERROR
  CODE_BIRTHDAY_FORMAT_ERROR = 30012;
  // CUSTOMER_IS_BLOCKED
  CODE_CUSTOMER_IS_BLOCKED = 30013;
  // CUSTOMER_ALREADY_EXISTS
  CODE_CUSTOMER_ALREADY_EXISTS = 30014;
  // INTAKE_FORM_IS_DELETED
  CODE_INTAKE_FORM_IS_DELETED = 30015;
  // INTAKE_FORM_MESSAGE_TOO_LONG
  CODE_INTAKE_FORM_MESSAGE_TOO_LONG = 30016;
  // PET_BREED_NOT_ALLOW_DELETE
  CODE_PET_BREED_NOT_ALLOW_DELETE = 30017;
  // CUSTOMER_ACCOUNT_OR_PASSWORD_ERROR
  CODE_CUSTOMER_ACCOUNT_OR_PASSWORD_ERROR = 30018;
  // CUSTOMER_ACCOUNT_NOT_FOUND
  CODE_CUSTOMER_ACCOUNT_NOT_FOUND = 30019;
  // CUSTOMER_SESSION_NOT_FOUND
  CODE_CUSTOMER_SESSION_NOT_FOUND = 30020;
  // CUSTOMER_INVALID_CODE
  CODE_CUSTOMER_INVALID_CODE = 30021;
  // PET_CODE_DESCRIPTION_TOO_LONG(30022, "Description should less than 50 characters.")
  CODE_PET_CODE_DESCRIPTION_TOO_LONG = 30022;
  // EMAIL_OR_CODE_ERROR(30023, "Email or verification code error.")
  CODE_EMAIL_OR_CODE_ERROR = 30023;
  // PHONE_OR_CODE_ERROR(30024, "Phone number or verification code error.")
  CODE_PHONE_OR_CODE_ERROR = 30024;
  // LICENSE_INVALID(30025, "License is invalid.")
  CODE_LICENSE_INVALID = 30025;
  // PHONE_INVALID(30026, "The phone number is invalid, please contact your service provider.")
  CODE_PHONE_INVALID = 30026;
  // EMAIL_INVALID(30027, "The email is invalid, please contact your service provider.")
  CODE_EMAIL_INVALID = 30027;
  // customer creation from given source is disabled
  CODE_CUSTOMER_CREATION_FROM_GIVEN_SOURCE_IS_DISABLED = 30028;
  // customer is merging
  CODE_CUSTOMER_MERGING = 30029;
  // customer not duplicate
  CODE_CUSTOMER_NOT_DUPLICATE = 30030;

  // business customer address error code
  // ADDRESS_NOT_FOUND
  CODE_ADDRESS_NOT_FOUND = 30100;
  // CANNOT_DELETE_PRIMARY_ADDRESS
  CODE_CANNOT_DELETE_PRIMARY_ADDRESS = 30101;
  // INVALID_LATITUDE_OR_LONGITUDE
  CODE_INVALID_LATITUDE_OR_LONGITUDE = 30102;
  // ADDRESS_IS_EMPTY
  CODE_ADDRESS_IS_EMPTY = 30103;
  // TOO_MANY_ADDRESS
  CODE_TOO_MANY_ADDRESSES = 30104;

  //book online client error code start 40000 end 50000
  // BOOK_ONLINE_NAME_INVALID
  CODE_BOOK_ONLINE_NAME_INVALID = 40404;
  // BOOK_ONLINE_NOT_ENABLE
  CODE_BOOK_ONLINE_NOT_ENABLE = 40070;
  // AGREEMENT_NOT_CONFIRM
  CODE_AGREEMENT_NOT_CONFIRM = 40090;
  // SIGNATURE_IS_EMPTY
  CODE_SIGNATURE_IS_EMPTY = 40091;
  // ACCEPT_TYPE_NOW_ALLOWED
  CODE_ACCEPT_TYPE_NOW_ALLOWED = 40092;
  // CANCEL_POLICY_NOT_CONFIRMED
  CODE_CANCEL_POLICY_NOT_CONFIRMED = 40093;
  // CUSTOMER_NOT_FOUND_FOR_OB
  CODE_CUSTOMER_NOT_FOUND_FOR_OB = 40094;
  // STRIPE_CARD_TOKEN_IS_EMPTY
  CODE_STRIPE_CARD_TOKEN_IS_EMPTY = 40095;
  // APPOINTMENT_TIME_IS_NOT_AVAILABLE
  CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE = 40096;
  // APPOINTMENT_CANCELED_INVOICE_INVALID
  CODE_APPOINTMENT_CANCELED_INVOICE_INVALID = 40097;
  // The appointment time submitted by ob is locked
  CODE_APPOINTMENT_TIME_LOCKED = 40098;
  // The OB's deposit must be paid first
  CODE_OB_DEPOSIT_NOT_PAID = 40099;
  // Business custom ob domain is invalid
  CODE_BOOK_ONLINE_DOMAIN_INVALID = 40100;
  // Business doesn't publish ob website
  CODE_BOOK_ONLINE_SITE_NOT_FOUND = 40101;

  //moego-grooming error code start 50000 end 60000
  // SERVICE_CATEGORY_NAME_IS_EXIST
  CODE_SERVICE_CATEGORY_NAME_IS_EXIST = 51001;
  // SERVICE_NAME_IS_EXIST
  CODE_SERVICE_NAME_IS_EXIST = 51002;
  // SERVICE_CATEGORY_NOT_FOUND
  CODE_SERVICE_CATEGORY_NOT_FOUND = 51003;
  // INVALID_VALUE_TYPE
  CODE_INVALID_VALUE_TYPE = 51004;
  // APPLY_PACKAGE_CHANGED
  CODE_APPLY_PACKAGE_CHANGED = 51005;
  // INVOICE_INVALID_STATUS
  CODE_INVOICE_INVALID_STATUS = 51006;
  // SERVICE_NOT_FOUND
  CODE_SERVICE_NOT_FOUND = 51007;
  // APPOINTMENT_INVALID_STATUS
  CODE_APPOINTMENT_INVALID_STATUS = 51008;
  // SERVICE_HAVE_BINDING
  CODE_SERVICE_HAVE_BINDING = 51009;
  // NO_AVAILABLE_SERVICE_SELECTED
  CODE_NO_AVAILABLE_SERVICE_SELECTED = 51010;
  // NO_CUSTOMER_ADDRESS
  CODE_NO_CUSTOMER_ADDRESS = 51011;
  // TOO_MANY_DAYS_TO_QUERY
  CODE_TOO_MANY_DAYS_TO_QUERY = 51012;
  // NO_AVAILABLE_STAFF_WHEN_SUBMIT
  CODE_NO_AVAILABLE_STAFF_WHEN_SUBMIT = 51013;
  // CREDIT_CARD_NEED_OPEN
  CODE_CREDIT_CARD_NEED_OPEN = 51014;
  // GOOGLE_INVALID_ADDRESS
  CODE_GOOGLE_INVALID_ADDRESS = 51015;
  // TASK_FAILURE_GENERAL
  CODE_TASK_FAILURE_GENERAL = 51016;
  // TASK_MESSAGE_RESET_FAILURE
  CODE_TASK_MESSAGE_RESET_FAILURE = 51017;
  // TOO_MANY_APPOINTMENTS
  CODE_TOO_MANY_APPOINTMENTS = 51018;
  // SERVICE_CATEGORY_NAME_IS_TOO_LONG
  CODE_SERVICE_CATEGORY_NAME_IS_TOO_LONG = 51019;
  // REPEAT_IS_NOT_FOUND
  CODE_REPEAT_IS_NOT_FOUND = 51020;
  // QUICKBOOKS_DEV_ERROR
  CODE_QUICKBOOKS_DEV_ERROR = 51021;
  // QUICKBOOKS_OAUTH_ERROR
  CODE_QUICKBOOKS_OAUTH_ERROR = 51022;
  // QUICKBOOKS_REFRESH_TOKEN_ERROR
  CODE_QUICKBOOKS_REFRESH_TOKEN_ERROR = 51023;
  // QUICKBOOKS_UNEXPECTED_EXCEPTION
  CODE_QUICKBOOKS_UNEXPECTED_EXCEPTION = 51024;
  // QUICKBOOKS_SETTING_ERROR
  CODE_QUICKBOOKS_SETTING_ERROR = 51025;
  // QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS
  CODE_QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS = 51026;
  // QUICKBOOKS_DATA_DUPLICATE_EMAIL_FORMAT_ERROR
  CODE_QUICKBOOKS_DATA_DUPLICATE_EMAIL_FORMAT_ERROR = 51027;
  //google calendar
  // GOOGLE_OAUTH_CHECK_ERROR
  CODE_GOOGLE_OAUTH_CHECK_ERROR = 51028;
  // GOOGLE_OAUTH_ERROR
  CODE_GOOGLE_OAUTH_ERROR = 51029;
  // GOOGLE_OAUTH_IS_USED
  CODE_GOOGLE_OAUTH_IS_USED = 51030;
  // UPCOMING_ID_ERROR
  CODE_UPCOMING_ID_ERROR = 51031;
  // CREATE_ERROR
  CODE_CREATE_ERROR = 51032;
  // WATCH_EVENT_ERROR
  CODE_WATCH_EVENT_ERROR = 51033;
  // GET_EVENT_ERROR
  CODE_GET_EVENT_ERROR = 51034;
  // SS_FOR_REPEAT_FREQUENCY_PARAMS_ERROR
  CODE_SS_FOR_REPEAT_FREQUENCY_PARAMS_ERROR = 51035;
  // FREE_SS_FOR_REPEAT_TIMES_USED_OUT
  CODE_FREE_SS_FOR_REPEAT_TIMES_USED_OUT = 51036;
  // SS_FOR_REPEAT_TIMES_OVER_LIMIT
  CODE_SS_FOR_REPEAT_TIMES_OVER_LIMIT = 51037;
  // INVOICE_SET_TIPS_ERROR
  CODE_INVOICE_SET_TIPS_ERROR = 51038;
  // CODE_QUESTION_NAME_IS_EXIST
  CODE_QUESTION_NAME_IS_EXIST = 51039;
  // INVOICE_NOT_FOUND
  CODE_INVOICE_NOT_FOUND = 51040;
  // PET_SIZE_IN_USED
  CODE_PET_SIZE_IN_USED = 51041;
  // CODE_GROOMING_REPORT_NOT_AVAILABLE
  CODE_GROOMING_REPORT_NOT_AVAILABLE = 51042;
  // CODE_GROOMING_REPORT_BOOK_AGAIN_EXPIRED
  CODE_GROOMING_REPORT_BOOK_AGAIN_EXPIRED = 51043;
  // CODE_SERVICE_CHARGE_NOT_AVAILABLE
  CODE_SERVICE_CHARGE_NOT_AVAILABLE = 51044;
  // CODE_EMPTY_PET_LIST
  CODE_EMPTY_PET_LIST = 51045;
  // CODE_EMPTY_SERVICE_LIST
  CODE_EMPTY_SERVICE_LIST = 51046;
  // CODE_APPOINTMENT_TRACKING_NOT_FOUND
  CODE_APPOINTMENT_TRACKING_NOT_FOUND = 51047;
  // CODE_APPOINTMENT_TRACKING_SHARED_BY_OTHER
  CODE_APPOINTMENT_TRACKING_SHARED_BY_OTHER = 51048;
  // CODE_APPOINTMENT_TRACKING_STAFF_HAS_ANOTHER_SHARING_APPOINTMENT
  CODE_APPOINTMENT_TRACKING_STAFF_HAS_ANOTHER_SHARING_APPOINTMENT = 51049;
  // CODE_APPOINTMENT_TRACKING_STAFF_LOCATION_STATUS_CHANGE_NOT_ALLOWED
  CODE_APPOINTMENT_TRACKING_STAFF_LOCATION_STATUS_CHANGE_NOT_ALLOWED = 51050;

  //moego-retail error code start 60000 end 70000
  // BUSINESS_IS_EMPTY
  CODE_BUSINESS_IS_EMPTY = 60000;
  // SUPPLIER_NOT_FOUND
  CODE_SUPPLIER_NOT_FOUND = 60001;
  // CATEGORY_NOT_FOUND
  CODE_CATEGORY_NOT_FOUND = 60002;
  // PACKAGE_NOT_FOUND
  CODE_PACKAGE_NOT_FOUND = 60003;
  // PRODUCT_NOT_FOUND
  CODE_PRODUCT_NOT_FOUND = 60004;
  // CART_NOT_FOUND
  CODE_CART_NOT_FOUND = 60005;
  // NAME_IS_EXIST
  CODE_NAME_IS_EXIST = 60006;
  // SKU_IS_EXIST
  CODE_SKU_IS_EXIST = 60007;
  // NOT_PAID
  CODE_NOT_PAID = 60008;
  // INVALID_CART_ID
  CODE_INVALID_CART_ID = 60009;
  // CART_ALREADY_PROCESSING
  CODE_CART_ALREADY_PROCESSING = 60010;
  // CART_ALREADY_COMPLETED
  CODE_CART_ALREADY_COMPLETED = 60011;
  // INVALID_DISCOUNT_TYPE
  CODE_INVALID_DISCOUNT_TYPE = 60012;
  // NAME_IS_EMPTY
  CODE_NAME_IS_EMPTY = 60013;
  // STAFF_IS_EMPTY
  CODE_STAFF_IS_EMPTY = 60014;
  // BARCODE_IS_EXIST(60015, "Barcode is already in use")
  CODE_BARCODE_IS_EXIST = 60015;
  // INVALID_CART_TYPE
  CODE_INVALID_CART_SALE_TYPE = 60016;

  //payment error code start 70000 end 80000
  // PAY_AMOUNT_INVALID
  CODE_PAY_AMOUNT_INVALID = 70001;
  // PAY_MODULE_EMPTY
  CODE_PAY_MODULE_EMPTY = 70002;
  // PAY_INVOICE_ID_EMPTY
  CODE_PAY_INVOICE_ID_EMPTY = 70003;
  // PAY_METHOD_INVALID
  CODE_PAY_METHOD_INVALID = 70004;
  // PAY_DATA_INVALID
  CODE_PAY_DATA_INVALID = 70005;
  // INVALID_CHECK_NUMBER
  CODE_INVALID_CHECK_NUMBER = 70006;
  // REFUND_AMOUNT_INVALID
  CODE_REFUND_AMOUNT_INVALID = 70007;
  // STRIPE_INTENT_NOT_FOUND
  CODE_STRIPE_INTENT_NOT_FOUND = 70008;
  // PAYMENT_NOT_FOUND
  CODE_PAYMENT_NOT_FOUND = 70009;
  // STRIPE_ACCOUNT_NOT_FOUND
  CODE_STRIPE_ACCOUNT_NOT_FOUND = 70011;
  // SUBSCRIPTION_EXPIRATION
  CODE_SUBSCRIPTION_EXPIRATION = 70012;
  // SUBSCRIPTION_NOT_EXIST
  CODE_SUBSCRIPTION_NOT_EXIST = 70013;
  // COMPANY_STATE_NOT_VALID
  CODE_COMPANY_STATE_NOT_VALID = 70014;
  // STRIPE_ACCOUNT_ERROR
  CODE_STRIPE_ACCOUNT_ERROR = 70015;
  // REFUSE_TO_STRIPE_SET_UP
  CODE_REFUSE_TO_STRIPE_SET_UP = 70016;
  // SUBSCRIPTION_NOT_VALID
  CODE_SUBSCRIPTION_NOT_VALID = 70017;
  // REFUND_TIME_OUT
  CODE_REFUND_TIME_OUT = 70018;
  // STRIPE_CARD_EXCEPTION
  CODE_STRIPE_CARD_EXCEPTION = 70019;
  //READER_STATUS_ERROR
  CODE_TERMINAL_READER_IN_PROGRESSING = 70020;
  //CARD PRE AUTH FAILED
  CODE_PRE_AUTHENTICATION_FAILED = 70021;
  //PREAUTH OPENED
  CODE_PRE_AUTHENTICATION_DUPLICATED = 70022;
  //PREAUTH ERROR STATUS
  CODE_PRE_AUTHENTICATION_STATUS_ERROR = 70023;
  // DEPOSIT_HAS_PAID_EXCEPTION(70024, "This invoice has already paid deposit")
  CODE_DEPOSIT_HAS_PAID_EXCEPTION = 70024;
  // A2P_NOW_ALLOW_EDITING(70025, "The current state does not allow editing of the data")
  CODE_A2P_NOW_ALLOW_EDITING = 70025;
  // A2P_CHARGE_FAILED(70026, "Payment failed, please check your credit card.")
  CODE_A2P_CHARGE_FAILED = 70026;
  //以下是旧的错误码，同步至新的错误码，代码里还是使用旧的错误码，后续可以替换成新的，前端如果有用到则需要改成新的
  // PAYMENT_STATUS_EXCEPTION(70020, "Payment status has been updated, please refresh.")
  CODE_PAYMENT_STATUS_EXCEPTION = 70027;
  // SUBSCRIPTION_UPDATE_FAILED(70021, "Cannot Update subscription for failed payments.")
  CODE_SUBSCRIPTION_UPDATE_FAILED = 70028;
  // STRIPE_COMPANY_CUSTOMER_NOT_FOUND(70022, "Stripe company customer is not found")
  CODE_STRIPE_COMPANY_CUSTOMER_NOT_FOUND = 70029;
  // STRIPE_UPDATE_CUSTOMER_EXCEPTION(70023, "Update Stripe customer error")
  CODE_STRIPE_UPDATE_CUSTOMER_EXCEPTION = 70030;
  // STRIPE_CREATE_HARDWARE_INVOICE_EXCEPTION(70024, "Create hardware invoice error")
  CODE_STRIPE_CREATE_HARDWARE_INVOICE_EXCEPTION = 70031;
  // STRIPE_CREATE_HARDWARE_ORDER_EXCEPTION(70025, "Create hardware order error")
  CODE_STRIPE_CREATE_HARDWARE_ORDER_EXCEPTION = 70032;
  //  PLATFORM_CARE_AGREEMENT_NOT_FOUND(70033, "The agreement is not found")
  CODE_PLATFORM_CARE_AGREEMENT_NOT_FOUND = 70033;
  // PLATFORM_CARE_RECORD_NOT_FOUND(70034, "The record is not found")
  CODE_PLATFORM_CARE_RECORD_NOT_FOUND = 70034;
  // PLATFORM_CARE_RECORD_NOT_FOUND_BY_EMAIL(70035, "This email cannot be found in the records.")
  CODE_PLATFORM_CARE_RECORD_NOT_FOUND_BY_EMAIL = 70035;
  // PLATFORM_CARE_EMAIL_ADDRESS_MISMATCH(70036, "The signing email is inconsistent with the registered email")
  CODE_PLATFORM_CARE_EMAIL_ADDRESS_MISMATCH = 70036;
  // PLATFORM_CARE_ASSIGN_AGREEMENT_ERROR(70037, "The agreement sign failed")
  CODE_PLATFORM_CARE_ASSIGN_AGREEMENT_FAILED = 70037;
  // PLATFORM_CARE_RECORD_IS_EXIST(70038, "The record is exist")
  CODE_PLATFORM_CARE_RECORD_IS_EXIST = 70038;
  // ENTERPRISE_STRIPE_CUSTOMER_IS_EXIST
  CODE_ENTERPRISE_STRIPE_CUSTOMER_IS_EXIST = 70039;
  // COMPANY_STRIPE_CUSTOMER_IS_EXIST
  CODE_COMPANY_STRIPE_CUSTOMER_IS_EXIST = 70040;
  //twilio fine charge fail
  CODE_TWILIO_FINE_CHARGE_FAIL = 70041;
  // moego pay is not set up
  CODE_MOEGO_PAY_NOT_SET_UP = 70042;
  // Not found refund.
  CODE_REFUND_NOT_FOUND = 70043;

  //moego-message error code start 80000 end 90000
  // MESSAGE_AUTO_TEMPLATE_NOT_CONFIG
  CODE_MESSAGE_AUTO_TEMPLATE_NOT_CONFIG = 80100;
  // MESSAGE_SEND_PHONE_IS_NULL
  CODE_MESSAGE_SEND_PHONE_IS_NULL = 80101;
  // MESSAGE_SEND_EMAIL_IS_NULL
  CODE_MESSAGE_SEND_EMAIL_IS_NULL = 80102;
  // MESSAGE_SEND_REVIEW_IS_NULL
  CODE_MESSAGE_SEND_REVIEW_IS_NULL = 80103;
  // MESSAGE_SEND_PHONE_FAILED
  CODE_MESSAGE_SEND_PHONE_FAILED = 80104;
  // MESSAGE_SEND_EMAIL_FAILED
  CODE_MESSAGE_SEND_EMAIL_FAILED = 80105;
  // MESSAGE_CANNOT_DELETE_OTHER_STAFF_MESSAGE
  CODE_MESSAGE_CANNOT_DELETE_OTHER_STAFF_MESSAGE = 80106;
  // CUSTOMER_PHONE_NUMBER_ERROR
  CODE_CUSTOMER_PHONE_NUMBER_ERROR = 80107;
  // NOTIFICATION_TYPE_NOT_FOUND
  CODE_NOTIFICATION_TYPE_NOT_FOUND = 80108;
  // MESSAGE_AMOUNT_RUN_OUT
  CODE_MESSAGE_AMOUNT_RUN_OUT = 80109;
  // CODE_SEND_LIMIT
  CODE_CODE_SEND_LIMIT = 80110;
  // FAIL_TO_SEND_CODE
  CODE_FAIL_TO_SEND_CODE = 80111;
  // BUSINESS_TWILIO_MESSAGE_NOE_MORE
  CODE_BUSINESS_TWILIO_MESSAGE_NOE_MORE = 80112;
  // MESSAGE_CONTROL_NO_RECORD
  CODE_MESSAGE_CONTROL_NO_RECORD = 80113;
  // MESSAGE_SEND_FAILED
  CODE_MESSAGE_SEND_FAILED = 80114;
  // MESSAGE_SEND_FAILED_BY_SWITCH_CLOSE
  CODE_MESSAGE_SEND_FAILED_BY_SWITCH_CLOSE = 80115;
  // MESSAGE_EMAIL_RECEIVE_NOT_FOUND_BUSINESS
  CODE_MESSAGE_EMAIL_RECEIVE_NOT_FOUND_BUSINESS = 80116;
  // NOT_ALLOW_UPDATE_TWILIO_CALL_FORWARDING
  CODE_NOT_ALLOW_UPDATE_TWILIO_CALL_FORWARDING = 80117;
  // TWILIO_QUERY_ERROR
  CODE_TWILIO_QUERY_ERROR = 80118;
  // invalid_email_event_type
  CODE_INVALID_EMAIL_EVENT_TYPE = 80119;
  // invalid_email_status
  CODE_INVALID_EMAIL_STATUS = 80120;
  // email_not_found
  CODE_EMAIL_NOT_FOUND = 80121;
  // grooming report send failed
  CODE_GROOMING_REPORT_SEND_FAILED = 80122;
  // not allow submit review
  CODE_NOT_ALLOW_SUBMIT_REVIEW = 80123;
  //a2p data forbidden edit
  CODE_ATP_DATA_FORBIDDEN_EDIT = 80124;
  //a2p phone number type error
  CODE_ATP_PN_TYPE_ERROR = 80125;
  //a2p submit lock
  CODE_ATP_SUBMIT_RETRY = 80126;
  //a2p atp brand otp retry
  CODE_ATP_BRAND_OTP_RETRY = 80127;
  //message not found
  CODE_MESSAGE_NOT_FOUND = 80128;
  // Phone number cannot be formatted
  CODE_PHONE_NUMBER_CANNOT_FORMATTED = 80129;
  // daily report send failed
  CODE_DAILY_REPORT_SEND_FAILED = 80130;

  // client app error code start 90000 end 94999
  // 90000-94999 for client app error
  // link business not found
  CODE_LINK_BUSINESS_NOT_FOUND = 90000;
  // invitation code not found
  CODE_INVITATION_CODE_NOT_FOUND = 90001;
  // link phone number not found
  CODE_LINK_PHONE_NUMBER_NOT_FOUND = 90002;
  // No linked customers found
  CODE_NO_LINKED_CUSTOMERS_FOUND = 90003;
  // Linked too many customers
  CODE_LINKED_TOO_MANY_CUSTOMERS = 90004;
  // branded app not found
  CODE_BRANDED_APP_NOT_FOUND = 90005;
  // Cancellations are not allowed
  CODE_CANCELLATION_NOT_ALLOWED = 90006;
  // Reschedule are not allowed
  CODE_RESCHEDULE_NOT_ALLOWED = 90007;

  // fintech app error code start 95000 end 99999
  // force update
  CODE_FINTECH_BUSINESS_APP_UPDATE_FORCE = 95000;
  // closable update
  CODE_FINTECH_BUSINESS_APP_UPDATE_CLOSABLE = 95001;

  //moego-account error code start 100000 end 110000
  // 100000 ~ 100999 for account error
  // account not exist
  CODE_ACCOUNT_NOT_EXIST = 100000;
  // password too weak
  CODE_PASSWORD_TOO_WEAK = 100001;
  // email conflict
  CODE_EMAIL_CONFLICT = 100002;
  // phone number conflict
  CODE_PHONE_NUMBER_CONFLICT = 100003;
  // account or password error
  CODE_ACCOUNT_OR_PASSWORD_ERROR = 100004;
  // old password error
  CODE_OLD_PASSWORD_ERROR = 100005;
  // account already recovered
  CODE_ACCOUNT_ALREADY_RECOVERED = 100006;
  // verification code not match
  CODE_VERIFICATION_CODE_NOT_MATCH = 100007;
  // account frozen
  CODE_ACCOUNT_FROZEN = 100008;

  // 101000 ~ 101999 for session error
  // session not exist
  CODE_SESSION_NOT_EXIST = 101000;
  // session expired
  CODE_SESSION_EXPIRED = 101001;
  // session token invalid
  CODE_SESSION_TOKEN_INVALID = 101002;

  // 102000 ~ 102999 for account association error
  // platform account already associated
  CODE_PLATFORM_ACCOUNT_ALREADY_ASSOCIATED = 102000;

  // ai assistant error code start 120000 end 130000
  // conversation not exist
  CODE_CONVERSATION_NOT_EXIST = 120000;
  // conversation business not match (conversation not belong to business)
  CODE_CONVERSATION_BUSINESS_NOT_MATCH = 120001;
  // conversation closed
  CODE_CONVERSATION_CLOSED = 120003;
  // conversation failed to reply
  CODE_CONVERSATION_FAILED_TO_REPLY = 120004;
  // conversation balance not enough
  CODE_CONVERSATION_BALANCE_NOT_ENOUGH = 120005;
  // conversation question not exist
  CODE_CONVERSATION_QUESTION_NOT_EXIST = 120006;
  // conversation question not match (question not belong to conversation)
  CODE_CONVERSATION_QUESTION_NOT_MATCH = 120007;
  // conversation question excessive
  CODE_CONVERSATION_QUESTION_EXCESSIVE = 120008;

  // google recaptcha error code start 130000 end 140000
  // google recaptcha v2 version verification failed
  CODE_GOOGLE_RECAPTCHA_INVALID = 130000;
  // google recaptcha v3 version score is less than threshold
  CODE_GOOGLE_RECAPTCHA_THRESHOLD_NOT_REACHED = 130001;
  // google recaptcha action not found
  CODE_GOOGLE_RECAPTCHA_ACTION_NOT_FOUND = 130002;
  // send verification code limited
  CODE_VERIFICATION_CODE_SENT_COUNT_LIMITED = 130003;
  // send verification code interval not reached
  CODE_VERIFICATION_CODE_SEND_INTERVAL_NOT_REACHED = 130004;
  // invalid verification code
  CODE_INVALID_VERIFICATION_CODE = 130005;
  // verification code expired
  CODE_VERIFICATION_CODE_EXPIRED = 130006;

  // moego-file error code start 140000 end 150000
  // file record does not exist in db
  CODE_FILE_RECORD_NOT_EXIST = 140000;
  // file object does not exist in s3
  CODE_FILE_OBJECT_NOT_EXIST = 140001;
  // the file extension is not allowed
  CODE_FILE_EXT_FORBIDDEN = 140002;

  // moego-permission error code start 150000 end 160000
  // role name already exists
  CODE_ROLE_NAME_ALREADY_EXISTS = 150000;
  // role not found, maybe be deleted or company id not match
  CODE_ROLE_NOT_FOUND = 150001;

  // moego-offering error code start 160000 end 170000
  // lodging
  // operation can't be performed on lodging type in use
  CODE_LODGING_TYPE_IN_USE = 160000;
  // lodging type not found
  CODE_LODGING_TYPE_NOT_FOUND = 160001;
  // operation can't be performed on lodging unit in use
  CODE_LODGING_UNIT_IN_USE = 160002;

  // moego-finance error code start 170000 end 180000
  // finance-common 170000-171000
  // The requested API is internal API and not allowed for current user.
  CODE_FINANCE_NO_ACCESS_INTERNAL_API = 170000;
  // capital 171000-172000
  // The requested offer is not found.
  CODE_CAPITAL_OFFER_NOT_FOUND = 171000;
  // The current user has no permission to access the requested offer.
  CODE_CAPITAL_OFFER_NO_ACCESS = 171001;
  // The requested offer is in a wrong status for the operation to perform.
  CODE_CAPITAL_ILLEGAL_OFFER_STATUS = 171002;
  // The requested channel account is not found.
  CODE_CAPITAL_CHANNEL_ACCOUNT_NOT_FOUND = 171003;
  // The database error occurred when operating the capital data.
  CODE_CAPITAL_DB_ERROR = 171004;
  // The capital lock failed.
  CODE_CAPITAL_GET_LOCK_FAILED = 171005;
  // The capital is locked by other goroutine.
  CODE_CAPITAL_LOCKED_BY_OTHER = 171006;
  // The capital is fully paid with remaining amount.
  CODE_CAPITAL_FULLY_PAID_WITH_REMAINING = 171007;
  // The transaction type is unknown.
  CODE_CAPITAL_UNKNOWN_TRANSACTION_TYPE = 171008;
  // The capital interval paid amount mismatch.
  CODE_CAPITAL_INTERVAL_PAID_AMOUNT_MISMATCH = 171009;
  // The capital offer remaining amount mismatch.
  CODE_CAPITAL_OFFER_REMAINING_AMOUNT_MISMATCH = 171010;
  // The capital transaction is not found.
  CODE_CAPITAL_TRANSACTION_NOT_FOUND = 171011;
  // The capital channel status is unknown.
  CODE_CAPITAL_CHANNEL_STATUS_UNKNOWN = 171012;
  // The capital stripe API error.
  CODE_CAPITAL_STRIPE_API_ERROR = 171013;
  // The capital cache error.
  CODE_CAPITAL_CACHE_ERROR = 171014;
  // The capital illegal status transition.
  CODE_CAPITAL_ILLEGAL_STATUS_TRANSITION = 171015;
  // The capital network error.
  CODE_CAPITAL_NETWORK_ERROR = 171016;
  // The capital unmarshal error.
  CODE_CAPITAL_UNMARSHAL_ERROR = 171017;
  // The capital unknown entity type.
  CODE_CAPITAL_UNKNOWN_ENTITY_TYPE = 171018;
  // The capital Kanmon API error.
  CODE_CAPITAL_KANMON_API_ERROR = 171019;
  // The capital channel status is unknown.
  CODE_CAPITAL_CHANNEL_UNKNOWN = 171020;
  // The capital processing amount is greater than remaining amount.
  CODE_CAPITAL_PROCESSING_AMOUNT_GREATER_THAN_REMAINING = 171021;
  // Failed to deliver message.
  CODE_CAPITAL_FAILED_TO_DELIVER_MESSAGE = 171022;

  // finance-gw 172000-173000
  // Invalid Stripe webhook payload (wrong signature, wrong json, etc.)
  CODE_FINANCE_GW_INVALID_STRIPE_WEBHOOK_PAYLOAD = 172000;
  // The downstream handler failed to handle the request
  CODE_FINANCE_GW_DOWNSTREAM_ERROR = 172001;
  // Invalid Kanmon webhook payload (wrong signature, wrong json, etc.)
  CODE_FINANCE_GW_INVALID_KANMON_WEBHOOK_PAYLOAD = 172002;
  // Unknown offer type
  CODE_FINANCE_GW_UNKNOWN_OFFER_TYPE = 172003;
  // Unknown account event type
  CODE_FINANCE_GW_UNKNOWN_ACCOUNT_EVENT_TYPE = 172004;
  // Unknown offer event type
  CODE_FINANCE_GW_UNKNOWN_OFFER_EVENT_TYPE = 172005;
  // Unknown offer transaction event type
  CODE_FINANCE_GW_UNKNOWN_TRANSACTION_EVENT_TYPE = 172006;
  // Invalid kanmon webhook secret
  CODE_FINANCE_GW_INVALID_KANMON_WEBHOOK_SECRET = 172007;
  // Signature verification failed
  CODE_FINANCE_GW_SIGNATURE_VERIFICATION_FAILED = 172008;
  // Unmarshal error
  CODE_FINANCE_GW_PAYLOAD_UNMARSHAL_ERROR = 172009;
  // Database error
  CODE_FINANCE_GW_DB_ERROR = 172010;

  // moego-rest-api error code start 180000 end 190000
  // IO failure
  CODE_REST_IO = 180000;
  // The invoked downstream returns error
  CODE_REST_DOWNSTREAM_ERROR = 180001;

  // moego-svc-finance-tools error code start 190000 end 200000
  // Cash Drawer
  // The time range is invalid (overlapping, end before start, e.g.)
  CODE_FINANCE_TOOLS_INVALID_TIME_RANGE = 190000;
  // The given payments total does not match the realtime calculated payments total
  CODE_FINANCE_TOOLS_PAYMENTS_TOTAL_UNMATCHED = 190001;
  // The given adjustments total does not match the realtime calculated adjustments total
  CODE_FINANCE_TOOLS_ADJUSTMENTS_TOTAL_UNMATCHED = 190002;

  // moego-svc-split-payment error code start 200000 end 210000
  // Split payment acceptance failed
  CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED = 200000;
  // Split payment reversal acceptance failed
  CODE_SPLIT_PAYMENT_REVERSAL_ACCEPTANCE_FAILED = 200001;

  // moego-svc-accounting error code start 210000 end 220000
  // accounting unknown error
  CODE_ACCOUNTING_UNKNOWN_ERROR = 210000;
  // 数据库访问异常
  CODE_ACCOUNTING_DATA_ACCESS_ERROR = 210001;
  // 调用第三方异常
  CODE_ACCOUNTING_THIRD_PARTY_API_ERROR = 210002;
  // 调用内部二方RPC异常
  CODE_ACCOUNTING_RPC_CALL_ERROR = 210003;
  // 数据同步器获取不到异常
  CODE_ACCOUNTING_DATA_SYNCER_NOT_FOUND_ERROR = 210004;
  // 内部业务异常
  CODE_ACCOUNTING_INTERNAL_BIZ_ERROR = 210005;
  // 未订阅，无法访问
  CODE_ACCOUNTING_UNSUBSCRIBED_ERROR = 210006;

  // TODO(chi, Kuroko, Perqin, P1): 排序、分组错误码
  // moego-svc-payment error code start 220000 end 230000
  // payment unknown error
  CODE_PAYMENT_UNKNOWN_ERROR = 220000;
  // 数据库访问异常
  CODE_PAYMENT_DATA_ACCESS_ERROR = 220001;
  // 内部业务异常
  CODE_PAYMENT_INTERNAL_ERROR = 220002;
  // 错误渠道
  CODE_PAYMENT_INVALID_CHANNEL = 220003;
  // 错误 Onboard 校验状态
  CODE_PAYMENT_INVALID_VERIFICATION_STATUS = 220004;
  // Company 下没有 location，理论上不能发生，除非是异常数据
  CODE_PAYMENT_NO_LOCATION = 220005;
  // 获取 company 失败
  CODE_PAYMENT_GET_COMPANY_FAILED = 220006;
  // 获取 channel account 失败
  CODE_PAYMENT_GET_CHANNEL_ACCOUNT_FAILED = 220007;
  // Adyen 获取 account holder 失败
  CODE_PAYMENT_ADYEN_GET_ACCOUNT_HOLDER_FAILED = 220008;
  // 获取 businesses 失败
  CODE_PAYMENT_LIST_BUSINESSES_FAILED = 220009;
  // Adyen 创建 legal entity 失败
  CODE_PAYMENT_ADYEN_CREATE_LEGAL_ENTITY_FAILED = 220010;
  // Adyen 创建 account holder 失败
  CODE_PAYMENT_ADYEN_CREATE_ACCOUNT_HOLDER_FAILED = 220011;
  // Adyen 创建 store 失败
  CODE_PAYMENT_ADYEN_CREATE_STORE_FAILED = 220012;
  // Adyen 创建 balance account 失败
  CODE_PAYMENT_ADYEN_CREATE_BALANCE_ACCOUNT_FAILED = 220013;
  // 创建 channel account 失败
  CODE_PAYMENT_CREATE_CHANNEL_ACCOUNT_FAILED = 220014;
  // 更新 channel account 失败
  CODE_PAYMENT_UPDATE_CHANNEL_ACCOUNT_FAILED = 220015;
  // Onboard 需要的预配置信息不合法
  CODE_PAYMENT_ONBOARD_INVALID_PRE_CONFIG = 220016;
  // Adyen 创建 business line 失败
  CODE_PAYMENT_ADYEN_CREATE_BUSINESS_LINE_FAILED = 220017;
  // Adyen 创建 onboard link 失败
  CODE_PAYMENT_ADYEN_CREATE_ONBOARD_LINK_FAILED = 220018;
  // 状态非法
  CODE_PAYMENT_STATUS_INVALID = 220019;
  // 调用第三方异常
  CODE_PAYMENT_THIRD_PARTY_API_ERROR = 220020;
  // 调用内部二方RPC异常
  CODE_PAYMENT_RPC_CALL_ERROR = 220021;
  // 传入的 reader 错误（例如 reader 不存在）
  CODE_PAYMENT_INVALID_READER = 220022;
  // 调用 Adyen Terminal API 失败
  CODE_PAYMENT_ADYEN_TERMINAL_REQUEST_ERROR = 220023;
  // Adyen 获取 store 失败
  CODE_PAYMENT_ADYEN_GET_STORE_FAILED = 220024;
  // Adyen 获取 balance account 失败
  CODE_PAYMENT_ADYEN_GET_BALANCE_ACCOUNT_FAILED = 220025;
  // 未找到对应的 MoeGo user account
  CODE_PAYMENT_GET_ACCOUNT_FAILED = 220026;
  // Adyen 给 stores 添加 payment methods 失败
  CODE_PAYMENT_ADYEN_ADD_PAYMENT_METHODS_FAILED = 220027;
  // Onboard 已经完成
  CODE_PAYMENT_ONBOARD_ALREADY_FINISHED = 220028;
  // Onboard 步骤非法
  CODE_PAYMENT_ADYEN_INVALID_ONBOARD_STEP = 220029;
  // Adyen 获取 legal entity 失败
  CODE_PAYMENT_ADYEN_GET_LEGAL_ENTITY_FAILED = 220030;
  // Adyen 获取 transfer instrument 失败
  CODE_PAYMENT_ADYEN_GET_TRANSFER_INSTRUMENT_FAILED = 220031;
  // 创建 payment setting 失败
  CODE_PAYMENT_CREATE_PAYMENT_SETTING_FAILED = 220032;
  // Adyen 获取 payment methods 失败
  CODE_PAYMENT_ADYEN_GET_PAYMENT_METHODS_FAILED = 220033;
  // 未找到对应的 channel account
  CODE_PAYMENT_CHANNEL_ACCOUNT_NOT_FOUND = 220034;
  // Adyen 调用参数不合法
  CODE_PAYMENT_ADYEN_INVALID_PARAMS = 220035;
  // Adyen Onboard 未完成
  CODE_PAYMENT_ONBOARD_UNFINISHED = 220036;

  // payout from 221000-222000
  // 未找到对应的 payout
  CODE_PAYMENT_PAYOUT_NOT_FOUND = 221001;
  // 获取 payout 失败
  CODE_PAYMENT_PAYOUT_GET_FAILED = 221002;
  // 创建 payout 失败
  CODE_PAYMENT_PAYOUT_CREATE_FAILED = 221003;

  // billing from 222000-223000
  // billing unknown error
  CODE_BILLING_UNKNOWN_ERROR = 222001;
  // 数据库访问异常
  CODE_BILLING_DATA_ACCESS_ERROR = 222002;
  // 内部业务异常
  CODE_BILLING_INTERNAL_ERROR = 222003;

  // moego-svc-order & moego-svc-order-v2 from 230000-240000
  // Deposit not paid.
  CODE_ORDER_DEPOSIT_NOT_PAID = 230000;
  // Deposit not enough.
  CODE_ORDER_INSUFFICIENT_DEPOSIT = 230001;
}
