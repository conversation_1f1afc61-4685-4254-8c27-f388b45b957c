syntax = "proto3";

package moego.models.google_partner.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/google_partner/v1;googlepartnerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.google_partner.v1";

// Google Reserve Integration Status
enum GoogleReserveIntegrationStatus {
  // status unspecified, aka. configured
  GOOGLE_RESERVE_INTEGRATION_STATUS_UNSPECIFIED = 0;
  // address unmatched status
  GOOGLE_RESERVE_INTEGRATION_STATUS_UNMATCHED = 1;
  // address matched status
  GOOGLE_RESERVE_INTEGRATION_STATUS_MATCHED = 2;
}
