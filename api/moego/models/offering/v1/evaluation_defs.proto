// @since 2024-03-12 11:33:59
// <AUTHOR> <z<PERSON><PERSON>@moego.pet>

syntax = "proto3";

package moego.models.offering.v1;

import "moego/models/offering/v1/evaluation_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// The Evaluation Definition
// Note:
// 1. a def message must end with Def
// 2. a def message could be used in both request and response
// 3. all the fields of a def message must be validated
// 4. a def message's semantics must be single, which means you
//    cannot share a single def message when anyone field is
//    different, including labels.
message EvaluationDef {
  // whether the evaluation is available for all business
  bool available_for_all_business = 1;
  // available business ids (if available_for_all_business is false)
  repeated int64 available_business_ids = 2;
  // service item types that require evaluation
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 3;
  // price
  optional double price = 4 [(validate.rules).double.gte = 0];
  // duration
  int32 duration = 5 [(validate.rules).int32.gt = 0];
  // color code
  string color_code = 6 [(validate.rules).string.pattern = "^#([A-Fa-f0-9]{6})$"];
  // name
  string name = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // is active
  bool is_active = 8;
  // whether the service is available for all lodging
  bool lodging_filter = 9;
  // available lodging ids(only if available_for_all_lodgings is false)
  repeated int64 customized_lodging_ids = 10;
  // description
  string description = 11 [(validate.rules).string = {max_len: 1500}];
  // name shown in ob flow
  optional string alias_for_online_booking = 12 [(validate.rules).string = {max_len: 100}];
  // is available for all staff. default is true
  optional bool is_all_staff = 13;
  // available staff ids(only if is_all_staff is false)
  repeated int64 allowed_staff_list = 14;
  // is allow staff auto assign. default is false
  optional bool allow_staff_auto_assign = 15;
  // is resettable
  optional bool is_resettable = 16;
  // reset interval days
  optional int32 reset_interval_days = 17;
  // Pet type breed filter
  optional PetBreedFilterConfig breed_filter_config = 18;
  // tax_id
  optional int64 tax_id = 20;
}

// The message for pet breed filter config
message PetBreedFilterConfig {
  // whether the service is available for all pet type & breed
  bool breed_filter = 1;
  // available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
  repeated PetBreedFilter filters = 2 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
  }];
}
