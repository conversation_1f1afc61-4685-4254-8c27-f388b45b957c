syntax = "proto3";

package moego.models.payment.v2;

import "moego/models/organization/v1/address_defs.proto";
import "moego/models/payment/v2/common_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v2";

// Def for Adyen pre-configuration
message AdyenPreConfigDef {
  // Individual legal entity.
  // 注意：Adyen 不允许我们直接 onboard individual，必须通过 sole proprietorship 方式 onboard individual。这里我们放开
  // individual 类型，但是商家在 onboard link 里应当选择 sole proprietorship 完成后续步骤。
  message Individual {
    // Given name (first name).
    string given_name = 1;
    // Family name (last name).
    string family_name = 2;
    // Residential address country alpha2 code.
    string residential_address_country = 3;
  }

  // Organization legal entity.
  message Organization {
    // Legal name
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: The "legal_name" is reasonable. --)
    string legal_name = 1;
    // Registered address country alpha2 code.
    string registered_address_country = 2;
  }

  // Sole proprietorship legal entity.
  // 根据 Adyen 的要求：Sole Proprietorship 的 main legal entity 也是 individual 类型，本质上是在 individual 上关联了一个
  // Sole Proprietorship 类型的 legal entity。因此这里入参调整为和 individual 一样。
  message SoleProprietorship {
    // Given name (first name).
    string given_name = 1;
    // Family name (last name).
    string family_name = 2;
    // Residential address country alpha2 code.
    string residential_address_country = 3;
  }

  // Phone number of this company.
  string phone_number = 1;
  // One of the legal entity type. Trust is not supported.
  oneof legal_entity {
    // Individual legal entity.
    Individual individual = 2;
    // Organization legal entity.
    Organization organization = 3;
    // Sole proprietorship legal entity.
    SoleProprietorship sole_proprietorship = 4;
  }
  // Additional configuration for business, keyed by business ID. These configurations override the business profile.
  map<int64, AdyenBusinessConfig> business_config = 5;
}

// Additional configuration for business.
message AdyenBusinessConfig {
  // Store address.
  optional moego.models.organization.v1.AddressDef store_address = 1;
  // Store phone number.
  optional string store_phone_number = 2;
}

// Invalid fields error reported by Adyen.
message AdyenInvalidFieldsError {
  // Invalid field.
  message InvalidField {
    // Message for detail.
    string message = 1;
    // Field name in a.b.c format.
    string name = 2;
    // Invalid value passed to Adyen.
    string value = 3;
  }

  // Invalid fields.
  repeated InvalidField invalid_fields = 1;
  // The type of the entity which causes the error
  moego.models.payment.v2.EntityType entity_type = 2;
  // The id of the entity which causes the error
  int64 entity_id = 3;
}
