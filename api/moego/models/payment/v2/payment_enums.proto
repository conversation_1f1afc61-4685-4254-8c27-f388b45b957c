syntax = "proto3";

package moego.models.payment.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v2";

// PaymentVersion 表示的是支付版本
enum PaymentVersion {
  // Unspecified
  PAYMENT_VERSION_UNSPECIFIED = 0;
  // V1
  V1 = 1;
  // V2
  V2 = 2;
}

// ExternalType 表示的是上层业务系统的类型
enum ExternalType {
  // Unspecified
  EXTERNAL_TYPE_UNSPECIFIED = 0;
  // Order Payment 对应order的order payment实体
  ORDER_PAYMENT = 1;
  // Refund Order Payment 对应 order 的 refund order payment 实体
  REFUND_ORDER_PAYMENT = 2;
  // Order 对应 order 域的 order 实体
  ORDER = 3;
}

// Reader 类型
enum ReaderType {
  // Unspecified
  READER_TYPE_UNSPECIFIED = 0;
  // Smart reader
  SMART_READER = 1;
  // BT reader
  BLUETOOTH_READER = 2;
  // Tap-to-pay reader
  TAP_TO_PAY_READER = 3;
}

// processing fee paid by 类型
enum ProcessingFeePaidBy {
  // Unspecified
  PROCESSING_FEE_PAID_BY_UNSPECIFIED = 0;
  // paid by business
  PAID_BY_BUSINESS = 1;
  // paid by client
  PAID_BY_CLIENT = 2;
}

// funding source 资金来源
enum FundingSource {
  // Unspecified
  FUNDING_SOURCE_UNSPECIFIED = 0;
  // credit
  CREDIT = 1;
  // debit
  DEBIT = 2;
}
