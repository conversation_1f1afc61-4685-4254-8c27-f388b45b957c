syntax = "proto3";

package moego.models.payment.v2;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/payment/v2/common_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v2";

// payout model
message PayoutModel {
  // PaymentStatus 支付状态
  enum PayoutStatus {
    // Unspecified
    STATUS_UNSPECIFIED = 0;
    // Created，这是初始化状态
    CREATED = 1;
    // Submitted，已向支付渠道提交请求,如stripe的in_transit
    SUBMITTED = 2;
    // PAID，支付成功，不见得是终态，按照渠道的说法，也可能从paid到failed :https://docs.stripe.com/api/payouts/object
    PAID = 3;
    // Cancelled，被取消或者reverse
    CANCELLED = 4;
    // Failed，支付失败，这是最终状态
    FAILED = 5;
  }

  // method mode
  enum MethodMode {
    // Unspecified
    MODE_UNSPECIFIED = 0;
    // 标准的payout
    STANDARD = 1;
    // Instant Payout
    INSTANT = 2;
    // Next Day Payout
    NEXT_DAY_PAYOUT = 3;
  }
  // destination type
  enum DestinationType {
    // Unspecified
    TYPE_UNSPECIFIED = 0;
    // bank account
    TYPE_BANK_ACCOUNT = 1;
    // debit card
    TYPE_DEBIT_CARD = 2;
  }
  // id
  int64 payout_id = 1;
  // amount
  google.type.Money amount = 2;
  // Date that you can expect the payout to arrive in the bank.
  // This factors in delays to account for weekends or bank holidays.
  google.protobuf.Timestamp arrival_date = 3;
  // Enums: PaymentVendor/Moego, used to be is_automatic
  string payout_trigger_source = 4;
  // Time at which the object was created. Measured in seconds since the Unix epoch.
  google.protobuf.Timestamp created = 5;

  // ID of the bank account or card the payout is sent to.
  string destination = 6;
  // Can be bank_account or card.
  DestinationType destination_type = 7;
  // Error code that provides a reason for a payout failure, if available.
  string failure_code = 8;
  // Message that provides the reason for a payout failure, if available.
  string failure_message = 9;
  // The method used to send this payout, which can be standard or instant.
  // https://docs.stripe.com/payouts/instant-payouts-banks
  MethodMode mode = 10;
  // Extra information about a payout that displays on the user’s bank statement.
  string statement_descriptor = 11;
  // Current status of the payout
  PayoutStatus status = 12;
  // entityType of the payout
  EntityType entity_type = 13;
  // entity id
  int64 entity_id = 14;
}

// payout summary
message PayoutSummary {
  //  gross sale
  google.type.Money gross_sale_amount = 1;
  //discount
  google.type.Money discount_amount = 2;
  // tax
  google.type.Money tax_amount = 3;
  //tips
  google.type.Money tips_amount = 4;
  //refunded
  google.type.Money refunded_amount = 5;
  //application_fee
  google.type.Money application_fee_amount = 6;
}
