syntax = "proto3";

package moego.models.reporting.v2;

import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/diagram_model.proto";
import "moego/models/reporting/v2/field_model.proto";
import "moego/models/reporting/v2/filter_model.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// A dashboard page
message DashboardPage {
  // Dashboard page types
  enum Tab {
    // Unspecified dashboard page tab
    TAB_UNSPECIFIED = 0;
    // The dashboard page tab is overview
    OVERVIEW = 1;
    // The dashboard page tab is sales
    SALES = 2;
    // The dashboard page tab is clients and pets
    PETS = 3;
    // The dashboard page tab is staff
    STAFF = 4;
    // The dashboard page tab is operation
    OPERATION = 5;
    // App Dashboard > Performance
    APP_PERSONAL_PERFORMANCE = 6;
    // App Dashboard > Overview
    APP_OVERVIEW = 7;
    // APP Dashboard > Staff performance
    APP_STAFF_PERFORMANCE = 8;
    // APP Dashboard > History(Dashboard 的 tab，用于控制 permission，Tab 下无关联的 diagram meta)
    APP_HISTORY = 9;
    // APP Dashboard > Report(Dashboard 的 tab，用于控制 permission，Tab 下无关联的 diagram meta)
    APP_REPORT = 10;
    // Payroll
    PAYROLL = 11;
    // Daily revenue
    DAILY_REVENUE = 12;
    // Leads
    LEADS = 13;
  }

  // The tab of the dashboard page
  Tab tab = 1;
  // The title of the dashboard page
  string title = 2;
  // The dashboard groups
  repeated DashBoardGroup groups = 3;
  // Current page's required permission code
  string permission_code = 4;
}

// A group of dashboards
message DashBoardGroup {
  // diagram id
  string diagram_id = 1;
  // The title of the dashboard group
  string title = 2;
  // description
  string description = 3;
  // The drill configuration
  DrillConfig drill_config = 4;
  // describe the dashboard group contents
  repeated DashBoardDiagram diagrams = 5;
  // Current diagram's required permission code
  string permission_code = 6;
}

// A dashboard diagram
message DashBoardDiagram {
  // The diagram id
  string diagram_id = 1;
  // The diagram type
  moego.models.reporting.v2.DiagramType diagram_type = 2;
  // table metas
  optional moego.models.reporting.v2.TableMeta table_meta = 3;
  // The drill configuration
  optional moego.models.reporting.v2.DrillConfig drill_config = 4;
  // Current diagram's permission code
  string permission_code = 5;
  // associated diagram id
  optional string associated_diagram_id = 6;
  // default group by field key
  repeated string default_group_by_field_keys = 7;
  // fields of the diagram
  repeated Field fields = 8;
  // filters of the diagram
  repeated Filter filters = 9;
}
