syntax = "proto3";

package moego.models.reporting.v2;

import "moego/models/reporting/v2/common_model.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// A field
message Field {
  // Field type enumeration
  enum Type {
    // Unspecified field type
    TYPE_UNSPECIFIED = 0;
    // A money field type
    MONEY = 1;
    // A text field type
    TEXT = 2;
    // A date field type
    DATE = 3;
    // A time field type
    TIME = 4;
    // A number field type
    NUMBER = 5;
    // A client field type
    CLIENT_NAME = 6;
    // An invoice field type
    INVOICE_ID = 7;
    // A booking field type
    BOOKING_ID = 8;
    // An avatar field type
    AVATAR_URL = 9;
    // A decimal number field type
    DECIMAL_NUMBER = 10;
    // A percentage field type
    PERCENTAGE = 11;
    // A duration field type, in minutes
    DURATION = 12;
    // With both date and time info
    DATETIME = 13;
    // Pet incident ID
    PET_INCIDENT_ID = 14;
    // color
    COLOR = 15;
    // Client ID
    CLIENT_ID = 16;
  }

  // The label of the field, which should be a string between 1 and 50 characters
  string label = 1;
  // The key of the field
  Type type = 2;
  // The key of the field
  string key = 3;
  // The drill configuration of the field
  optional DrillConfig drill_config = 4;
  // Whether the field is sortable
  bool sortable = 5;
  // Whether the field is removable
  bool removable = 6;
  // Whether the field can be grouped by
  bool group_by_enable = 7;
  // Whether the field is movable
  bool movable = 8;
  // Field trend type: BENEFIT, HARMFUL, NEUTRAL
  Trend trend = 9;
  // Current field's required permission code
  string permission_code = 10;
  // Current field's description/tooltips
  optional string description = 11;
  // linkage config to another diagram
  optional LinkageConfig linkage_config = 12;
}
