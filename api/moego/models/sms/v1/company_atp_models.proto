// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.sms.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/sms/v1;smspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.sms.v1";

// The company atp status model
message CompanyAtpStatusModel {
  //atp status: No required  Action Required   Pending   Verified   Failed
  string atp_status = 1;
  //atp resigter fail reason
  string fail_reason = 2;
  //atp setp  1 Need Init 2 Brand Review 3 Campaign Review
  sint32 step = 3;
  //is have ein
  bool with_ein = 4;
  //atp fee is charged
  double atp_fee = 5;
  //is paid
  bool is_paid = 6;
  //reserved field is need support
  bool need_support = 7;
}

//company atp data model
message CompanyAtpDataModel {
  //query company id
  int64 company_id = 1;
  //query config is paid
  bool is_paid = 2;
  //atp fee status: newRequired  oldFailUser
  string atp_fee_status = 3;

  //business legal name
  string business_name = 4;
  //business legal type
  string business_type = 5;

  //business address1
  string address1 = 6;
  //business address2
  string address2 = 7;
  //business address city
  string city = 8;
  //business address region
  string region = 9;
  //business address zipcode
  string zipcode = 10;
  //business address lat
  string lat = 11;
  //business address lng
  string lng = 12;

  //business website
  string website = 14;
  //business ein
  string ein = 15;
  //business einfiles
  repeated string ein_files = 16;

  //business owner first_name
  string first_name = 17;
  //business owner last_name
  string last_name = 18;
  //business owner mobile phone number
  string phone_number = 19;
  //business owner email
  string email = 20;
  //submit with ein
  bool with_ein = 21;
}
