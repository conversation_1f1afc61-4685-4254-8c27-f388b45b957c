syntax = "proto3";

package moego.service.payment.v2;

import "moego/models/payment/v2/common_enums.proto";
import "moego/models/payment/v2/payout_models.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v2";

// Request for PayoutSummary
message GetPayoutSummaryRequest {
  // 实体类型
  models.payment.v2.EntityType entity_type = 1;
  // 实体 id
  int64 entity_id = 2;
  // moego_payout_id
  int64 payout_id = 3;
}

// Response for PayoutSummary
message GetPayoutSummaryResponse {
  // payout
  moego.models.payment.v2.PayoutModel payout = 1;
  // payments
  repeated moego.models.payment.v2.PayoutModel payments = 2;
}

// GetPayoutListRequest
message GetPayoutListRequest {
  // 实体类型
  models.payment.v2.EntityType entity_type = 1;
  // 实体 id
  int64 entity_id = 2;
  // start_created_date
  int64 start_created_date = 3;
  // end_created_date
  int64 end_created_date = 4;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 10;
}

// GetPayoutListResponse
message GetPayoutListResponse {
  // payout
  repeated moego.models.payment.v2.PayoutModel payout = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// Payment's payout service.
service PayoutService {
  // Get PayoutSummary for business
  rpc GetPayoutSummary(GetPayoutSummaryRequest) returns (GetPayoutSummaryResponse);
  //  Get Payout List for business
  rpc GetPayoutList(GetPayoutListRequest) returns (GetPayoutListResponse);
}
