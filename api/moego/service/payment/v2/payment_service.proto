syntax = "proto3";

package moego.service.payment.v2;

import "google/type/money.proto";
import "moego/models/payment/v2/common_enums.proto";
import "moego/models/payment/v2/payment_enums.proto";
import "moego/models/payment/v2/payment_models.proto";
import "moego/utils/v1/time_period.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v2";

// PaymentService 支付服务
service PaymentService {
  // GetPaymentVersion 获取支付版本, 用于前端判断该走哪个支付流程，控制灰度
  rpc GetPaymentVersion(GetPaymentVersionRequest) returns (GetPaymentVersionResponse);

  // CreatePayment 创建支付单据，返回支付单据 ID
  rpc CreatePayment(CreatePaymentRequest) returns (CreatePaymentResponse);
  // Cancel 取消支付
  rpc CancelPayment(CancelPaymentRequest) returns (CancelPaymentResponse);
  // GetPayData 获取支付时所需的数据，用于前端加载第三方支付组件，注意此 API 只关注 Pay 支付动作
  rpc GetPayData(GetPayDataRequest) returns (GetPayDataResponse);
  // Pay 确认支付 Confirm，在用户提交完所有支付凭证后调用
  rpc PayPayment(PayPaymentRequest) returns (PayPaymentResponse);
  // SubmitActionDetail 提交支付 detail 凭证，一般是在完成 Pay 要求的 Action 后调用
  rpc SubmitActionDetail(SubmitActionDetailRequest) returns (SubmitActionDetailResponse);
  // GetPayment 获取支付单据
  rpc GetPayment(GetPaymentRequest) returns (GetPaymentResponse);
  // ListPayment 获取支付单据列表
  rpc ListPayment(ListPaymentRequest) returns (ListPaymentResponse);

  // 添加绑定的支付方式
  rpc AddRecurringPaymentMethod(AddRecurringPaymentMethodRequest) returns (AddRecurringPaymentMethodResponse);
  // 删除绑定的支付方式
  rpc DeleteRecurringPaymentMethod(DeleteRecurringPaymentMethodRequest) returns (DeleteRecurringPaymentMethodResponse);
  // 将payment method设置为primary
  rpc SetRecurringPaymentMethodPrimary(SetRecurringPaymentMethodPrimaryRequest) returns (SetRecurringPaymentMethodPrimaryResponse);
  // ListRecurringPaymentMethods 获取用户的所有绑定的支付方式，如 COF、ACH等
  rpc ListRecurringPaymentMethods(ListRecurringPaymentMethodsRequest) returns (ListRecurringPaymentMethodsResponse);
}

// Request for GetPaymentVersion
message GetPaymentVersionRequest {
  // 收款方
  models.payment.v2.User payee = 1;
}

// Response for GetPaymentVersion
message GetPaymentVersionResponse {
  // 支付版本
  models.payment.v2.PaymentVersion payment_version = 1;
  // 渠道
  models.payment.v2.ChannelType channel_type = 2;
}

// Request for CreatePayment
message CreatePaymentRequest {
  // 必填，上层业务系统类型
  models.payment.v2.ExternalType external_type = 1;
  // 必填，上层业务系统内单据 ID
  string external_id = 2;
  // 付款方，可选
  models.payment.v2.User payer = 3;
  // 收款方
  models.payment.v2.User payee = 4;
  // 金额
  google.type.Money amount = 5;
  // 支付类型，决定是pre-pay、pre-auth还是常规支付
  models.payment.v2.PaymentModel.PaymentType payment_type = 6;
}

// Response for CreatePayment
message CreatePaymentResponse {
  // 生成的支付单据
  models.payment.v2.PaymentModel payment = 1;
}

// cancel payment request
message CancelPaymentRequest {
  // 支付单据id
  int64 id = 1;
}

// cancel payment response
message CancelPaymentResponse {
  // msg 可以展示给用户的信息
  string msg = 1;
}

// Request for GetPayData
message GetPayDataRequest {
  // Business ID
  int64 business_id = 1;
  // 渠道，可不传，将由后端根据渠道路由自行决定；如果传了，优先级高于后端路由
  optional models.payment.v2.ChannelType channel_type = 2;
}

// Response for GetPayData
message GetPayDataResponse {
  // adyen data
  message AdyenData {
    // data
    string data = 1;
  }

  // 渠道
  models.payment.v2.ChannelType channel_type = 1;
  // 支付数据
  oneof data {
    // adyen data
    AdyenData adyen_data = 2;
  }
}

// Request for Pay
message PayPaymentRequest {
  // 支付单据 id
  int64 id = 1;
  // 支付方式
  models.payment.v2.PaymentMethod.MethodType payment_method_type = 2;
  // 支付凭证
  models.payment.v2.PaymentMethod.Detail detail = 3;
  // 是否添加cv fee,不传的时候后端判断
  optional bool add_convenience_fee = 4;
  // payer, 一般是customer name
  string payer = 5;
  // payment description 支付描述 自定义
  string description = 6;
}

// Response for Pay
message PayPaymentResponse {
  // msg 可以展示给用户的信息
  string msg = 1;
  // channel response，渠道返回的原始数据，用于前端加载第三方支付组件
  // e.g. adyen 3ds2:
  // `{
  //   "resultCode": "IdentifyShopper",
  //   "action": {
  //     "paymentData": "Ab02b4c0!BQABAgCuZFJrQOjSsl\\/zt+...",
  //     "paymentMethodType": "scheme",
  //     "authorisationToken": "Ab02b4c0!BQABAgAvrX03p...",
  //     "subtype": "fingerprint",
  //     "token": "eyJ0aHJlZURTTWV0aG9kTm90aWZpY...",
  //     "type": "threeDS2"
  //   }
  // }`
  string channel_response = 2;
}

// Request for SubmitActionDetail
message SubmitActionDetailRequest {
  // 支付单据 id
  int64 id = 1;
  // Action Result，前端从组件拿到的原始数据，
  // e.g. ayden 3ds2:
  // `{
  //   "details": {
  //     "threeDSResult": "eyJ0cmFuc1N0YXR1cyI6IlkifQ=="
  //   }
  // }`
  string action_result = 2;
}

// Response for SubmitPayDetail
message SubmitActionDetailResponse {
  // msg 可以展示给用户的信息
  string msg = 1;
  // channel response，渠道返回的原始数据，用于前端加载第三方支付组件,
  // e.g. adyen:
  // `{
  //   "resultCode": "Authorised",
  //   "pspReference": "V4HZ4RBFJGXXGN82"
  // }`
  string channel_response = 2;
}

// get payment request
message GetPaymentRequest {
  // 支付单据id
  int64 id = 1;
}

// get payment response
message GetPaymentResponse {
  // payment 实体
  models.payment.v2.PaymentModel payment = 1;
}

// list payment request
message ListPaymentRequest {
  // 分页查询请求
  utils.v2.PaginationRequest pagination_request = 1;
  // filter
  message Filter {
    // 买家
    repeated models.payment.v2.User payers = 1;
    // 卖家
    repeated models.payment.v2.User payees = 2;
    // order id
    repeated int64 order_ids = 3;
    // order payment id
    repeated int64 order_payment_ids = 4;
    // payment
    repeated int64 ids = 5;
    // 查询时间范围
    moego.utils.v1.TimePeriod time_period = 6;
  }
  // filter
  Filter filter = 2;
}

// list payment response
message ListPaymentResponse {
  // 支付列表
  repeated models.payment.v2.PaymentView payment_views = 1;
  // 分页
  utils.v2.PaginationResponse pagination_request = 2;
}

// add recurring payment method request
message AddRecurringPaymentMethodRequest {
  // customer id
  optional int64 customer_id = 1;
  // customer code
  optional string encrypted_customer_id = 2;
  // channel type
  optional models.payment.v2.ChannelType channel_type = 3;
  // payment method type
  models.payment.v2.PaymentMethod.MethodType payment_method_type = 4;
  // 支付凭证
  models.payment.v2.PaymentMethod.Detail detail = 5;
  // 透传参数，一般是用户自定义的额外信息
  optional models.payment.v2.RecurringPaymentMethodModel.Extra extra = 6;
}

// add recurring payment method response
message AddRecurringPaymentMethodResponse {
  // 已保存的支付方式
  models.payment.v2.RecurringPaymentMethodModel recurring_payment_method = 1;
}

// delete recurring payment method request
message DeleteRecurringPaymentMethodRequest {
  // 存储的 payment method id
  int64 payment_method_id = 1;
}

// delete recurring payment method response
message DeleteRecurringPaymentMethodResponse {}

// set recurring payment method primary request
message SetRecurringPaymentMethodPrimaryRequest {
  // payment method id
  int64 payment_method_id = 1;
}

// set recurring payment method primary response
message SetRecurringPaymentMethodPrimaryResponse {
  // 已设置的 payment method
  models.payment.v2.RecurringPaymentMethodModel recurring_payment_method = 1;
}

// list recurring payment method request
message ListRecurringPaymentMethodsRequest {
  // 分页请求
  utils.v2.PaginationRequest pagination_request = 1;
  // filter
  message Filter {
    // 用户
    repeated models.payment.v2.User users = 1;
    // payment method ids
    repeated int64 recurring_payment_method_ids = 2;
    // users
  }

  // filter
  Filter filter = 2;
}

// list recurring payment method response
message ListRecurringPaymentMethodsResponse {
  // 分页
  utils.v2.PaginationResponse pagination_response = 1;
  // 已保存的支付方式
  repeated models.payment.v2.RecurringPaymentMethodModel payment_methods = 2;
}
