syntax = "proto3";

package moego.service.payment.v2;

import "moego/models/payment/v2/common_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v2";

// PaymentTerminalService Terminal 相关服务
service PaymentTerminalService {
  // Retrieve required data to initialize the client side terminal SDK.
  rpc RetrieveSdkData(RetrieveSdkDataRequest) returns (RetrieveSdkDataResponse);
}

// Request for RetrieveSdkData
message RetrieveSdkDataRequest {
  // Adyen params
  message AdyenParams {
    // Setup token
    string setup_token = 1;
  }

  // Company ID
  int64 company_id = 1;
  // Business ID
  int64 business_id = 2;
  // 渠道
  models.payment.v2.ChannelType channel_type = 3;
  // Request params
  oneof params {
    // Data for Adyen
    AdyenParams adyen_params = 4;
  }
}

// Response for RetrieveSdkData
message RetrieveSdkDataResponse {
  // adyen data
  message AdyenData {
    // SDK data
    string sdk_data = 1;
  }

  // 支付数据
  oneof data {
    // adyen data
    AdyenData adyen_data = 1;
  }
}
