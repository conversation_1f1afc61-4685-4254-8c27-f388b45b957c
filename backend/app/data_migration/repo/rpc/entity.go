package rpc

type ListProductsRequest struct {
	CompanyID int64 `json:"companyId"`
}

type ListProductsResponse struct {
	Products []*Product `json:"products"`
}

type Product struct {
	ID                    int64   `json:"id"`
	BusinessID            int64   `json:"businessId"`
	Name                  string  `json:"name"`
	ImageURL              string  `json:"imageUrl"`
	Description           string  `json:"description"`
	SKU                   string  `json:"sku"`
	SupplierID            int64   `json:"supplierId"`
	CategoryID            int64   `json:"categoryId"`
	SupplyPrice           float64 `json:"supplyPrice"`
	TaxRate               float64 `json:"taxRate"`
	RetailPrice           float64 `json:"retailPrice"`
	SpecialPrice          float64 `json:"specialPrice"`
	EnableStaffCommission bool    `json:"enableStaffCommission"`
	CreateTime            int64   `json:"createTime"`
	UpdateTime            int64   `json:"updateTime"`
	TaxID                 int64   `json:"taxId"`
	Stock                 int64   `json:"stock"`
	Deleted               bool    `json:"deleted"`
	Barcode               string  `json:"barcode"`
}
