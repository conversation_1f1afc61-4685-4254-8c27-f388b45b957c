package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	jwt "github.com/golang-jwt/jwt/v4"
	"google.golang.org/api/idtoken"

	"github.com/MoeGolibrary/moego/backend/app/devops-auth/configinit"
)

// HTTPService is the HTTP server.
type HTTPService struct {
	cfg *configinit.Config
}

// NewHTTPService creates a new HTTP server.
func NewHTTPService(cfg *configinit.Config) *HTTPService {
	return &HTTPService{cfg: cfg}
}

type loginRequest struct {
	Credential string `json:"credential"`
}

var ErrInvalidHostedDomain = errors.New("user is not part of the required domain")

func decodeLoginRequest(r *http.Request) (*loginRequest, error) {
	var reqBody loginRequest
	if err := json.NewDecoder(r.Body).Decode(&reqBody); err != nil {
		return nil, errors.New("invalid request body")
	}
	if reqBody.Credential == "" {
		return nil, errors.New("credential is required")
	}
	return &reqBody, nil
}

func verifyGoogleIDToken(ctx context.Context, credential string, clientID string) (*idtoken.Payload, error) {
	payload, err := idtoken.Validate(ctx, credential, clientID)
	if err != nil {
		return nil, fmt.Errorf("invalid google id token: %w", err)
	}
	return payload, nil
}

func validateHostedDomain(payload *idtoken.Payload) error {
	hd, ok := payload.Claims["hd"].(string)
	if !ok || hd != "moego.pet" {
		return ErrInvalidHostedDomain
	}
	return nil
}

func generateJWT(claims jwt.MapClaims, secret string) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", fmt.Errorf("failed to create token: %w", err)
	}
	return tokenString, nil
}

func setAuthCookie(w http.ResponseWriter, tokenString string) {
	http.SetCookie(w, &http.Cookie{
		Name:     "auth_token",
		Value:    tokenString,
		Expires:  time.Now().Add(time.Hour * 24),
		HttpOnly: false,
		Secure:   false,
		Path:     "/",
		SameSite: http.SameSiteLaxMode,
	})
}

func (s *HTTPService) LoginHandler(w http.ResponseWriter, r *http.Request) {
	reqBody, err := decodeLoginRequest(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	payload, err := verifyGoogleIDToken(r.Context(), reqBody.Credential, s.cfg.Google.ClientID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return
	}

	if err := validateHostedDomain(payload); err != nil {
		http.Error(w, err.Error(), http.StatusForbidden)
		return
	}

	claims := jwt.MapClaims{
		"email": payload.Claims["email"],
		"sub":   payload.Claims["sub"],
		"exp":   time.Now().Add(time.Hour * 24).Unix(),
	}

	tokenString, err := generateJWT(claims, s.cfg.JWT.Secret)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	setAuthCookie(w, tokenString)
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write([]byte(`{"message": "Login successful"}`))
}
