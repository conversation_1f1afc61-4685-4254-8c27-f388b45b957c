load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "caretype",
    srcs = [
        "care_type_repo.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/caretype",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/proto/offering/v1:offering",
        "@io_gorm_gorm//:gorm",
    ],
)
