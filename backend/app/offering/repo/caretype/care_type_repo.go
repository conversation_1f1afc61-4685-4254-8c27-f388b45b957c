package caretype

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
)

//go:generate mockgen -package=caretype -destination=mocks/mock_care_type_repo.go github.com/moego/backend/app/offering/repo/caretype Repository
type Repository interface {
	Create(ctx context.Context, careType *CareType) error
	Get(ctx context.Context, id int64) (*CareType, error)
	Update(ctx context.Context, careType *CareType) error
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context, filter *ListCareTypeFilter) ([]*CareType, error)
}

// repository implements the data access logic for CareType.
type repository struct {
	db *gorm.DB
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{db: db.GetDB()}
}

// Create creates a new care type.
func (r *repository) Create(ctx context.Context, careType *CareType) error {
	err := r.db.WithContext(ctx).Create(careType).Error
	if err != nil {
		return err
	}
	return nil
}

// Get gets a care type by ID.
func (r *repository) Get(ctx context.Context, id int64) (*CareType, error) {
	var res *CareType
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

// Update updates a care type.
func (r *repository) Update(ctx context.Context, careType *CareType) error {
	err := r.db.WithContext(ctx).
		Model(&CareType{ID: careType.ID}).
		Updates(careType).Error
	if err != nil {
		return err
	}
	return nil
}

// Delete deletes a care type by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).Model(&CareType{}).Delete("id = ?", id).Error
	if err != nil {
		return err
	}
	return nil
}

// List lists care types.
func (r *repository) List(ctx context.Context, filter *ListCareTypeFilter) ([]*CareType, error) {
	var res []*CareType
	clause := r.db.WithContext(ctx).Model(&CareType{}).
		Where(&CareType{
			OrganizationType: filter.OrganizationType,
			OrganizationID:   filter.OrganizationID,
		})
	if len(filter.CareTypes) > 0 {
		clause = clause.Where("care_type IN ?", filter.CareTypes)
	}
	err := clause.Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}
