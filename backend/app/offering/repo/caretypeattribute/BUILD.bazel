load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "care_type_attribute",
    srcs = [
        "care_type_attribute_repo.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/care_type_attribute",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/proto/offering/v1:offering",
        "@io_gorm_gorm//:gorm",
    ],
)

go_library(
    name = "caretypeattribute",
    srcs = [
        "care_type_attribute_repo.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/caretypeattribute",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/proto/offering/v1:offering",
        "@io_gorm_gorm//:gorm",
    ],
)
