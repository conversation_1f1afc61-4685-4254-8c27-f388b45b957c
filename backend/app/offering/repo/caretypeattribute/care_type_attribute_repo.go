package caretypeattribute

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
)

//go:generate mockgen -package=caretypeattribute -destination=mocks/mock_care_type_attribute_repo.go github.com/moego/backend/app/offering/repo/caretypeattribute Repository
type Repository interface {
	Create(ctx context.Context, careTypeAttribute *CareTypeAttribute) error
	Get(ctx context.Context, id int64) (*CareTypeAttribute, error)
	Update(ctx context.Context, careTypeAttribute *CareTypeAttribute) error
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context, filter *ListCareTypeAttributeFilter) ([]*CareTypeAttribute, error)
}

// repository implements the data access logic for CareTypeAttribute.
type repository struct {
	db *gorm.DB
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{db: db.GetDB()}
}

// Create creates a new care type attribute.
func (r *repository) Create(ctx context.Context, careTypeAttribute *CareTypeAttribute) error {
	err := r.db.WithContext(ctx).Create(careTypeAttribute).Error
	if err != nil {
		return err
	}
	return nil
}

// Get gets a care type attribute by ID.
func (r *repository) Get(ctx context.Context, id int64) (*CareTypeAttribute, error) {
	var res *CareTypeAttribute
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

// Update updates a care type attribute.
func (r *repository) Update(ctx context.Context, careTypeAttribute *CareTypeAttribute) error {
	err := r.db.WithContext(ctx).
		Model(&CareTypeAttribute{ID: careTypeAttribute.ID}).
		Updates(careTypeAttribute).Error
	if err != nil {
		return err
	}
	return nil
}

// Delete deletes a care type attribute by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).Model(&CareTypeAttribute{}).Delete("id = ?", id).Error
	if err != nil {
		return err
	}
	return nil
}

// List lists care type attributes.
func (r *repository) List(ctx context.Context, filter *ListCareTypeAttributeFilter) ([]*CareTypeAttribute, error) {
	var res []*CareTypeAttribute
	clause := r.db.WithContext(ctx).
		Where("care_type_id = ?", filter.CareTypeID)
	if filter.IsRequired != nil {
		clause = clause.Where("is_required = ?", *filter.IsRequired)
	}
	err := clause.Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}
