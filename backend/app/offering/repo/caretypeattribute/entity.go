package caretypeattribute

import (
	"time"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

const TableNameCareTypeAttribute = "care_type_attribute"

// CareTypeAttribute mapped from table <care_type_attribute>
type CareTypeAttribute struct {
	//nolint:lll // reason: struct tags and comments in this file are long by design
	ID int64 `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:Primary key ID of the care type attribute entry" json:"id"` // Primary key ID of the care type attribute entry
	//nolint:lll // reason: struct tags and comments in this file are long by design
	CareTypeID int64 `gorm:"column:care_type_id;type:bigint;not null;comment:Care type to which this attribute is assigned" json:"care_type_id"` // Care type to which this attribute is assigned
	//nolint:lll // reason: struct tags and comments in this file are long by design
	AttributeName string `gorm:"column:attribute_name;type:character varying(255);not null;comment:Unique name (key) of the attribute within the care type (e.g., size, duration)" json:"attribute_name"` // Unique name (key) of the attribute within the care type (e.g., size, duration)
	//nolint:lll // reason: struct tags and comments in this file are long by design
	Label *string `gorm:"column:label;type:text;comment:Display label for frontend UI (e.g., Pet Size)" json:"label"` // Display label for frontend UI (e.g., Pet Size)
	//nolint:lll // reason: struct tags and comments in this file are long by design
	ValueType offeringpb.ValueType `gorm:"column:value_type;type:character varying(50);not null;default:STRING;comment:Value type of the attribute (STRING, NUMBER, BOOLEAN, ENUM)" json:"value_type"` // Value type of the attribute (STRING, NUMBER, BOOLEAN, ENUM)
	//nolint:lll // reason: struct tags and comments in this file are long by design
	Options *string `gorm:"column:options;type:jsonb;comment:JSON list of options (used when value_type is ENUM or BOOLEAN)" json:"options"` // JSON list of options (used when value_type is ENUM or BOOLEAN)
	//nolint:lll // reason: struct tags and comments in this file are long by design
	Description *string `gorm:"column:description;type:text;comment:Optional explanation or usage hint" json:"description"` // Optional explanation or usage hint
	//nolint:lll // reason: struct tags and comments in this file are long by design
	IsRequired bool `gorm:"column:is_required;type:boolean;not null;comment:Whether this attribute is required for the care type" json:"is_required"` // Whether this attribute is required for the care type
	//nolint:lll // reason: struct tags and comments in this file are long by design
	DefaultValue string `gorm:"column:default_value;type:text;not null;comment:Default value assigned to this attribute" json:"default_value"` // Default value assigned to this attribute
	//nolint:lll // reason: struct tags and comments in this file are long by design
	CreateTime *time.Time `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	//nolint:lll // reason: struct tags and comments in this file are long by design
	UpdateTime *time.Time `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	//nolint:lll // reason: struct tags and comments in this file are long by design
	DeleteTime *time.Time `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
}

// TableName CareTypeAttribute's table name
func (*CareTypeAttribute) TableName() string {
	return TableNameCareTypeAttribute
}

// ListCareTypeAttributeFilter filter for listing care types
type ListCareTypeAttributeFilter struct {
	// Required
	CareTypeID int64

	// Optional
	IsRequired *bool
}
