CREATE TABLE care_type
(
    id                BIGSERIAL PRIMARY KEY,

    organization_type VARCHAR(50) NOT NULL DEFAULT 'company',
    organization_id   BIGINT      NOT NULL DEFAULT 0,

    name              VARCHAR(50) NOT NULL DEFAULT '',
    care_type         VARCHAR(50) NOT NULL DEFAULT '',
    description       VARCHAR(512)         DEFAULT NULL,

    create_time       TIMESTAMPTZ NOT NULL DEFAULT now(),
    update_time       TIMESTAMPTZ NOT NULL DEFAULT now(),
    delete_time       TIMESTAMPTZ          DEFAULT NULL,

    UNIQUE (organization_type, organization_id, name)
);

-- === Column Comments ===
COMMENT
ON TABLE care_type IS 'Defines a Care Type available to an organization level (enterprise, company, business)';
COMMENT
ON COLUMN care_type.id IS 'Primary key ID of the care type';
COMMENT
ON COLUMN care_type.organization_type IS 'Level of the organization: enterprise, company, business, etc.';
COMMENT
ON COLUMN care_type.organization_id IS 'ID of the organization unit corresponding to the organization_type';
COMMENT
ON COLUMN care_type.name IS 'Name of the care type, unique within the same organization';
COMMENT
ON COLUMN care_type.care_type IS 'System code of the care type (e.g. grooming, daycare, training)';
COMMENT
ON COLUMN care_type.description IS 'Optional description of the care type';

-- === Recommended Constraints ===
ALTER TABLE care_type
    ADD CONSTRAINT check_org_type
        CHECK (organization_type IN ('enterprise', 'company'));



CREATE TABLE care_type_attribute
(
    id             BIGSERIAL PRIMARY KEY,

    care_type_id   BIGINT       NOT NULL DEFAULT 0,

    attribute_name VARCHAR(255) NOT NULL DEFAULT '',
    label          TEXT,
    value_type     VARCHAR(50)  NOT NULL DEFAULT 'STRING',
    options        JSONB,
    description    TEXT,
    is_required    BOOLEAN      NOT NULL DEFAULT FALSE,
    default_value  TEXT         NOT NULL DEFAULT '',

    create_time    TIMESTAMPTZ  NOT NULL DEFAULT now(),
    update_time    TIMESTAMPTZ  NOT NULL DEFAULT now(),
    delete_time    TIMESTAMPTZ           DEFAULT NULL,

    UNIQUE (care_type_id, attribute_name)
);

-- === Column Comments ===
COMMENT
ON TABLE care_type_attribute IS 'Attribute definition assigned to a specific care type';

COMMENT
ON COLUMN care_type_attribute.id IS 'Primary key ID of the care type attribute entry';
COMMENT
ON COLUMN care_type_attribute.care_type_id IS 'Care type to which this attribute is assigned';

COMMENT
ON COLUMN care_type_attribute.attribute_name IS 'Unique name (key) of the attribute within the care type (e.g., size, duration)';
COMMENT
ON COLUMN care_type_attribute.label IS 'Display label for frontend UI (e.g., Pet Size)';
COMMENT
ON COLUMN care_type_attribute.value_type IS 'Value type of the attribute (STRING, NUMBER, BOOLEAN, ENUM)';
COMMENT
ON COLUMN care_type_attribute.options IS 'JSON list of options (used when value_type is ENUM or BOOLEAN)';
COMMENT
ON COLUMN care_type_attribute.description IS 'Optional explanation or usage hint';
COMMENT
ON COLUMN care_type_attribute.is_required IS 'Whether this attribute is required for the care type';
COMMENT
ON COLUMN care_type_attribute.default_value IS 'Default value assigned to this attribute';

-- === Recommended Constraints ===
ALTER TABLE care_type_attribute
    ADD CONSTRAINT check_value_type
        CHECK (value_type IN ('STRING', 'NUMBER', 'BOOLEAN', 'ENUM'));


CREATE TABLE service_template
(
    id                BIGSERIAL PRIMARY KEY,

    organization_type VARCHAR(50)  NOT NULL DEFAULT 'company',
    organization_id   BIGINT       NOT NULL DEFAULT 0,

    care_type_id      BIGINT       NOT NULL DEFAULT 0,
    category_id       BIGINT       NOT NULL DEFAULT 0,

    name              VARCHAR(255) NOT NULL DEFAULT '',
    description       TEXT,
    color_code        VARCHAR(7)            DEFAULT '',
    sort              BIGINT       NOT NULL DEFAULT 0,
    images            JSONB                 DEFAULT '[]'::jsonb,

    source            INT          NOT NULL DEFAULT 0,
    is_active         BOOLEAN      NOT NULL DEFAULT FALSE,

    create_time       TIMESTAMPTZ  NOT NULL DEFAULT now(),
    update_time       TIMESTAMPTZ  NOT NULL DEFAULT now(),
    delete_time       TIMESTAMPTZ           DEFAULT NULL,

    UNIQUE (organization_type, organization_id, name)
);

-- === Column Comments ===
COMMENT
ON TABLE service_template IS 'Service template definition used to configure reusable service logic';

COMMENT
ON COLUMN service_template.id IS 'Primary key ID of the service template';
COMMENT
ON COLUMN service_template.organization_type IS 'Organization type: company, enterprise, business';
COMMENT
ON COLUMN service_template.organization_id IS 'Organization ID corresponding to the type';

COMMENT
ON COLUMN service_template.care_type_id IS 'Reference to care_type used in this service template';
COMMENT
ON COLUMN service_template.category_id IS 'Optional category to organize service templates';

COMMENT
ON COLUMN service_template.name IS 'Name of the service template, unique within the same organization';
COMMENT
ON COLUMN service_template.description IS 'Optional description of the service template';
COMMENT
ON COLUMN service_template.color_code IS 'Color code for UI display, such as #F15A2B';
COMMENT
ON COLUMN service_template.sort IS 'Sort order for UI display';
COMMENT
ON COLUMN service_template.images IS 'List of image URLs in JSON array';

COMMENT
ON COLUMN service_template.source IS 'Source of the template: 1-MoeGo Platform 2-Enterprise Hub';
COMMENT
ON COLUMN service_template.is_active IS 'Whether the service template is currently active';

-- === Recommended Indexes ===
CREATE INDEX idx_care_type
    ON service_template (care_type_id);


CREATE TABLE service_attribute_value
(
    id                  BIGSERIAL PRIMARY KEY,

    service_template_id BIGINT       NOT NULL DEFAULT 0,
    attribute_name      VARCHAR(255) NOT NULL DEFAULT '',
    attribute_value     TEXT         NOT NULL,

    create_time         TIMESTAMPTZ  NOT NULL DEFAULT now(),
    update_time         TIMESTAMPTZ  NOT NULL DEFAULT now(),
    delete_time         TIMESTAMPTZ           DEFAULT NULL,

    UNIQUE (service_template_id, attribute_name)
);

-- === Column Comments ===
COMMENT
ON COLUMN service_attribute_value.id IS 'Primary key ID of the service attribute value';
COMMENT
ON COLUMN service_attribute_value.service_template_id IS 'Reference to the service template';
COMMENT
ON COLUMN service_attribute_value.attribute_name IS 'Unique name (key) of the attribute within the care type (e.g., size, duration)';
COMMENT
ON COLUMN service_attribute_value.attribute_value IS 'Concrete value assigned to the attribute for this service-care_type pair';
