package serviceattributevalue

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
)

//go:generate mockgen -package=serviceattributevalue -destination=mocks/mock_service_attribute_value_repo.go github.com/moego/backend/app/offering/repo/serviceattributevalue Repository
type Repository interface {
	Create(ctx context.Context, serviceAttributeValue *ServiceAttributeValue) error
	Get(ctx context.Context, id int64) (*ServiceAttributeValue, error)
	Update(ctx context.Context, serviceAttributeValue *ServiceAttributeValue) error
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context, serviceTemplateID int64) ([]*ServiceAttributeValue, error)
}

// repository implements the data access logic for ServiceAttributeValue.
type repository struct {
	db *gorm.DB
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{db: db.GetDB()}
}

// Create creates a new service attribute value.
func (r *repository) Create(ctx context.Context, serviceAttributeValue *ServiceAttributeValue) error {
	err := r.db.WithContext(ctx).Create(serviceAttributeValue).Error
	if err != nil {
		return err
	}
	return nil
}

// Get gets a service attribute value by ID.
func (r *repository) Get(ctx context.Context, id int64) (*ServiceAttributeValue, error) {
	var res *ServiceAttributeValue
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

// Update updates a service attribute value.
func (r *repository) Update(ctx context.Context, serviceAttributeValue *ServiceAttributeValue) error {
	err := r.db.WithContext(ctx).
		Model(&ServiceAttributeValue{ID: serviceAttributeValue.ID}).
		Updates(serviceAttributeValue).Error
	if err != nil {
		return err
	}
	return nil
}

// Delete deletes a service attribute value by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).Model(&ServiceAttributeValue{}).Delete("id = ?", id).Error
	if err != nil {
		return err
	}
	return nil
}

// List lists service attribute values.
func (r *repository) List(ctx context.Context, serviceTemplateID int64) ([]*ServiceAttributeValue, error) {
	var res []*ServiceAttributeValue
	err := r.db.WithContext(ctx).Where("service_template_id = ?", serviceTemplateID).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}
