package servicetemplate

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
)

//go:generate mockgen -package=servicetemplate -destination=mocks/mock_service_template_repo.go github.com/moego/backend/app/offering/repo/servicetemplate Repository
type Repository interface {
	Create(ctx context.Context, serviceTemplate *ServiceTemplate) error
	Get(ctx context.Context, id int64) (*ServiceTemplate, error)
	Update(ctx context.Context, serviceTemplate *ServiceTemplate) error
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context, filter *ListServiceTemplateFilter) ([]*ServiceTemplate, error)
}

// repository implements the data access logic for ServiceTemplate.
type repository struct {
	db *gorm.DB
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{db: db.GetDB()}
}

// Create creates a new service template.
func (r *repository) Create(ctx context.Context, serviceTemplate *ServiceTemplate) error {
	err := r.db.WithContext(ctx).Create(serviceTemplate).Error
	if err != nil {
		return err
	}
	return nil
}

// Get gets a service template by ID.
func (r *repository) Get(ctx context.Context, id int64) (*ServiceTemplate, error) {
	var res *ServiceTemplate
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

// Update updates a service template.
func (r *repository) Update(ctx context.Context, serviceTemplate *ServiceTemplate) error {
	err := r.db.WithContext(ctx).
		Model(&ServiceTemplate{ID: serviceTemplate.ID}).
		Updates(serviceTemplate).Error
	if err != nil {
		return err
	}
	return nil
}

// Delete deletes a service template by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).Model(&ServiceTemplate{}).Delete("id = ?", id).Error
	if err != nil {
		return err
	}
	return nil
}

// List lists service templates.
func (r *repository) List(ctx context.Context, filter *ListServiceTemplateFilter) ([]*ServiceTemplate, error) {
	var res []*ServiceTemplate
	clause := r.db.WithContext(ctx).Model(&ServiceTemplate{}).
		Where(&ServiceTemplate{
			OrganizationType: filter.OrganizationType,
			OrganizationID:   filter.OrganizationID,
		})
	if len(filter.CareTypeIDs) > 0 {
		clause = clause.Where("care_type_id IN ?", filter.CareTypeIDs)
	}
	if len(filter.CategoriesIDs) > 0 {
		clause = clause.Where("category_id IN ?", filter.CategoriesIDs)
	}
	if filter.IsActive != nil {
		clause = clause.Where("is_active = ?", *filter.IsActive)
	}
	if filter.Source != nil {
		clause = clause.Where("source = ?", *filter.Source)
	}
	err := clause.Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}
