// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/offering/v1/care_type.proto

package offeringpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CareType with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CareType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CareType with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CareTypeMultiError, or nil
// if none found.
func (m *CareType) ValidateAll() error {
	return m.validate(true)
}

func (m *CareType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for OrganizationType

	// no validation rules for OrganizationId

	// no validation rules for Name

	// no validation rules for CareType

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CareTypeValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CareTypeValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CareTypeValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CareTypeValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CareTypeValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CareTypeValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeleteTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CareTypeValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CareTypeValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CareTypeValidationError{
				field:  "DeleteTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CareTypeMultiError(errors)
	}

	return nil
}

// CareTypeMultiError is an error wrapping multiple validation errors returned
// by CareType.ValidateAll() if the designated constraints aren't met.
type CareTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CareTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CareTypeMultiError) AllErrors() []error { return m }

// CareTypeValidationError is the validation error returned by
// CareType.Validate if the designated constraints aren't met.
type CareTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CareTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CareTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CareTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CareTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CareTypeValidationError) ErrorName() string { return "CareTypeValidationError" }

// Error satisfies the builtin error interface
func (e CareTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCareType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CareTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CareTypeValidationError{}

// Validate checks the field values on CareTypeAttribute with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CareTypeAttribute) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CareTypeAttribute with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CareTypeAttributeMultiError, or nil if none found.
func (m *CareTypeAttribute) ValidateAll() error {
	return m.validate(true)
}

func (m *CareTypeAttribute) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CareTypeId

	// no validation rules for AttributeKey

	// no validation rules for Label

	// no validation rules for ValueType

	if all {
		switch v := interface{}(m.GetOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CareTypeAttributeValidationError{
				field:  "Options",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Description

	// no validation rules for IsRequired

	if all {
		switch v := interface{}(m.GetDefaultValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "DefaultValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "DefaultValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CareTypeAttributeValidationError{
				field:  "DefaultValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CareTypeAttributeValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CareTypeAttributeValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeleteTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CareTypeAttributeValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CareTypeAttributeValidationError{
				field:  "DeleteTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CareTypeAttributeMultiError(errors)
	}

	return nil
}

// CareTypeAttributeMultiError is an error wrapping multiple validation errors
// returned by CareTypeAttribute.ValidateAll() if the designated constraints
// aren't met.
type CareTypeAttributeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CareTypeAttributeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CareTypeAttributeMultiError) AllErrors() []error { return m }

// CareTypeAttributeValidationError is the validation error returned by
// CareTypeAttribute.Validate if the designated constraints aren't met.
type CareTypeAttributeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CareTypeAttributeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CareTypeAttributeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CareTypeAttributeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CareTypeAttributeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CareTypeAttributeValidationError) ErrorName() string {
	return "CareTypeAttributeValidationError"
}

// Error satisfies the builtin error interface
func (e CareTypeAttributeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCareTypeAttribute.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CareTypeAttributeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CareTypeAttributeValidationError{}
