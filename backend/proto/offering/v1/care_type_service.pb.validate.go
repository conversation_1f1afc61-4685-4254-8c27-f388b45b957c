// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/offering/v1/care_type_service.proto

package offeringpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCareTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCareTypeRequestMultiError, or nil if none found.
func (m *CreateCareTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCareTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCareTypeRequestValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCareTypeRequestValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCareTypeRequestValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCareTypeRequestMultiError(errors)
	}

	return nil
}

// CreateCareTypeRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCareTypeRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCareTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCareTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCareTypeRequestMultiError) AllErrors() []error { return m }

// CreateCareTypeRequestValidationError is the validation error returned by
// CreateCareTypeRequest.Validate if the designated constraints aren't met.
type CreateCareTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCareTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCareTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCareTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCareTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCareTypeRequestValidationError) ErrorName() string {
	return "CreateCareTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCareTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCareTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCareTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCareTypeRequestValidationError{}

// Validate checks the field values on CreateCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCareTypeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCareTypeResponseMultiError, or nil if none found.
func (m *CreateCareTypeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCareTypeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCareTypeResponseValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCareTypeResponseMultiError(errors)
	}

	return nil
}

// CreateCareTypeResponseMultiError is an error wrapping multiple validation
// errors returned by CreateCareTypeResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCareTypeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCareTypeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCareTypeResponseMultiError) AllErrors() []error { return m }

// CreateCareTypeResponseValidationError is the validation error returned by
// CreateCareTypeResponse.Validate if the designated constraints aren't met.
type CreateCareTypeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCareTypeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCareTypeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCareTypeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCareTypeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCareTypeResponseValidationError) ErrorName() string {
	return "CreateCareTypeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCareTypeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCareTypeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCareTypeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCareTypeResponseValidationError{}

// Validate checks the field values on GetCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCareTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCareTypeRequestMultiError, or nil if none found.
func (m *GetCareTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCareTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetCareTypeRequestMultiError(errors)
	}

	return nil
}

// GetCareTypeRequestMultiError is an error wrapping multiple validation errors
// returned by GetCareTypeRequest.ValidateAll() if the designated constraints
// aren't met.
type GetCareTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCareTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCareTypeRequestMultiError) AllErrors() []error { return m }

// GetCareTypeRequestValidationError is the validation error returned by
// GetCareTypeRequest.Validate if the designated constraints aren't met.
type GetCareTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCareTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCareTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCareTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCareTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCareTypeRequestValidationError) ErrorName() string {
	return "GetCareTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCareTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCareTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCareTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCareTypeRequestValidationError{}

// Validate checks the field values on GetCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCareTypeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCareTypeResponseMultiError, or nil if none found.
func (m *GetCareTypeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCareTypeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCareTypeResponseValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCareTypeResponseMultiError(errors)
	}

	return nil
}

// GetCareTypeResponseMultiError is an error wrapping multiple validation
// errors returned by GetCareTypeResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCareTypeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCareTypeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCareTypeResponseMultiError) AllErrors() []error { return m }

// GetCareTypeResponseValidationError is the validation error returned by
// GetCareTypeResponse.Validate if the designated constraints aren't met.
type GetCareTypeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCareTypeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCareTypeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCareTypeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCareTypeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCareTypeResponseValidationError) ErrorName() string {
	return "GetCareTypeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCareTypeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCareTypeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCareTypeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCareTypeResponseValidationError{}

// Validate checks the field values on ListCareTypesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCareTypesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCareTypesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCareTypesRequestMultiError, or nil if none found.
func (m *ListCareTypesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCareTypesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrganizationType

	// no validation rules for OrganizationId

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCareTypesRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCareTypesRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCareTypesRequestValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCareTypesRequestMultiError(errors)
	}

	return nil
}

// ListCareTypesRequestMultiError is an error wrapping multiple validation
// errors returned by ListCareTypesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCareTypesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCareTypesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCareTypesRequestMultiError) AllErrors() []error { return m }

// ListCareTypesRequestValidationError is the validation error returned by
// ListCareTypesRequest.Validate if the designated constraints aren't met.
type ListCareTypesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCareTypesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCareTypesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCareTypesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCareTypesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCareTypesRequestValidationError) ErrorName() string {
	return "ListCareTypesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCareTypesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCareTypesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCareTypesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCareTypesRequestValidationError{}

// Validate checks the field values on ListCareTypesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCareTypesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCareTypesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCareTypesResponseMultiError, or nil if none found.
func (m *ListCareTypesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCareTypesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCareTypes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCareTypesResponseValidationError{
						field:  fmt.Sprintf("CareTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCareTypesResponseValidationError{
						field:  fmt.Sprintf("CareTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCareTypesResponseValidationError{
					field:  fmt.Sprintf("CareTypes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCareTypesResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCareTypesResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCareTypesResponseValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListCareTypesResponseMultiError(errors)
	}

	return nil
}

// ListCareTypesResponseMultiError is an error wrapping multiple validation
// errors returned by ListCareTypesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCareTypesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCareTypesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCareTypesResponseMultiError) AllErrors() []error { return m }

// ListCareTypesResponseValidationError is the validation error returned by
// ListCareTypesResponse.Validate if the designated constraints aren't met.
type ListCareTypesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCareTypesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCareTypesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCareTypesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCareTypesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCareTypesResponseValidationError) ErrorName() string {
	return "ListCareTypesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCareTypesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCareTypesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCareTypesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCareTypesResponseValidationError{}

// Validate checks the field values on UpdateCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCareTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCareTypeRequestMultiError, or nil if none found.
func (m *UpdateCareTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCareTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCareTypeRequestValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCareTypeRequestValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCareTypeRequestValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCareTypeRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCareTypeRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCareTypeRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCareTypeRequestMultiError(errors)
	}

	return nil
}

// UpdateCareTypeRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCareTypeRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCareTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCareTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCareTypeRequestMultiError) AllErrors() []error { return m }

// UpdateCareTypeRequestValidationError is the validation error returned by
// UpdateCareTypeRequest.Validate if the designated constraints aren't met.
type UpdateCareTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCareTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCareTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCareTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCareTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCareTypeRequestValidationError) ErrorName() string {
	return "UpdateCareTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCareTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCareTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCareTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCareTypeRequestValidationError{}

// Validate checks the field values on UpdateCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCareTypeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCareTypeResponseMultiError, or nil if none found.
func (m *UpdateCareTypeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCareTypeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCareTypeResponseValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCareTypeResponseMultiError(errors)
	}

	return nil
}

// UpdateCareTypeResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateCareTypeResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCareTypeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCareTypeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCareTypeResponseMultiError) AllErrors() []error { return m }

// UpdateCareTypeResponseValidationError is the validation error returned by
// UpdateCareTypeResponse.Validate if the designated constraints aren't met.
type UpdateCareTypeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCareTypeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCareTypeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCareTypeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCareTypeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCareTypeResponseValidationError) ErrorName() string {
	return "UpdateCareTypeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCareTypeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCareTypeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCareTypeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCareTypeResponseValidationError{}

// Validate checks the field values on DeleteCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCareTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCareTypeRequestMultiError, or nil if none found.
func (m *DeleteCareTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCareTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteCareTypeRequestMultiError(errors)
	}

	return nil
}

// DeleteCareTypeRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteCareTypeRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteCareTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCareTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCareTypeRequestMultiError) AllErrors() []error { return m }

// DeleteCareTypeRequestValidationError is the validation error returned by
// DeleteCareTypeRequest.Validate if the designated constraints aren't met.
type DeleteCareTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCareTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCareTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCareTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCareTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCareTypeRequestValidationError) ErrorName() string {
	return "DeleteCareTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCareTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCareTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCareTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCareTypeRequestValidationError{}

// Validate checks the field values on DeleteCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCareTypeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCareTypeResponseMultiError, or nil if none found.
func (m *DeleteCareTypeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCareTypeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteCareTypeResponseMultiError(errors)
	}

	return nil
}

// DeleteCareTypeResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteCareTypeResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteCareTypeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCareTypeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCareTypeResponseMultiError) AllErrors() []error { return m }

// DeleteCareTypeResponseValidationError is the validation error returned by
// DeleteCareTypeResponse.Validate if the designated constraints aren't met.
type DeleteCareTypeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCareTypeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCareTypeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCareTypeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCareTypeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCareTypeResponseValidationError) ErrorName() string {
	return "DeleteCareTypeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCareTypeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCareTypeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCareTypeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCareTypeResponseValidationError{}

// Validate checks the field values on CreateCareTypeAttributeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCareTypeAttributeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCareTypeAttributeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateCareTypeAttributeRequestMultiError, or nil if none found.
func (m *CreateCareTypeAttributeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCareTypeAttributeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareTypeAttribute()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCareTypeAttributeRequestValidationError{
					field:  "CareTypeAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCareTypeAttributeRequestValidationError{
					field:  "CareTypeAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareTypeAttribute()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCareTypeAttributeRequestValidationError{
				field:  "CareTypeAttribute",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCareTypeAttributeRequestMultiError(errors)
	}

	return nil
}

// CreateCareTypeAttributeRequestMultiError is an error wrapping multiple
// validation errors returned by CreateCareTypeAttributeRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateCareTypeAttributeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCareTypeAttributeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCareTypeAttributeRequestMultiError) AllErrors() []error { return m }

// CreateCareTypeAttributeRequestValidationError is the validation error
// returned by CreateCareTypeAttributeRequest.Validate if the designated
// constraints aren't met.
type CreateCareTypeAttributeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCareTypeAttributeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCareTypeAttributeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCareTypeAttributeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCareTypeAttributeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCareTypeAttributeRequestValidationError) ErrorName() string {
	return "CreateCareTypeAttributeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCareTypeAttributeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCareTypeAttributeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCareTypeAttributeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCareTypeAttributeRequestValidationError{}

// Validate checks the field values on CreateCareTypeAttributeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCareTypeAttributeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCareTypeAttributeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateCareTypeAttributeResponseMultiError, or nil if none found.
func (m *CreateCareTypeAttributeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCareTypeAttributeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareTypeAttribute()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCareTypeAttributeResponseValidationError{
					field:  "CareTypeAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCareTypeAttributeResponseValidationError{
					field:  "CareTypeAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareTypeAttribute()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCareTypeAttributeResponseValidationError{
				field:  "CareTypeAttribute",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCareTypeAttributeResponseMultiError(errors)
	}

	return nil
}

// CreateCareTypeAttributeResponseMultiError is an error wrapping multiple
// validation errors returned by CreateCareTypeAttributeResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateCareTypeAttributeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCareTypeAttributeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCareTypeAttributeResponseMultiError) AllErrors() []error { return m }

// CreateCareTypeAttributeResponseValidationError is the validation error
// returned by CreateCareTypeAttributeResponse.Validate if the designated
// constraints aren't met.
type CreateCareTypeAttributeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCareTypeAttributeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCareTypeAttributeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCareTypeAttributeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCareTypeAttributeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCareTypeAttributeResponseValidationError) ErrorName() string {
	return "CreateCareTypeAttributeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCareTypeAttributeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCareTypeAttributeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCareTypeAttributeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCareTypeAttributeResponseValidationError{}

// Validate checks the field values on ListCareTypeAttributesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCareTypeAttributesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCareTypeAttributesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCareTypeAttributesRequestMultiError, or nil if none found.
func (m *ListCareTypeAttributesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCareTypeAttributesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CareTypeId

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCareTypeAttributesRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCareTypeAttributesRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCareTypeAttributesRequestValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCareTypeAttributesRequestMultiError(errors)
	}

	return nil
}

// ListCareTypeAttributesRequestMultiError is an error wrapping multiple
// validation errors returned by ListCareTypeAttributesRequest.ValidateAll()
// if the designated constraints aren't met.
type ListCareTypeAttributesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCareTypeAttributesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCareTypeAttributesRequestMultiError) AllErrors() []error { return m }

// ListCareTypeAttributesRequestValidationError is the validation error
// returned by ListCareTypeAttributesRequest.Validate if the designated
// constraints aren't met.
type ListCareTypeAttributesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCareTypeAttributesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCareTypeAttributesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCareTypeAttributesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCareTypeAttributesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCareTypeAttributesRequestValidationError) ErrorName() string {
	return "ListCareTypeAttributesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCareTypeAttributesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCareTypeAttributesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCareTypeAttributesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCareTypeAttributesRequestValidationError{}

// Validate checks the field values on ListCareTypeAttributesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCareTypeAttributesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCareTypeAttributesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCareTypeAttributesResponseMultiError, or nil if none found.
func (m *ListCareTypeAttributesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCareTypeAttributesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCareTypeAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCareTypeAttributesResponseValidationError{
						field:  fmt.Sprintf("CareTypeAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCareTypeAttributesResponseValidationError{
						field:  fmt.Sprintf("CareTypeAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCareTypeAttributesResponseValidationError{
					field:  fmt.Sprintf("CareTypeAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCareTypeAttributesResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCareTypeAttributesResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCareTypeAttributesResponseValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListCareTypeAttributesResponseMultiError(errors)
	}

	return nil
}

// ListCareTypeAttributesResponseMultiError is an error wrapping multiple
// validation errors returned by ListCareTypeAttributesResponse.ValidateAll()
// if the designated constraints aren't met.
type ListCareTypeAttributesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCareTypeAttributesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCareTypeAttributesResponseMultiError) AllErrors() []error { return m }

// ListCareTypeAttributesResponseValidationError is the validation error
// returned by ListCareTypeAttributesResponse.Validate if the designated
// constraints aren't met.
type ListCareTypeAttributesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCareTypeAttributesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCareTypeAttributesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCareTypeAttributesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCareTypeAttributesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCareTypeAttributesResponseValidationError) ErrorName() string {
	return "ListCareTypeAttributesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCareTypeAttributesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCareTypeAttributesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCareTypeAttributesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCareTypeAttributesResponseValidationError{}

// Validate checks the field values on DeleteCareTypeAttributeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCareTypeAttributeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCareTypeAttributeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteCareTypeAttributeRequestMultiError, or nil if none found.
func (m *DeleteCareTypeAttributeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCareTypeAttributeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteCareTypeAttributeRequestMultiError(errors)
	}

	return nil
}

// DeleteCareTypeAttributeRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteCareTypeAttributeRequest.ValidateAll()
// if the designated constraints aren't met.
type DeleteCareTypeAttributeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCareTypeAttributeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCareTypeAttributeRequestMultiError) AllErrors() []error { return m }

// DeleteCareTypeAttributeRequestValidationError is the validation error
// returned by DeleteCareTypeAttributeRequest.Validate if the designated
// constraints aren't met.
type DeleteCareTypeAttributeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCareTypeAttributeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCareTypeAttributeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCareTypeAttributeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCareTypeAttributeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCareTypeAttributeRequestValidationError) ErrorName() string {
	return "DeleteCareTypeAttributeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCareTypeAttributeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCareTypeAttributeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCareTypeAttributeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCareTypeAttributeRequestValidationError{}

// Validate checks the field values on DeleteCareTypeAttributeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCareTypeAttributeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCareTypeAttributeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteCareTypeAttributeResponseMultiError, or nil if none found.
func (m *DeleteCareTypeAttributeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCareTypeAttributeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteCareTypeAttributeResponseMultiError(errors)
	}

	return nil
}

// DeleteCareTypeAttributeResponseMultiError is an error wrapping multiple
// validation errors returned by DeleteCareTypeAttributeResponse.ValidateAll()
// if the designated constraints aren't met.
type DeleteCareTypeAttributeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCareTypeAttributeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCareTypeAttributeResponseMultiError) AllErrors() []error { return m }

// DeleteCareTypeAttributeResponseValidationError is the validation error
// returned by DeleteCareTypeAttributeResponse.Validate if the designated
// constraints aren't met.
type DeleteCareTypeAttributeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCareTypeAttributeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCareTypeAttributeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCareTypeAttributeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCareTypeAttributeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCareTypeAttributeResponseValidationError) ErrorName() string {
	return "DeleteCareTypeAttributeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCareTypeAttributeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCareTypeAttributeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCareTypeAttributeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCareTypeAttributeResponseValidationError{}
