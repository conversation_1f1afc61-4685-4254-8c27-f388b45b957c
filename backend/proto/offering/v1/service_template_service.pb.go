// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_template_service.proto

package offeringpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 删除服务模板请求
type DeleteServiceTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID
	ServiceTemplateId int64 `protobuf:"varint,1,opt,name=service_template_id,json=serviceTemplateId,proto3" json:"service_template_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DeleteServiceTemplateRequest) Reset() {
	*x = DeleteServiceTemplateRequest{}
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceTemplateRequest) ProtoMessage() {}

func (x *DeleteServiceTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceTemplateRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_template_service_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteServiceTemplateRequest) GetServiceTemplateId() int64 {
	if x != nil {
		return x.ServiceTemplateId
	}
	return 0
}

// 删除服务模板响应
type DeleteServiceTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceTemplateResponse) Reset() {
	*x = DeleteServiceTemplateResponse{}
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceTemplateResponse) ProtoMessage() {}

func (x *DeleteServiceTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceTemplateResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_template_service_proto_rawDescGZIP(), []int{1}
}

// 更新服务模板请求
type UpdateServiceTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板
	ServiceTemplate *ServiceTemplate `protobuf:"bytes,1,opt,name=service_template,json=serviceTemplate,proto3" json:"service_template,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateServiceTemplateRequest) Reset() {
	*x = UpdateServiceTemplateRequest{}
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceTemplateRequest) ProtoMessage() {}

func (x *UpdateServiceTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceTemplateRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_template_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateServiceTemplateRequest) GetServiceTemplate() *ServiceTemplate {
	if x != nil {
		return x.ServiceTemplate
	}
	return nil
}

// 更新服务模板响应
type UpdateServiceTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceTemplateResponse) Reset() {
	*x = UpdateServiceTemplateResponse{}
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceTemplateResponse) ProtoMessage() {}

func (x *UpdateServiceTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceTemplateResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_template_service_proto_rawDescGZIP(), []int{3}
}

// 创建服务工厂请求
type CreateServiceTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务工厂
	ServiceTemplate *ServiceTemplate `protobuf:"bytes,1,opt,name=service_template,json=serviceTemplate,proto3" json:"service_template,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateServiceTemplateRequest) Reset() {
	*x = CreateServiceTemplateRequest{}
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTemplateRequest) ProtoMessage() {}

func (x *CreateServiceTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_template_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateServiceTemplateRequest) GetServiceTemplate() *ServiceTemplate {
	if x != nil {
		return x.ServiceTemplate
	}
	return nil
}

// 创建服务工厂响应
type CreateServiceTemplateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID
	// (-- api-linter: core::0141::forbidden-types=disabled
	//
	//	aip.dev/not-precedent: uint64 is appropriate for ID field --)
	ServiceTemplateId uint64 `protobuf:"varint,1,opt,name=service_template_id,json=serviceTemplateId,proto3" json:"service_template_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateServiceTemplateResponse) Reset() {
	*x = CreateServiceTemplateResponse{}
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceTemplateResponse) ProtoMessage() {}

func (x *CreateServiceTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_template_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceTemplateResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_template_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateServiceTemplateResponse) GetServiceTemplateId() uint64 {
	if x != nil {
		return x.ServiceTemplateId
	}
	return 0
}

var File_backend_proto_offering_v1_service_template_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_template_service_proto_rawDesc = "" +
	"\n" +
	"8backend/proto/offering/v1/service_template_service.proto\x12\x19backend.proto.offering.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17validate/validate.proto\x1a0backend/proto/offering/v1/service_template.proto\x1a&backend/proto/offering/v1/common.proto\"N\n" +
	"\x1cDeleteServiceTemplateRequest\x12.\n" +
	"\x13service_template_id\x18\x01 \x01(\x03R\x11serviceTemplateId\"\x1f\n" +
	"\x1dDeleteServiceTemplateResponse\"u\n" +
	"\x1cUpdateServiceTemplateRequest\x12U\n" +
	"\x10service_template\x18\x01 \x01(\v2*.backend.proto.offering.v1.ServiceTemplateR\x0fserviceTemplate\"\x1f\n" +
	"\x1dUpdateServiceTemplateResponse\"u\n" +
	"\x1cCreateServiceTemplateRequest\x12U\n" +
	"\x10service_template\x18\x01 \x01(\v2*.backend.proto.offering.v1.ServiceTemplateR\x0fserviceTemplate\"O\n" +
	"\x1dCreateServiceTemplateResponse\x12.\n" +
	"\x13service_template_id\x18\x01 \x01(\x04R\x11serviceTemplateId2\xbf\x03\n" +
	"\x16ServiceTemplateService\x12\x8a\x01\n" +
	"\x15CreateServiceTemplate\x127.backend.proto.offering.v1.CreateServiceTemplateRequest\x1a8.backend.proto.offering.v1.CreateServiceTemplateResponse\x12\x8a\x01\n" +
	"\x15UpdateServiceTemplate\x127.backend.proto.offering.v1.UpdateServiceTemplateRequest\x1a8.backend.proto.offering.v1.UpdateServiceTemplateResponse\x12\x8a\x01\n" +
	"\x15DeleteServiceTemplate\x127.backend.proto.offering.v1.DeleteServiceTemplateRequest\x1a8.backend.proto.offering.v1.DeleteServiceTemplateResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_template_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_template_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_template_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_template_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_template_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_template_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_template_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_template_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_template_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_backend_proto_offering_v1_service_template_service_proto_goTypes = []any{
	(*DeleteServiceTemplateRequest)(nil),  // 0: backend.proto.offering.v1.DeleteServiceTemplateRequest
	(*DeleteServiceTemplateResponse)(nil), // 1: backend.proto.offering.v1.DeleteServiceTemplateResponse
	(*UpdateServiceTemplateRequest)(nil),  // 2: backend.proto.offering.v1.UpdateServiceTemplateRequest
	(*UpdateServiceTemplateResponse)(nil), // 3: backend.proto.offering.v1.UpdateServiceTemplateResponse
	(*CreateServiceTemplateRequest)(nil),  // 4: backend.proto.offering.v1.CreateServiceTemplateRequest
	(*CreateServiceTemplateResponse)(nil), // 5: backend.proto.offering.v1.CreateServiceTemplateResponse
	(*ServiceTemplate)(nil),               // 6: backend.proto.offering.v1.ServiceTemplate
}
var file_backend_proto_offering_v1_service_template_service_proto_depIdxs = []int32{
	6, // 0: backend.proto.offering.v1.UpdateServiceTemplateRequest.service_template:type_name -> backend.proto.offering.v1.ServiceTemplate
	6, // 1: backend.proto.offering.v1.CreateServiceTemplateRequest.service_template:type_name -> backend.proto.offering.v1.ServiceTemplate
	4, // 2: backend.proto.offering.v1.ServiceTemplateService.CreateServiceTemplate:input_type -> backend.proto.offering.v1.CreateServiceTemplateRequest
	2, // 3: backend.proto.offering.v1.ServiceTemplateService.UpdateServiceTemplate:input_type -> backend.proto.offering.v1.UpdateServiceTemplateRequest
	0, // 4: backend.proto.offering.v1.ServiceTemplateService.DeleteServiceTemplate:input_type -> backend.proto.offering.v1.DeleteServiceTemplateRequest
	5, // 5: backend.proto.offering.v1.ServiceTemplateService.CreateServiceTemplate:output_type -> backend.proto.offering.v1.CreateServiceTemplateResponse
	3, // 6: backend.proto.offering.v1.ServiceTemplateService.UpdateServiceTemplate:output_type -> backend.proto.offering.v1.UpdateServiceTemplateResponse
	1, // 7: backend.proto.offering.v1.ServiceTemplateService.DeleteServiceTemplate:output_type -> backend.proto.offering.v1.DeleteServiceTemplateResponse
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_template_service_proto_init() }
func file_backend_proto_offering_v1_service_template_service_proto_init() {
	if File_backend_proto_offering_v1_service_template_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_service_template_proto_init()
	file_backend_proto_offering_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_template_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_template_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_service_template_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_template_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_service_template_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_template_service_proto = out.File
	file_backend_proto_offering_v1_service_template_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_template_service_proto_depIdxs = nil
}
