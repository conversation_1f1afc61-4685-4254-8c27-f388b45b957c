// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/sales/v1/annual_contract_service.proto

package salespb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateAnnualContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAnnualContractRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAnnualContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAnnualContractRequestMultiError, or nil if none found.
func (m *CreateAnnualContractRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAnnualContractRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCreator()) < 1 {
		err := CreateAnnualContractRequestValidationError{
			field:  "Creator",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCompanyId() <= 0 {
		err := CreateAnnualContractRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateAnnualContractRequest_SubscriptionPlan_NotInLookup[m.GetSubscriptionPlan()]; ok {
		err := CreateAnnualContractRequestValidationError{
			field:  "SubscriptionPlan",
			reason: "value must not be in list [SUBSCRIPTION_PLAN_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := SubscriptionPlan_name[int32(m.GetSubscriptionPlan())]; !ok {
		err := CreateAnnualContractRequestValidationError{
			field:  "SubscriptionPlan",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSubscriptionTermMonths() <= 0 {
		err := CreateAnnualContractRequestValidationError{
			field:  "SubscriptionTermMonths",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateAnnualContractRequest_DiscountPercentage_Pattern.MatchString(m.GetDiscountPercentage()) {
		err := CreateAnnualContractRequestValidationError{
			field:  "DiscountPercentage",
			reason: "value does not match regex pattern \"^[0-9]+(\\\\.[0-9]{1,2})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBdLocationCount() < 0 {
		err := CreateAnnualContractRequestValidationError{
			field:  "BdLocationCount",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetGroomingLocationCount() < 0 {
		err := CreateAnnualContractRequestValidationError{
			field:  "GroomingLocationCount",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetGroomingVanCount() < 0 {
		err := CreateAnnualContractRequestValidationError{
			field:  "GroomingVanCount",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateAnnualContractRequestMultiError(errors)
	}

	return nil
}

// CreateAnnualContractRequestMultiError is an error wrapping multiple
// validation errors returned by CreateAnnualContractRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateAnnualContractRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAnnualContractRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAnnualContractRequestMultiError) AllErrors() []error { return m }

// CreateAnnualContractRequestValidationError is the validation error returned
// by CreateAnnualContractRequest.Validate if the designated constraints
// aren't met.
type CreateAnnualContractRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAnnualContractRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAnnualContractRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAnnualContractRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAnnualContractRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAnnualContractRequestValidationError) ErrorName() string {
	return "CreateAnnualContractRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAnnualContractRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAnnualContractRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAnnualContractRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAnnualContractRequestValidationError{}

var _CreateAnnualContractRequest_SubscriptionPlan_NotInLookup = map[SubscriptionPlan]struct{}{
	0: {},
}

var _CreateAnnualContractRequest_DiscountPercentage_Pattern = regexp.MustCompile("^[0-9]+(\\.[0-9]{1,2})?$")

// Validate checks the field values on GetAnnualContractRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAnnualContractRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAnnualContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAnnualContractRequestMultiError, or nil if none found.
func (m *GetAnnualContractRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAnnualContractRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := GetAnnualContractRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAnnualContractRequestMultiError(errors)
	}

	return nil
}

// GetAnnualContractRequestMultiError is an error wrapping multiple validation
// errors returned by GetAnnualContractRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAnnualContractRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAnnualContractRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAnnualContractRequestMultiError) AllErrors() []error { return m }

// GetAnnualContractRequestValidationError is the validation error returned by
// GetAnnualContractRequest.Validate if the designated constraints aren't met.
type GetAnnualContractRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAnnualContractRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAnnualContractRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAnnualContractRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAnnualContractRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAnnualContractRequestValidationError) ErrorName() string {
	return "GetAnnualContractRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAnnualContractRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAnnualContractRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAnnualContractRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAnnualContractRequestValidationError{}

// Validate checks the field values on ListAnnualContractsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAnnualContractsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAnnualContractsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAnnualContractsRequestMultiError, or nil if none found.
func (m *ListAnnualContractsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAnnualContractsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageSize(); val <= 0 || val > 100 {
		err := ListAnnualContractsRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range (0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAnnualContractsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAnnualContractsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAnnualContractsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAnnualContractsRequestMultiError(errors)
	}

	return nil
}

// ListAnnualContractsRequestMultiError is an error wrapping multiple
// validation errors returned by ListAnnualContractsRequest.ValidateAll() if
// the designated constraints aren't met.
type ListAnnualContractsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAnnualContractsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAnnualContractsRequestMultiError) AllErrors() []error { return m }

// ListAnnualContractsRequestValidationError is the validation error returned
// by ListAnnualContractsRequest.Validate if the designated constraints aren't met.
type ListAnnualContractsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAnnualContractsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAnnualContractsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAnnualContractsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAnnualContractsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAnnualContractsRequestValidationError) ErrorName() string {
	return "ListAnnualContractsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAnnualContractsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAnnualContractsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAnnualContractsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAnnualContractsRequestValidationError{}

// Validate checks the field values on ListAnnualContractsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAnnualContractsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAnnualContractsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAnnualContractsResponseMultiError, or nil if none found.
func (m *ListAnnualContractsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAnnualContractsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAnnualContracts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAnnualContractsResponseValidationError{
						field:  fmt.Sprintf("AnnualContracts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAnnualContractsResponseValidationError{
						field:  fmt.Sprintf("AnnualContracts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAnnualContractsResponseValidationError{
					field:  fmt.Sprintf("AnnualContracts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return ListAnnualContractsResponseMultiError(errors)
	}

	return nil
}

// ListAnnualContractsResponseMultiError is an error wrapping multiple
// validation errors returned by ListAnnualContractsResponse.ValidateAll() if
// the designated constraints aren't met.
type ListAnnualContractsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAnnualContractsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAnnualContractsResponseMultiError) AllErrors() []error { return m }

// ListAnnualContractsResponseValidationError is the validation error returned
// by ListAnnualContractsResponse.Validate if the designated constraints
// aren't met.
type ListAnnualContractsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAnnualContractsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAnnualContractsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAnnualContractsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAnnualContractsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAnnualContractsResponseValidationError) ErrorName() string {
	return "ListAnnualContractsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAnnualContractsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAnnualContractsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAnnualContractsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAnnualContractsResponseValidationError{}

// Validate checks the field values on CountAnnualContractsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountAnnualContractsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountAnnualContractsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountAnnualContractsRequestMultiError, or nil if none found.
func (m *CountAnnualContractsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CountAnnualContractsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountAnnualContractsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountAnnualContractsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountAnnualContractsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CountAnnualContractsRequestMultiError(errors)
	}

	return nil
}

// CountAnnualContractsRequestMultiError is an error wrapping multiple
// validation errors returned by CountAnnualContractsRequest.ValidateAll() if
// the designated constraints aren't met.
type CountAnnualContractsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountAnnualContractsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountAnnualContractsRequestMultiError) AllErrors() []error { return m }

// CountAnnualContractsRequestValidationError is the validation error returned
// by CountAnnualContractsRequest.Validate if the designated constraints
// aren't met.
type CountAnnualContractsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountAnnualContractsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountAnnualContractsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountAnnualContractsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountAnnualContractsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountAnnualContractsRequestValidationError) ErrorName() string {
	return "CountAnnualContractsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CountAnnualContractsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountAnnualContractsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountAnnualContractsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountAnnualContractsRequestValidationError{}

// Validate checks the field values on CountAnnualContractsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountAnnualContractsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountAnnualContractsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountAnnualContractsResponseMultiError, or nil if none found.
func (m *CountAnnualContractsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CountAnnualContractsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Count

	if len(errors) > 0 {
		return CountAnnualContractsResponseMultiError(errors)
	}

	return nil
}

// CountAnnualContractsResponseMultiError is an error wrapping multiple
// validation errors returned by CountAnnualContractsResponse.ValidateAll() if
// the designated constraints aren't met.
type CountAnnualContractsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountAnnualContractsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountAnnualContractsResponseMultiError) AllErrors() []error { return m }

// CountAnnualContractsResponseValidationError is the validation error returned
// by CountAnnualContractsResponse.Validate if the designated constraints
// aren't met.
type CountAnnualContractsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountAnnualContractsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountAnnualContractsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountAnnualContractsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountAnnualContractsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountAnnualContractsResponseValidationError) ErrorName() string {
	return "CountAnnualContractsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CountAnnualContractsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountAnnualContractsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountAnnualContractsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountAnnualContractsResponseValidationError{}

// Validate checks the field values on SignAnnualContractRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SignAnnualContractRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignAnnualContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SignAnnualContractRequestMultiError, or nil if none found.
func (m *SignAnnualContractRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SignAnnualContractRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := SignAnnualContractRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if uri, err := url.Parse(m.GetSignatureUri()); err != nil {
		err = SignAnnualContractRequestValidationError{
			field:  "SignatureUri",
			reason: "value must be a valid URI",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	} else if !uri.IsAbs() {
		err := SignAnnualContractRequestValidationError{
			field:  "SignatureUri",
			reason: "value must be absolute",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SignAnnualContractRequestMultiError(errors)
	}

	return nil
}

// SignAnnualContractRequestMultiError is an error wrapping multiple validation
// errors returned by SignAnnualContractRequest.ValidateAll() if the
// designated constraints aren't met.
type SignAnnualContractRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignAnnualContractRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignAnnualContractRequestMultiError) AllErrors() []error { return m }

// SignAnnualContractRequestValidationError is the validation error returned by
// SignAnnualContractRequest.Validate if the designated constraints aren't met.
type SignAnnualContractRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignAnnualContractRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignAnnualContractRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignAnnualContractRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignAnnualContractRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignAnnualContractRequestValidationError) ErrorName() string {
	return "SignAnnualContractRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SignAnnualContractRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignAnnualContractRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignAnnualContractRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignAnnualContractRequestValidationError{}

// Validate checks the field values on SignAnnualContractResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SignAnnualContractResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignAnnualContractResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SignAnnualContractResponseMultiError, or nil if none found.
func (m *SignAnnualContractResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SignAnnualContractResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContract()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SignAnnualContractResponseValidationError{
					field:  "Contract",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SignAnnualContractResponseValidationError{
					field:  "Contract",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContract()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SignAnnualContractResponseValidationError{
				field:  "Contract",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SignAnnualContractResponseMultiError(errors)
	}

	return nil
}

// SignAnnualContractResponseMultiError is an error wrapping multiple
// validation errors returned by SignAnnualContractResponse.ValidateAll() if
// the designated constraints aren't met.
type SignAnnualContractResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignAnnualContractResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignAnnualContractResponseMultiError) AllErrors() []error { return m }

// SignAnnualContractResponseValidationError is the validation error returned
// by SignAnnualContractResponse.Validate if the designated constraints aren't met.
type SignAnnualContractResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignAnnualContractResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignAnnualContractResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignAnnualContractResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignAnnualContractResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignAnnualContractResponseValidationError) ErrorName() string {
	return "SignAnnualContractResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SignAnnualContractResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignAnnualContractResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignAnnualContractResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignAnnualContractResponseValidationError{}

// Validate checks the field values on DeleteAnnualContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAnnualContractRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAnnualContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAnnualContractRequestMultiError, or nil if none found.
func (m *DeleteAnnualContractRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAnnualContractRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := DeleteAnnualContractRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteAnnualContractRequestMultiError(errors)
	}

	return nil
}

// DeleteAnnualContractRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteAnnualContractRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteAnnualContractRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAnnualContractRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAnnualContractRequestMultiError) AllErrors() []error { return m }

// DeleteAnnualContractRequestValidationError is the validation error returned
// by DeleteAnnualContractRequest.Validate if the designated constraints
// aren't met.
type DeleteAnnualContractRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAnnualContractRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAnnualContractRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAnnualContractRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAnnualContractRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAnnualContractRequestValidationError) ErrorName() string {
	return "DeleteAnnualContractRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAnnualContractRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAnnualContractRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAnnualContractRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAnnualContractRequestValidationError{}

// Validate checks the field values on AnnualContract with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AnnualContract) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnualContract with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AnnualContractMultiError,
// or nil if none found.
func (m *AnnualContract) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnualContract) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TemplateId

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnualContractValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnualContractValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnualContractValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnualContractValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnualContractValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnualContractValidationError{
				field:  "Parameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Content

	// no validation rules for Creator

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnualContractValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnualContractValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnualContractValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnualContractValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnualContractValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnualContractValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.SignTime != nil {

		if all {
			switch v := interface{}(m.GetSignTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnnualContractValidationError{
						field:  "SignTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnnualContractValidationError{
						field:  "SignTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSignTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnnualContractValidationError{
					field:  "SignTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.SignatureUri != nil {
		// no validation rules for SignatureUri
	}

	if len(errors) > 0 {
		return AnnualContractMultiError(errors)
	}

	return nil
}

// AnnualContractMultiError is an error wrapping multiple validation errors
// returned by AnnualContract.ValidateAll() if the designated constraints
// aren't met.
type AnnualContractMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnualContractMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnualContractMultiError) AllErrors() []error { return m }

// AnnualContractValidationError is the validation error returned by
// AnnualContract.Validate if the designated constraints aren't met.
type AnnualContractValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnualContractValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnualContractValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnualContractValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnualContractValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnualContractValidationError) ErrorName() string { return "AnnualContractValidationError" }

// Error satisfies the builtin error interface
func (e AnnualContractValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnualContract.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnualContractValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnualContractValidationError{}

// Validate checks the field values on AnnualContractQueryFilters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnnualContractQueryFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnualContractQueryFilters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnnualContractQueryFiltersMultiError, or nil if none found.
func (m *AnnualContractQueryFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnualContractQueryFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CompanyId != nil {
		// no validation rules for CompanyId
	}

	if m.AccountId != nil {
		// no validation rules for AccountId
	}

	if m.OwnerEmail != nil {
		// no validation rules for OwnerEmail
	}

	if m.Creator != nil {
		// no validation rules for Creator
	}

	if m.Signed != nil {
		// no validation rules for Signed
	}

	if len(errors) > 0 {
		return AnnualContractQueryFiltersMultiError(errors)
	}

	return nil
}

// AnnualContractQueryFiltersMultiError is an error wrapping multiple
// validation errors returned by AnnualContractQueryFilters.ValidateAll() if
// the designated constraints aren't met.
type AnnualContractQueryFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnualContractQueryFiltersMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnualContractQueryFiltersMultiError) AllErrors() []error { return m }

// AnnualContractQueryFiltersValidationError is the validation error returned
// by AnnualContractQueryFilters.Validate if the designated constraints aren't met.
type AnnualContractQueryFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnualContractQueryFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnualContractQueryFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnualContractQueryFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnualContractQueryFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnualContractQueryFiltersValidationError) ErrorName() string {
	return "AnnualContractQueryFiltersValidationError"
}

// Error satisfies the builtin error interface
func (e AnnualContractQueryFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnualContractQueryFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnualContractQueryFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnualContractQueryFiltersValidationError{}

// Validate checks the field values on AnnualContract_Metadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnnualContract_Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnualContract_Metadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnnualContract_MetadataMultiError, or nil if none found.
func (m *AnnualContract_Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnualContract_Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for AccountId

	// no validation rules for SubscriptionPlan

	if len(errors) > 0 {
		return AnnualContract_MetadataMultiError(errors)
	}

	return nil
}

// AnnualContract_MetadataMultiError is an error wrapping multiple validation
// errors returned by AnnualContract_Metadata.ValidateAll() if the designated
// constraints aren't met.
type AnnualContract_MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnualContract_MetadataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnualContract_MetadataMultiError) AllErrors() []error { return m }

// AnnualContract_MetadataValidationError is the validation error returned by
// AnnualContract_Metadata.Validate if the designated constraints aren't met.
type AnnualContract_MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnualContract_MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnualContract_MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnualContract_MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnualContract_MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnualContract_MetadataValidationError) ErrorName() string {
	return "AnnualContract_MetadataValidationError"
}

// Error satisfies the builtin error interface
func (e AnnualContract_MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnualContract_Metadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnualContract_MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnualContract_MetadataValidationError{}

// Validate checks the field values on AnnualContract_Parameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnnualContract_Parameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnualContract_Parameters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnnualContract_ParametersMultiError, or nil if none found.
func (m *AnnualContract_Parameters) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnualContract_Parameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyName

	// no validation rules for OwnerName

	// no validation rules for OwnerEmail

	// no validation rules for Address

	// no validation rules for SubscriptionPlanName

	// no validation rules for SubscriptionTermMonths

	// no validation rules for DiscountPercentage

	// no validation rules for TotalAmount

	if m.GroomingLocation != nil {

		if all {
			switch v := interface{}(m.GetGroomingLocation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnnualContract_ParametersValidationError{
						field:  "GroomingLocation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnnualContract_ParametersValidationError{
						field:  "GroomingLocation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGroomingLocation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnnualContract_ParametersValidationError{
					field:  "GroomingLocation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.BdLocation != nil {

		if all {
			switch v := interface{}(m.GetBdLocation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnnualContract_ParametersValidationError{
						field:  "BdLocation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnnualContract_ParametersValidationError{
						field:  "BdLocation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBdLocation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnnualContract_ParametersValidationError{
					field:  "BdLocation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GroomingVan != nil {

		if all {
			switch v := interface{}(m.GetGroomingVan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AnnualContract_ParametersValidationError{
						field:  "GroomingVan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AnnualContract_ParametersValidationError{
						field:  "GroomingVan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGroomingVan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AnnualContract_ParametersValidationError{
					field:  "GroomingVan",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AnnualContract_ParametersMultiError(errors)
	}

	return nil
}

// AnnualContract_ParametersMultiError is an error wrapping multiple validation
// errors returned by AnnualContract_Parameters.ValidateAll() if the
// designated constraints aren't met.
type AnnualContract_ParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnualContract_ParametersMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnualContract_ParametersMultiError) AllErrors() []error { return m }

// AnnualContract_ParametersValidationError is the validation error returned by
// AnnualContract_Parameters.Validate if the designated constraints aren't met.
type AnnualContract_ParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnualContract_ParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnualContract_ParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnualContract_ParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnualContract_ParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnualContract_ParametersValidationError) ErrorName() string {
	return "AnnualContract_ParametersValidationError"
}

// Error satisfies the builtin error interface
func (e AnnualContract_ParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnualContract_Parameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnualContract_ParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnualContract_ParametersValidationError{}

// Validate checks the field values on AnnualContract_ProductLineItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnnualContract_ProductLineItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnualContract_ProductLineItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AnnualContract_ProductLineItemMultiError, or nil if none found.
func (m *AnnualContract_ProductLineItem) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnualContract_ProductLineItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Quantity

	// no validation rules for UnitPrice

	// no validation rules for DiscountedUnitPrice

	if len(errors) > 0 {
		return AnnualContract_ProductLineItemMultiError(errors)
	}

	return nil
}

// AnnualContract_ProductLineItemMultiError is an error wrapping multiple
// validation errors returned by AnnualContract_ProductLineItem.ValidateAll()
// if the designated constraints aren't met.
type AnnualContract_ProductLineItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnualContract_ProductLineItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnualContract_ProductLineItemMultiError) AllErrors() []error { return m }

// AnnualContract_ProductLineItemValidationError is the validation error
// returned by AnnualContract_ProductLineItem.Validate if the designated
// constraints aren't met.
type AnnualContract_ProductLineItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnualContract_ProductLineItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnualContract_ProductLineItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnualContract_ProductLineItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnualContract_ProductLineItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnualContract_ProductLineItemValidationError) ErrorName() string {
	return "AnnualContract_ProductLineItemValidationError"
}

// Error satisfies the builtin error interface
func (e AnnualContract_ProductLineItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnualContract_ProductLineItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnualContract_ProductLineItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnualContract_ProductLineItemValidationError{}
