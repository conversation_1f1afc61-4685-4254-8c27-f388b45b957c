plugins {
  id 'java'
  id 'jacoco'
  id 'org.springframework.boot' version "${springBootVersion}"
  id 'io.spring.dependency-management' version "${springDependencyManagementVersion}"
  id 'com.diffplug.spotless' version "${spotlessVersion}"
  id "com.github.spotbugs" version "${spotbugsVersion}"
  id "com.qqviaja.gradle.MybatisGenerator" version "${mybatisGeneratorGradlePlugin}"
  id 'net.razvan.jacoco-to-cobertura' version "${jacocoToCoberturaPlugin}"
}

repositories {
  mavenCentral()
}

dependencyManagement {
  imports {
    mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
  }
}

dependencies {

  implementation("com.moego:moego-server-common")
  implementation("com.moego:moego-server-api")
  implementation("com.moego.api:moego-api-java")
  implementation("com.moego.lib:moego-lib-common")
  implementation("com.moego.lib:moego-lib-permission")

  implementation "org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}"
  implementation 'com.mysql:mysql-connector-j'
  // aws jdbc driver
  implementation("software.amazon.jdbc:aws-advanced-jdbc-wrapper:2.5.3")
  // aws secrets manager
  implementation("io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:${springCloudAWSVersion}")

  implementation("org.mybatis.dynamic-sql:mybatis-dynamic-sql:${mybatisDynamicSqlVersion}")

  compileOnly 'org.projectlombok:lombok'
  compileOnly("com.github.spotbugs:spotbugs-annotations:4.7.3")
  annotationProcessor 'org.projectlombok:lombok'
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

  testImplementation 'org.springframework.boot:spring-boot-starter-test'

  implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-freemarker'
  implementation 'org.springframework.boot:spring-boot-starter-cache'
  implementation 'org.springframework.boot:spring-boot-starter-data-redis'

  //feign client add patch support
  implementation 'io.github.openfeign:feign-httpclient:13.2.1'

  implementation "io.grpc:grpc-services:${grpcVersion}"

  implementation 'com.mandrillapp.wrapper.lutung:lutung:0.0.8'

  implementation 'org.mapstruct:mapstruct:1.5.3.Final'

  annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'
  testAnnotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'

  annotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'
  testAnnotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'

  implementation "com.github.pagehelper:pagehelper-spring-boot-starter:${pagehelperSpringBootStarterVersion}"
}

compileJava {
  options.compilerArgs << '-parameters'
}

tasks.withType(JavaCompile) {
  options.encoding = 'UTF-8'
}

tasks.named("bootJar") {
  archivesBaseName = 'moego-server'
}

tasks.named('test') {
  useJUnitPlatform()
  testLogging {
    events 'failed'
    exceptionFormat 'full'
  }
}

configurations {
  mybatisGenerator
}

mybatisGenerator {
  verbose = true
  configFile = 'src/main/resources/MyBatisGeneratorConfig.xml'

  dependencies {
    mybatisGenerator "org.mybatis.generator:mybatis-generator-core:${mybatisGeneratorCoreVersion}"
    mybatisGenerator files('libs/mybatis-generator-lombok-plugin-1.1-SNAPSHOT.jar')
    mybatisGenerator 'com.mysql:mysql-connector-j'
    mybatisGenerator("com.moego.lib:moego-lib-mybatis-plugins")
  }
}

spotless {
  encoding 'UTF-8'
  java {
    toggleOffOn()
    removeUnusedImports()
    trimTrailingWhitespace()
    endWithNewline()
    palantirJavaFormat()

    targetExclude "build/generated/**"
    custom('Refuse wildcard imports', {
      if (it =~ /\nimport .*\*;/) {
        throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
      }
    } as Closure<String>)
  }
}

spotbugs {
  spotbugsTest.enabled = false
  omitVisitors.addAll("FindReturnRef", "MethodReturnCheck", "DontReusePublicIdentifiers")
  excludeFilter.set(file("${rootDir}/config/spotbugs/exclude.xml"))
}

jacocoTestReport {
  reports {
    xml {
      required = true
      destination file("${rootProject.buildDir}/reports/jacoco/test/jacocoTestReport.xml")
    }
  }

  afterEvaluate {
    classDirectories.setFrom(files(classDirectories.files.collect {
      fileTree(dir: it, include: ["**/service/**", "**/mapstruct/**", "**/server/**"])
    }))
  }
}