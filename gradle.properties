group=com.moego
artifact=svc-organization

# spring
springBootVersion=3.1.4
springCloudVersion=2022.0.4
# https://github.com/awspring/spring-cloud-aws?tab=readme-ov-file#compatibility-with-spring-project-versions
springCloudAWSVersion=3.2.1
springDependencyManagementVersion=1.1.3

mybatisDynamicSqlVersion=1.5.2
mybatisBootStarterVersion=3.0.2
mybatisGeneratorCoreVersion=1.4.2
# https://plugins.gradle.org/plugin/com.qqviaja.gradle.MybatisGenerator
mybatisGeneratorGradlePlugin=2.5
pagehelperSpringBootStarterVersion=2.0.0
jacocoToCoberturaPlugin=1.2.0

grpcVersion=1.58.0
mapstructVersion=1.5.3.Final

# code quality
spotlessVersion=6.22.0
spotbugsVersion=5.2.2

org.gradle.jvmargs=-Xmx4g
org.gradle.caching=true
