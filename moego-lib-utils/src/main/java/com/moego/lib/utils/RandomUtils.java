package com.moego.lib.utils;

import java.security.SecureRandom;
import java.util.Random;

public class RandomUtils {

    /**
     * 纯大写字母
     */
    private static final char[] UPPER = {
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V',
        'W', 'X', 'Y', 'Z'
    };

    /**
     * 随机生成只包含大写字母的 length 位字符串
     * e.g. FHUVIA (length = 6)
     * @param length 随机字符串的长度
     * @return 长度为 length 的字符串
     */
    public static String randomUpperCharString(int length) {
        return randomString(UPPER, length);
    }

    /**
     * 纯小写字母
     */
    private static final char[] LOWER = {
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
        'w', 'x', 'y', 'z'
    };

    /**
     * 随机生成只包含小写字母的 length 位字符串
     * e.g. fhuvia (length = 6)
     * @param length 随机字符串的长度
     * @return 长度为 length 的字符串
     */
    public static String randomLowerCharString(int length) {
        return randomString(LOWER, length);
    }

    /**
     * 纯数字
     */
    private static final char[] NUMBER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};

    /**
     * 随机生成只包含数字的 length 位字符串
     * e.g. 328971 (length = 6)
     * @param length 随机字符串的长度
     * @return 长度为 length 的字符串
     */
    public static String randomNumString(int length) {
        return randomString(NUMBER, length);
    }

    /**
     * 大写字母+小写字母+数字
     */
    private static final char[] ALL = {
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V',
        'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r',
        's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
    };

    /**
     * 随机生成包含大写字母+小写字母+数字的 length 位字符串
     * e.g. 3Ae9iQ (length = 6)
     * @param length 随机字符串的长度
     * @return 长度为 length 的字符串
     */
    public static String randomCharAndNumString(int length) {
        return randomString(ALL, length);
    }

    /**
     * 小写字母+数字
     */
    private static final char[] LOWER_NUMBER = {
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
        'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
    };

    /**
     * 随机生成只包含小写字母+数字的 length 位字符串
     * e.g. a28u7t (length = 6)
     * @param length 随机字符串的长度
     * @return 长度为 length 的字符串
     */
    public static String randomLowerCharAndNumString(int length) {
        return randomString(LOWER_NUMBER, length);
    }

    /**
     * 大写字母+数字
     */
    private static final char[] UPPER_NUMBER = {
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V',
        'W', 'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
    };

    /**
     * 随机生成只包含大写字母+数字的 length 位字符串
     * e.g. A20UOT (length = 6)
     * @param length 随机字符串的长度
     * @return 长度为 length 的字符串
     */
    public static String randomUpperCharAndNumString(int length) {
        return randomString(UPPER_NUMBER, length);
    }

    /**
     * 大写字母+数字(排除数字0)，因为数字0和字母O容易混淆
     */
    private static final char[] UPPER_NUMBER_NZ = {
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V',
        'W', 'X', 'Y', 'Z', '1', '2', '3', '4', '5', '6', '7', '8', '9'
    };

    /**
     * 随机生成只包含大写字母+数字(排除数字0)的 length 位字符串
     * e.g. A28UOT (length = 6)
     * @param length 随机字符串的长度
     * @return 长度为 length 的字符串
     */
    public static String randomUpperCharAndNonzeroNumString(int length) {
        return randomString(UPPER_NUMBER_NZ, length);
    }

    private static final Random RANDOM = new SecureRandom();

    /**
     * 根据给定的字符集 charset, 随机生成长度为 length 的字符串
     * @param charset 字符集
     * @param length 随机字符串的长度
     * @return 长度为 length 的字符串
     */
    public static String randomString(char[] charset, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int number = RANDOM.nextInt(charset.length);
            sb.append(charset[number]);
        }
        return sb.toString();
    }

    /**
     * 随机生成 [0, upperBound) 的整数
     * @param upperBound 上界, exclusive
     * @return [0, upperBound) 的整数
     */
    public static Integer randomInt(int upperBound) {
        return RANDOM.nextInt(upperBound);
    }

    /**
     * 随机生成 [lowerBound, upperBound) 的整数
     * @param upperBound 上界, exclusive
     * @return [lowerBound, upperBound) 的整数
     */
    public static Integer randomInt(int lowerBound, int upperBound) {
        return RANDOM.nextInt(upperBound - lowerBound) + lowerBound;
    }
}
