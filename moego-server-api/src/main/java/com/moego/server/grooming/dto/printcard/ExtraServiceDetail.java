package com.moego.server.grooming.dto.printcard;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExtraServiceDetail {

    private String serviceName;

    private Integer serviceType;

    private List<String> specificDates;

    private Long startTime;

    private Long endTime;

    private String staffName;

    private Integer quantityPerDay;

    private Boolean requireDedicatedStaff;

    private Integer dateType;

    /*
     * 实际执行服务的日期，通过 DateType + SpecificDates 计算得到
     */
    private List<String> actualDates;
}
