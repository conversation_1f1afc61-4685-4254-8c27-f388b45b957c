package com.moego.server.message.api;

import com.moego.server.message.dto.MessageControlDTO;
import com.moego.server.message.params.MessageControlUpdateResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@Validated
public interface IMessageControlService {

    @GetMapping("/service/message/control/current")
    MessageControlDTO getCurrentMessageControl(@RequestParam Long companyId);

    /**
     * 1. Update message subscription amount caused by subscription plan change.
     * 2. or update message purchase amount caused by message pack purchasing.
     */
    @PutMapping("/service/message/control/count")
    MessageControlUpdateResponse updateMessageAmount(
            @RequestParam(value = "businessId", required = false) Integer businessId,
            @RequestParam(value = "companyId", required = false) Integer companyId,
            @RequestParam(value = "subscriptionAmount", required = false) Integer subscriptionAmount,
            @RequestParam(value = "purchaseAmount", required = false) Integer purchaseAmount,
            @RequestParam(value = "subscriptionEmailAmount", required = false) Integer subscriptionEmailAmount,
            @RequestParam(value = "purchaseEmailAmount", required = false) Integer purchaseEmailAmount);

    /**
     * Create new message control record in DB for new company
     *
     * @param subscriptionDate plan cycle start date, value in 1 ~ 31
     *                         new cycle created with time range: given date UTC 8am to the same day of next month UTC 8am
     */
    @PostMapping("/service/message/control/count")
    MessageControlUpdateResponse initialMessageControl(
            @RequestParam(value = "accountId", required = false) Integer accountId,
            @RequestParam(value = "companyId", required = false) Integer companyId,
            @RequestParam(value = "subscriptionAmount") Integer subscriptionAmount,
            @RequestParam(value = "subscriptionDate") Integer subscriptionDate,
            @RequestParam(value = "qualifyFlag", required = false) Boolean qualifyFlag,
            @RequestParam(value = "subscriptionEmailAmount", required = false) Integer subscriptionEmailAmount);

    @GetMapping("/service/message/control/left")
    Integer getLeftMessage(@RequestParam(value = "companyId") Integer companyId);

    @GetMapping("/service/message/control/includeInPlan")
    Integer getCurrentCycle(@RequestParam(value = "companyId") Integer companyId);

    /**
     * Increase message usage count.
     *
     * @param param {@link IncreaseUsageParam}
     * @return true if success
     */
    @PostMapping("/service/message/message-control/increase-usage")
    Boolean increaseUsage(@RequestBody @Valid IncreaseUsageParam param);

    /**
     * @param type {@link Type}
     */
    @Builder(toBuilder = true)
    record IncreaseUsageParam(
            @NotNull @Positive Integer companyId, @Positive int type, @NotNull @Positive Integer count) {

        @Getter
        @RequiredArgsConstructor
        public enum Type {
            TWO_WAY(1),
            AUTO(2),
            CALL(3);
            private final Integer value;
        }
    }
}
