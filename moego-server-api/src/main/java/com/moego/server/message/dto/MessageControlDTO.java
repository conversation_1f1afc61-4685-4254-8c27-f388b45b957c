package com.moego.server.message.dto;

import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class MessageControlDTO {
    Integer id;
    Integer companyId;
    Integer cycleBeginTime;
    Integer cycleEndTime;

    // email credit
    // availableEmails =
    // subscriptionEmailAmount + purchaseEmailAmount + purchaseEmailLeftover - usedEmails
    Integer availableEmails;
    Integer purchaseEmailAmount;
    Integer purchaseEmailLeftover;
    Integer subscriptionEmailAmount;
    Integer usedEmails;

    // sms credit
    // availableSms =
    // subscriptionAmount + purchaseAmount + purchaseLeftover - used2wayMessage - usedAutoMessage - usedCall
    Integer subscriptionAmount;
    Integer purchaseAmount;
    Integer purchaseLeftover;
    Integer used2wayMessage;
    Integer usedAutoMessage;
    Integer usedCall;
}
