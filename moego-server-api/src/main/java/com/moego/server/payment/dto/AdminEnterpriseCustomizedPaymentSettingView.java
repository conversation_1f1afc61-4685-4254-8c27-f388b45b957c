package com.moego.server.payment.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Builder
public class AdminEnterpriseCustomizedPaymentSettingView {

    private Long id;
    private Long accountId;
    private String email;
    private Long enterpriseId;
    private String enterpriseName;
    private Long customizedMinVol;
    private BigDecimal onlineFeeRate;
    private Integer onlineFeeCents;
    private BigDecimal readerFeeRate;
    private Integer readerFeeCents;
    private Date createTime;
    private Date updateTime;
}
