package com.moego.server.retail.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class ReportWebRetailSale {

    private Long createTime;
    private Integer customerId;
    private Integer businessId;
    private Integer staffId;
    private String saleType;
    private BigDecimal paymentAmount;
    // 净实收收入 = payment - convenienceFee - tax - tips - refund
    private BigDecimal netSaleRevenue;
}
