package com.moego.server.business.server;

import com.moego.server.business.api.IPayrollSettingServiceBase;
import com.moego.server.business.dto.BusinessPayrollSettingDTO;
import com.moego.server.business.dto.PayrollExceptionDTO;
import com.moego.server.business.dto.StaffPayrollSettingDTO;
import com.moego.server.business.service.PayrollSettingService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class PayrollSettingServer extends IPayrollSettingServiceBase {

    @Autowired
    private PayrollSettingService payrollSettingService;

    @Override
    public BusinessPayrollSettingDTO getBusinessPayrollSetting(Integer businessId) {
        return payrollSettingService.getBusinessPayrollSetting(businessId);
    }

    @Override
    public BusinessPayrollSettingDTO getBusinessPayrollSettingByCompanyId(@RequestParam("companyId") Long companyId) {
        return null;
    }

    @Override
    public List<StaffPayrollSettingDTO> getStaffPayrollSettingList(Integer businessId) {
        return payrollSettingService.getStaffPayrollSettingList(businessId, true);
    }

    @Override
    public List<StaffPayrollSettingDTO> getStaffPayrollSettingListByStaffIds(
            @RequestParam(value = "companyId", required = false) Long companyId, @RequestBody List<Integer> staffIds) {
        return payrollSettingService.getStaffPayrollSettingListByCompanyId(companyId, staffIds);
    }

    @Override
    public List<PayrollExceptionDTO> getPayrollExceptionList(Integer businessId) {
        return payrollSettingService.getPayrollExceptionList(businessId, true);
    }
}
