plugins {
  id 'org.springframework.boot'
  id "com.qqviaja.gradle.MybatisGenerator" version "${mybatisGeneratorGradlePlugin}"
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-jdbc'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-data-redis'
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
  implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
  implementation("io.grpc:grpc-services:${grpcVersion}")
  implementation 'com.github.pagehelper:pagehelper-spring-boot-starter:1.4.7'
  implementation 'software.amazon.awssdk:regions:2.26.20'
  implementation 'software.amazon.awssdk:s3:2.26.20'
  implementation 'com.google.apis:google-api-services-calendar:v3-rev411-1.25.0'
  implementation "com.intuit.quickbooks-online:ipp-v3-java-data:${quickbooksOnlineVersion}"
  implementation "com.intuit.quickbooks-online:ipp-v3-java-devkit:${quickbooksOnlineVersion}"
  implementation "com.intuit.quickbooks-online:oauth2-platform-api:${quickbooksOnlineVersion}"
  implementation 'com.google.analytics:google-analytics-data:0.16.0'
  implementation 'org.apache.xmlgraphics:batik-transcoder:1.16'
  implementation 'org.apache.xmlgraphics:batik-codec:1.16'

  implementation 'com.alibaba:easyexcel:3.3.1'

  implementation "org.mapstruct:mapstruct:${mapstructVersion}"
  annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
  testAnnotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"

  runtimeOnly 'com.mysql:mysql-connector-j'
  testImplementation 'org.springframework.boot:spring-boot-starter-test'
  testImplementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter-test:3.0.2'
  compileOnly 'org.projectlombok:lombok'
  annotationProcessor 'org.projectlombok:lombok'
  testCompileOnly 'org.projectlombok:lombok'
  testAnnotationProcessor 'org.projectlombok:lombok'
  annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
  implementation 'io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:3.2.1'
  // aws jdbc driver
  implementation("software.amazon.jdbc:aws-advanced-jdbc-wrapper:2.5.3")
}

sourceSets.main.resources.srcDirs = ['src/main/java', 'src/main/resources']

bootJar {
  archivesBaseName = 'moego-server'
  version = ''
  duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
bootRun {
  jvmArgs "--add-opens", "java.base/sun.net=ALL-UNNAMED"
}

configurations {
  mybatisGenerator
}

mybatisGenerator {
  verbose = true
  configFile = 'moego-server-grooming/src/main/resources/MyBatisGeneratorConfig.xml'
  dependencies {
    mybatisGenerator "org.mybatis.generator:mybatis-generator-core:${mybatisGeneratorVersion}"
    mybatisGenerator "com.mysql:mysql-connector-j"
    mybatisGenerator "com.moego.lib:moego-lib-mybatis-plugins"
  }
}

// mbGenerator needs moego-lib-mybatis-plugins.jar, so we need to make sure it is built before mbGenerator
mbGenerator.dependsOn gradle.includedBuild("moego-java-lib").task(":moego-lib-mybatis-plugins:jar")

configurations {
  configureEach {
    // prefer spring-jcl
    exclude group: 'commons-logging', module: 'commons-logging'
  }
}
