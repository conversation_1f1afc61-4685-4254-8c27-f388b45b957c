package com.moego.server.grooming.convert;

import com.moego.server.grooming.params.ss.SmartScheduleRequest;
import com.moego.server.grooming.web.vo.ob.SmartScheduleV2Request;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface SmartScheduleRequestConverter {

    SmartScheduleRequestConverter INSTANCE = Mappers.getMapper(SmartScheduleRequestConverter.class);

    SmartScheduleV2Request toSmartScheduleV2Request(SmartScheduleRequest smartScheduleRequest);
}
