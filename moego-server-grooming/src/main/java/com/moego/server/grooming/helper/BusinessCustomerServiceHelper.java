package com.moego.server.grooming.helper;

import static java.util.stream.Collectors.toList;

import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListCustomerAddressRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.customer.api.ICustomerContactService;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.customer.api.IProfileRequestAddressService;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.ProfileRequestAddressDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.grooming.api.IBookOnlineQuestionService;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.mapstruct.BusinessCustomerConverter;
import com.moego.server.grooming.service.utils.ProfileConflictUtils;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * 从 package com.moego.api.v3.online_booking.service.BusinessCustomerService; 迁移过来部分方法
 */
@Component
@RequiredArgsConstructor
public class BusinessCustomerServiceHelper {
    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub
            businessCustomerAddressServiceBlockingStub;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerServiceBlockingStub;
    private final ICustomerCustomerClient iCustomerCustomerClient;
    private final IPetClient iPetClient;
    private final ICustomerProfileRequestService customerProfileRequestService;
    private final ICustomerContactService customerContactService;
    private final IProfileRequestAddressService profileRequestAddressApi;
    private final IBookOnlineQuestionService questionService;

    @Builder
    public record CustomerHasRequestDTO(
            /**
             * Business existing customer profile.
             */
            CustomerProfileRequestDTO existingProfile,
            /**
             * Customer profile request.
             */
            CustomerProfileRequestDTO profileRequest,
            /**
             * Merged customer profile.
             * conflict fields will be overwritten by profile request.
             */
            CustomerProfileRequestDTO mergedProfile,
            /**
             * Has request update.
             */
            boolean hasRequestUpdate) {}

    /**
     * List customer has profile request review updates
     *
     * @param businessId     business id
     * @param customerIdList customer id list
     * @return customer id - profile request and review update map
     */
    public Map<Long, CustomerHasRequestDTO> listCustomerHasRequestUpdate(
            Integer businessId, List<Long> customerIdList) {
        if (CollectionUtils.isEmpty(customerIdList)) {
            return Map.of();
        }
        List<Integer> customerIds = customerIdList.stream().map(Long::intValue).collect(toList());
        Map<Integer, CustomerProfileRequestDTO> profileRequestMap =
                customerProfileRequestService.getCustomerProfileRequest(businessId, customerIds).stream()
                        .collect(Collectors.toMap(
                                CustomerProfileRequestDTO::getCustomerId, Function.identity(), (v1, v2) -> v1));

        Map<Integer, List<ProfileRequestAddressDTO>> customerIdToProfileRequestAddresses =
                profileRequestAddressApi.listByCustomerIds(customerIds);

        Map<Integer, CustomerProfileRequestDTO> existingProfileMap = listCustomerProfile(businessId, customerIds);
        return customerIds.stream().collect(Collectors.toMap(Integer::longValue, customerId -> {
            CustomerProfileRequestDTO request = profileRequestMap.get(customerId);
            CustomerProfileRequestDTO existing = existingProfileMap.get(customerId);
            CustomerHasRequestDTO.CustomerHasRequestDTOBuilder builder = CustomerHasRequestDTO.builder()
                    .profileRequest(request)
                    .existingProfile(existing)
                    .hasRequestUpdate(false);
            boolean hasUpdate = hasDiff(customerIdToProfileRequestAddresses.get(customerId), customerId);

            if (Objects.isNull(request) || Objects.isNull(existing)) {
                return builder.mergedProfile(existing)
                        .hasRequestUpdate(hasUpdate)
                        .build();
            }

            builder.mergedProfile(ProfileConflictUtils.replaceConflictProfile(request, existing));

            hasUpdate = hasUpdate || ProfileConflictUtils.conflict(request.getClient(), existing.getClient());

            if (!CollectionUtils.isEmpty(request.getPets())) {
                Map<Integer, CustomerProfileRequestDTO.PetProfileDTO> requestPetMap = request.getPets().stream()
                        .collect(Collectors.toMap(
                                CustomerProfileRequestDTO.PetProfileDTO::getPetId, Function.identity()));
                boolean petConflict = existing.getPets().stream()
                        .anyMatch(existingPet ->
                                ProfileConflictUtils.conflict(requestPetMap.get(existingPet.getPetId()), existingPet));
                hasUpdate = hasUpdate || petConflict;
            }

            builder.hasRequestUpdate(hasUpdate);

            return builder.build();
        }));
    }

    public Map<Integer, CustomerProfileRequestDTO> listCustomerProfile(Integer businessId, List<Integer> customerIds) {
        CompletableFuture<List<MoeBusinessCustomerDTO>> customersFuture = CompletableFuture.supplyAsync(
                () -> {
                    CustomerIdListParams params = new CustomerIdListParams();
                    params.setIdList(customerIds);
                    return iCustomerCustomerClient.queryCustomerList(params);
                },
                ThreadPool.getSubmitExecutor());
        CompletableFuture<List<CustomerContactDto>> contactsFuture = CompletableFuture.supplyAsync(
                () -> customerContactService.listCustomerOwnerPhone(businessId, customerIds),
                ThreadPool.getSubmitExecutor());
        CompletableFuture<List<CustomerPetDetailDTO>> petsFuture = CompletableFuture.supplyAsync(
                () -> iPetClient.getAllPetListWithVaccine(businessId, customerIds), ThreadPool.getSubmitExecutor());
        CompletableFuture<List<BookOnlineQuestionSaveDTO>> questionsFuture = CompletableFuture.supplyAsync(
                () -> questionService.listCustomerLatestQuestionSave(businessId, customerIds),
                ThreadPool.getSubmitExecutor());
        CompletableFuture.allOf(customersFuture, contactsFuture, petsFuture, questionsFuture)
                .join();
        List<MoeBusinessCustomerDTO> customerDTOList = customersFuture.join();
        List<CustomerContactDto> contactDTOList = contactsFuture.join();
        List<CustomerPetDetailDTO> petDetailDTOList = petsFuture.join();
        List<BookOnlineQuestionSaveDTO> questionSaveDTOList = questionsFuture.join();
        Map<Integer, MoeBusinessCustomerDTO> customerMap = customerDTOList.stream()
                .collect(Collectors.toMap(MoeBusinessCustomerDTO::getCustomerId, Function.identity()));
        Map<Integer, CustomerContactDto> ownerPhoneMap = contactDTOList.stream()
                .collect(Collectors.toMap(CustomerContactDto::getCustomerId, Function.identity()));
        Map<Integer, List<CustomerPetDetailDTO>> customerPetsMap =
                petDetailDTOList.stream().collect(Collectors.groupingBy(CustomerPetDetailDTO::getCustomerId));
        Map<Integer, BookOnlineQuestionSaveDTO> customerQuestionMap = questionSaveDTOList.stream()
                .collect(Collectors.toMap(BookOnlineQuestionSaveDTO::getCustomerId, Function.identity()));
        return customerIds.stream().collect(Collectors.toMap(customerId -> customerId, customerId -> {
            CustomerProfileRequestDTO.CustomerProfileRequestDTOBuilder builder = CustomerProfileRequestDTO.builder()
                    .businessId(businessId)
                    .customerId(customerId)
                    .pets(List.of());
            MoeBusinessCustomerDTO customerDTO = customerMap.get(customerId);
            if (Objects.isNull(customerDTO)) {
                return builder.build();
            }
            BookOnlineQuestionSaveDTO questionSaveDTO = customerQuestionMap.get(customerId);
            CustomerProfileRequestDTO.ClientProfileDTO client =
                    BusinessCustomerConverter.INSTANCE.dto2ClientProfileDTO(customerDTO);
            CustomerContactDto ownerPhone = ownerPhoneMap.get(customerId);
            client.setPhoneNumber(Objects.nonNull(ownerPhone) ? ownerPhone.getPhoneNumber() : null);
            builder.client(client);
            if (Objects.nonNull(questionSaveDTO)) {
                client.setCustomQuestions(questionSaveDTO.getClientCustomQuestionMap());
            }
            List<CustomerPetDetailDTO> petList = customerPetsMap.get(customerId);
            if (CollectionUtils.isEmpty(petList)) {
                return builder.build();
            }
            builder.pets(petList.stream()
                            .map(petDTO -> {
                                if (Objects.isNull(questionSaveDTO)) {
                                    return BusinessCustomerConverter.INSTANCE.dto2PetProfileDTO(petDTO);
                                }
                                return BusinessCustomerConverter.INSTANCE
                                        .dto2PetProfileDTO(petDTO)
                                        .setCustomQuestions(questionSaveDTO
                                                .getPetCustomQuestionMap()
                                                .get(petDTO.getPetId()));
                            })
                            .toList())
                    .build();
            return builder.build();
        }));
    }

    private boolean hasDiff(@Nullable List<ProfileRequestAddressDTO> profileRequestAddresses, Integer customerId) {
        if (ObjectUtils.isEmpty(profileRequestAddresses)) {
            return false;
        }

        boolean hasNewAddress = profileRequestAddresses.stream().anyMatch(e -> e.getCustomerAddressId() == null);
        if (hasNewAddress) {
            return true;
        }

        var listRequest = ListCustomerAddressRequest.newBuilder()
                .setCustomerId(customerId)
                .build();
        var existingAddresses = businessCustomerAddressServiceBlockingStub
                .listCustomerAddress(listRequest)
                .getAddressesList();
        if (ObjectUtils.isEmpty(existingAddresses)) {
            return true;
        }

        Map<Integer, ProfileRequestAddressDTO> addressIdToProfileRequestAddress = profileRequestAddresses.stream()
                .filter(e -> e.getCustomerAddressId() != null)
                .collect(Collectors.toMap(
                        ProfileRequestAddressDTO::getCustomerAddressId, Function.identity(), (o, n) -> o));

        return existingAddresses.stream().anyMatch(existingAddress -> {
            ProfileRequestAddressDTO updatedBean = addressIdToProfileRequestAddress.get((int) existingAddress.getId());
            return hasAddressUpdate(updatedBean, existingAddress);
        });
    }

    public static boolean hasAddressUpdate(
            ProfileRequestAddressDTO updatedBean, BusinessCustomerAddressModel existingAddress) {
        if (updatedBean == null) {
            return false;
        }
        // 经纬度比较复杂，单独拎出来比较
        if (StringUtils.hasText(updatedBean.getLat()) || StringUtils.hasText(updatedBean.getLng())) {
            // 入参有经纬度，但是 B 端 address 没有经纬度，需要更新
            if (!existingAddress.hasCoordinate()) {
                return true;
            }

            var coordinate = existingAddress.getCoordinate();
            // 入参有经纬度，但是 B 端 address 的经纬度和入参不一致，需要更新
            if (StringUtils.hasText(updatedBean.getLat())) {
                if (Double.parseDouble(updatedBean.getLat()) != coordinate.getLatitude()) {
                    return true;
                }
            }

            if (StringUtils.hasText(updatedBean.getLng())) {
                if (Double.parseDouble(updatedBean.getLng()) != coordinate.getLongitude()) {
                    return true;
                }
            }
        }

        return (StringUtils.hasText(updatedBean.getAddress1())
                        && !Objects.equals(updatedBean.getAddress1(), existingAddress.getAddress1()))
                || (StringUtils.hasText(updatedBean.getAddress2())
                        && !Objects.equals(updatedBean.getAddress2(), existingAddress.getAddress2()))
                || (StringUtils.hasText(updatedBean.getCity())
                        && !Objects.equals(updatedBean.getCity(), existingAddress.getCity()))
                || (StringUtils.hasText(updatedBean.getState())
                        && !Objects.equals(updatedBean.getState(), existingAddress.getState()))
                || (StringUtils.hasText(updatedBean.getZipcode())
                        && !Objects.equals(updatedBean.getZipcode(), existingAddress.getZipcode()))
                || (StringUtils.hasText(updatedBean.getCountry())
                        && !Objects.equals(updatedBean.getCountry(), existingAddress.getCountry()))
                || (updatedBean.getIsPrimary() != null
                        && !Objects.equals(updatedBean.getIsPrimary(), existingAddress.getIsPrimary()));
    }
}
