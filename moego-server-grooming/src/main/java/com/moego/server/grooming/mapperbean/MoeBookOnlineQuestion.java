package com.moego.server.grooming.mapperbean;

import com.moego.idl.models.online_booking.v1.ExistingClientAccessMode;
import com.moego.idl.models.online_booking.v1.ExistingPetAccessMode;
import com.moego.idl.models.online_booking.v1.NewClientAccessMode;
import com.moego.idl.models.online_booking.v1.NewPetAccessMode;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_question
 */
public class MoeBookOnlineQuestion {
    /**
     * Database Column Remarks:
     *   商家ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   表单id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   问题
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.question
     *
     * @mbg.generated
     */
    private String question;

    /**
     * Database Column Remarks:
     *   占位符
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.placeholder
     *
     * @mbg.generated
     */
    private String placeholder;

    /**
     * Database Column Remarks:
     *   是否显示
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.is_show
     *
     * @mbg.generated
     */
    private Byte isShow;

    /**
     * Database Column Remarks:
     *   是否必填
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.is_required
     *
     * @mbg.generated
     */
    private Byte isRequired;

    /**
     * Database Column Remarks:
     *   1 For pets   2 For pet owners
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     * Database Column Remarks:
     *   是否允许删除0不允许  1允许
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.is_allow_delete
     *
     * @mbg.generated
     */
    private Byte isAllowDelete;

    /**
     * Database Column Remarks:
     *   是否允许改变状态 0不允许  1允许
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.is_allow_change
     *
     * @mbg.generated
     */
    private Byte isAllowChange;

    /**
     * Database Column Remarks:
     *   是否允许编辑 0   1
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.is_allow_edit
     *
     * @mbg.generated
     */
    private Byte isAllowEdit;

    /**
     * Database Column Remarks:
     *   排序
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     * Database Column Remarks:
     *   状态：1 正常 0 删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   1: short txt 2:long txt 3:drop down 4: radio 5: check boxex
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.question_type
     *
     * @mbg.generated
     */
    private Byte questionType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   Access mode when creating a new pet, see NewPetAccessMode
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.new_pet_access_mode
     *
     * @mbg.generated
     */
    private NewPetAccessMode newPetAccessMode;

    /**
     * Database Column Remarks:
     *   Access mode when managing an existing pet, see ExistingPetAccessMode
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.existing_pet_access_mode
     *
     * @mbg.generated
     */
    private ExistingPetAccessMode existingPetAccessMode;

    /**
     * Database Column Remarks:
     *   Access mode when creating a new customer, see NewClientAccessMode
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.new_client_access_mode
     *
     * @mbg.generated
     */
    private NewClientAccessMode newClientAccessMode;

    /**
     * Database Column Remarks:
     *   Access mode when managing an existing customer, see ExistingClientAccessMode
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.existing_client_access_mode
     *
     * @mbg.generated
     */
    private ExistingClientAccessMode existingClientAccessMode;

    /**
     * Database Column Remarks:
     *   schema 格式化字符串
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_question.extra_json
     *
     * @mbg.generated
     */
    private String extraJson;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.id
     *
     * @return the value of moe_book_online_question.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.id
     *
     * @param id the value for moe_book_online_question.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.business_id
     *
     * @return the value of moe_book_online_question.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.business_id
     *
     * @param businessId the value for moe_book_online_question.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.question
     *
     * @return the value of moe_book_online_question.question
     *
     * @mbg.generated
     */
    public String getQuestion() {
        return question;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.question
     *
     * @param question the value for moe_book_online_question.question
     *
     * @mbg.generated
     */
    public void setQuestion(String question) {
        this.question = question == null ? null : question.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.placeholder
     *
     * @return the value of moe_book_online_question.placeholder
     *
     * @mbg.generated
     */
    public String getPlaceholder() {
        return placeholder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.placeholder
     *
     * @param placeholder the value for moe_book_online_question.placeholder
     *
     * @mbg.generated
     */
    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder == null ? null : placeholder.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.is_show
     *
     * @return the value of moe_book_online_question.is_show
     *
     * @mbg.generated
     */
    public Byte getIsShow() {
        return isShow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.is_show
     *
     * @param isShow the value for moe_book_online_question.is_show
     *
     * @mbg.generated
     */
    public void setIsShow(Byte isShow) {
        this.isShow = isShow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.is_required
     *
     * @return the value of moe_book_online_question.is_required
     *
     * @mbg.generated
     */
    public Byte getIsRequired() {
        return isRequired;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.is_required
     *
     * @param isRequired the value for moe_book_online_question.is_required
     *
     * @mbg.generated
     */
    public void setIsRequired(Byte isRequired) {
        this.isRequired = isRequired;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.type
     *
     * @return the value of moe_book_online_question.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.type
     *
     * @param type the value for moe_book_online_question.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.is_allow_delete
     *
     * @return the value of moe_book_online_question.is_allow_delete
     *
     * @mbg.generated
     */
    public Byte getIsAllowDelete() {
        return isAllowDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.is_allow_delete
     *
     * @param isAllowDelete the value for moe_book_online_question.is_allow_delete
     *
     * @mbg.generated
     */
    public void setIsAllowDelete(Byte isAllowDelete) {
        this.isAllowDelete = isAllowDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.is_allow_change
     *
     * @return the value of moe_book_online_question.is_allow_change
     *
     * @mbg.generated
     */
    public Byte getIsAllowChange() {
        return isAllowChange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.is_allow_change
     *
     * @param isAllowChange the value for moe_book_online_question.is_allow_change
     *
     * @mbg.generated
     */
    public void setIsAllowChange(Byte isAllowChange) {
        this.isAllowChange = isAllowChange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.is_allow_edit
     *
     * @return the value of moe_book_online_question.is_allow_edit
     *
     * @mbg.generated
     */
    public Byte getIsAllowEdit() {
        return isAllowEdit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.is_allow_edit
     *
     * @param isAllowEdit the value for moe_book_online_question.is_allow_edit
     *
     * @mbg.generated
     */
    public void setIsAllowEdit(Byte isAllowEdit) {
        this.isAllowEdit = isAllowEdit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.sort
     *
     * @return the value of moe_book_online_question.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.sort
     *
     * @param sort the value for moe_book_online_question.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.status
     *
     * @return the value of moe_book_online_question.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.status
     *
     * @param status the value for moe_book_online_question.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.create_time
     *
     * @return the value of moe_book_online_question.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.create_time
     *
     * @param createTime the value for moe_book_online_question.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.update_time
     *
     * @return the value of moe_book_online_question.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.update_time
     *
     * @param updateTime the value for moe_book_online_question.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.question_type
     *
     * @return the value of moe_book_online_question.question_type
     *
     * @mbg.generated
     */
    public Byte getQuestionType() {
        return questionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.question_type
     *
     * @param questionType the value for moe_book_online_question.question_type
     *
     * @mbg.generated
     */
    public void setQuestionType(Byte questionType) {
        this.questionType = questionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.company_id
     *
     * @return the value of moe_book_online_question.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.company_id
     *
     * @param companyId the value for moe_book_online_question.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.new_pet_access_mode
     *
     * @return the value of moe_book_online_question.new_pet_access_mode
     *
     * @mbg.generated
     */
    public NewPetAccessMode getNewPetAccessMode() {
        return newPetAccessMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.new_pet_access_mode
     *
     * @param newPetAccessMode the value for moe_book_online_question.new_pet_access_mode
     *
     * @mbg.generated
     */
    public void setNewPetAccessMode(NewPetAccessMode newPetAccessMode) {
        this.newPetAccessMode = newPetAccessMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.existing_pet_access_mode
     *
     * @return the value of moe_book_online_question.existing_pet_access_mode
     *
     * @mbg.generated
     */
    public ExistingPetAccessMode getExistingPetAccessMode() {
        return existingPetAccessMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.existing_pet_access_mode
     *
     * @param existingPetAccessMode the value for moe_book_online_question.existing_pet_access_mode
     *
     * @mbg.generated
     */
    public void setExistingPetAccessMode(ExistingPetAccessMode existingPetAccessMode) {
        this.existingPetAccessMode = existingPetAccessMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.new_client_access_mode
     *
     * @return the value of moe_book_online_question.new_client_access_mode
     *
     * @mbg.generated
     */
    public NewClientAccessMode getNewClientAccessMode() {
        return newClientAccessMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.new_client_access_mode
     *
     * @param newClientAccessMode the value for moe_book_online_question.new_client_access_mode
     *
     * @mbg.generated
     */
    public void setNewClientAccessMode(NewClientAccessMode newClientAccessMode) {
        this.newClientAccessMode = newClientAccessMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.existing_client_access_mode
     *
     * @return the value of moe_book_online_question.existing_client_access_mode
     *
     * @mbg.generated
     */
    public ExistingClientAccessMode getExistingClientAccessMode() {
        return existingClientAccessMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.existing_client_access_mode
     *
     * @param existingClientAccessMode the value for moe_book_online_question.existing_client_access_mode
     *
     * @mbg.generated
     */
    public void setExistingClientAccessMode(ExistingClientAccessMode existingClientAccessMode) {
        this.existingClientAccessMode = existingClientAccessMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_question.extra_json
     *
     * @return the value of moe_book_online_question.extra_json
     *
     * @mbg.generated
     */
    public String getExtraJson() {
        return extraJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_question.extra_json
     *
     * @param extraJson the value for moe_book_online_question.extra_json
     *
     * @mbg.generated
     */
    public void setExtraJson(String extraJson) {
        this.extraJson = extraJson == null ? null : extraJson.trim();
    }
}
