<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineQuestionMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="question" jdbcType="VARCHAR" property="question" />
    <result column="placeholder" jdbcType="VARCHAR" property="placeholder" />
    <result column="is_show" jdbcType="TINYINT" property="isShow" />
    <result column="is_required" jdbcType="TINYINT" property="isRequired" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="is_allow_delete" jdbcType="TINYINT" property="isAllowDelete" />
    <result column="is_allow_change" jdbcType="TINYINT" property="isAllowChange" />
    <result column="is_allow_edit" jdbcType="TINYINT" property="isAllowEdit" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="question_type" jdbcType="TINYINT" property="questionType" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="new_pet_access_mode" jdbcType="TINYINT" property="newPetAccessMode" typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler" />
    <result column="existing_pet_access_mode" jdbcType="TINYINT" property="existingPetAccessMode" typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler" />
    <result column="new_client_access_mode" jdbcType="TINYINT" property="newClientAccessMode" typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler" />
    <result column="existing_client_access_mode" jdbcType="TINYINT" property="existingClientAccessMode" typeHandler="com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.newPetAccessModeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.existingPetAccessModeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.newClientAccessModeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.existingClientAccessModeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.newPetAccessModeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.existingPetAccessModeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.newClientAccessModeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.existingClientAccessModeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, question, placeholder, is_show, is_required, type, is_allow_delete, 
    is_allow_change, is_allow_edit, sort, status, create_time, update_time, question_type, 
    company_id, new_pet_access_mode, existing_pet_access_mode, new_client_access_mode, 
    existing_client_access_mode
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    extra_json
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_book_online_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_question
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_question
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_question (business_id, question, placeholder, 
      is_show, is_required, type, 
      is_allow_delete, is_allow_change, is_allow_edit, 
      sort, status, create_time, 
      update_time, question_type, company_id, 
      new_pet_access_mode, 
      existing_pet_access_mode, 
      new_client_access_mode, 
      existing_client_access_mode, 
      extra_json)
    values (#{businessId,jdbcType=INTEGER}, #{question,jdbcType=VARCHAR}, #{placeholder,jdbcType=VARCHAR}, 
      #{isShow,jdbcType=TINYINT}, #{isRequired,jdbcType=TINYINT}, #{type,jdbcType=TINYINT}, 
      #{isAllowDelete,jdbcType=TINYINT}, #{isAllowChange,jdbcType=TINYINT}, #{isAllowEdit,jdbcType=TINYINT}, 
      #{sort,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{questionType,jdbcType=TINYINT}, #{companyId,jdbcType=BIGINT}, 
      #{newPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler}, 
      #{existingPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler}, 
      #{newClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler}, 
      #{existingClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler}, 
      #{extraJson,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_question
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="question != null">
        question,
      </if>
      <if test="placeholder != null">
        placeholder,
      </if>
      <if test="isShow != null">
        is_show,
      </if>
      <if test="isRequired != null">
        is_required,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="isAllowDelete != null">
        is_allow_delete,
      </if>
      <if test="isAllowChange != null">
        is_allow_change,
      </if>
      <if test="isAllowEdit != null">
        is_allow_edit,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="questionType != null">
        question_type,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="newPetAccessMode != null">
        new_pet_access_mode,
      </if>
      <if test="existingPetAccessMode != null">
        existing_pet_access_mode,
      </if>
      <if test="newClientAccessMode != null">
        new_client_access_mode,
      </if>
      <if test="existingClientAccessMode != null">
        existing_client_access_mode,
      </if>
      <if test="extraJson != null">
        extra_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="question != null">
        #{question,jdbcType=VARCHAR},
      </if>
      <if test="placeholder != null">
        #{placeholder,jdbcType=VARCHAR},
      </if>
      <if test="isShow != null">
        #{isShow,jdbcType=TINYINT},
      </if>
      <if test="isRequired != null">
        #{isRequired,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="isAllowDelete != null">
        #{isAllowDelete,jdbcType=TINYINT},
      </if>
      <if test="isAllowChange != null">
        #{isAllowChange,jdbcType=TINYINT},
      </if>
      <if test="isAllowEdit != null">
        #{isAllowEdit,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="questionType != null">
        #{questionType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="newPetAccessMode != null">
        #{newPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler},
      </if>
      <if test="existingPetAccessMode != null">
        #{existingPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler},
      </if>
      <if test="newClientAccessMode != null">
        #{newClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler},
      </if>
      <if test="existingClientAccessMode != null">
        #{existingClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler},
      </if>
      <if test="extraJson != null">
        #{extraJson,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_book_online_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_question
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=INTEGER},
      </if>
      <if test="row.question != null">
        question = #{row.question,jdbcType=VARCHAR},
      </if>
      <if test="row.placeholder != null">
        placeholder = #{row.placeholder,jdbcType=VARCHAR},
      </if>
      <if test="row.isShow != null">
        is_show = #{row.isShow,jdbcType=TINYINT},
      </if>
      <if test="row.isRequired != null">
        is_required = #{row.isRequired,jdbcType=TINYINT},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=TINYINT},
      </if>
      <if test="row.isAllowDelete != null">
        is_allow_delete = #{row.isAllowDelete,jdbcType=TINYINT},
      </if>
      <if test="row.isAllowChange != null">
        is_allow_change = #{row.isAllowChange,jdbcType=TINYINT},
      </if>
      <if test="row.isAllowEdit != null">
        is_allow_edit = #{row.isAllowEdit,jdbcType=TINYINT},
      </if>
      <if test="row.sort != null">
        sort = #{row.sort,jdbcType=INTEGER},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=BIGINT},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=BIGINT},
      </if>
      <if test="row.questionType != null">
        question_type = #{row.questionType,jdbcType=TINYINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.newPetAccessMode != null">
        new_pet_access_mode = #{row.newPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler},
      </if>
      <if test="row.existingPetAccessMode != null">
        existing_pet_access_mode = #{row.existingPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler},
      </if>
      <if test="row.newClientAccessMode != null">
        new_client_access_mode = #{row.newClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler},
      </if>
      <if test="row.existingClientAccessMode != null">
        existing_client_access_mode = #{row.existingClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler},
      </if>
      <if test="row.extraJson != null">
        extra_json = #{row.extraJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_question
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      question = #{row.question,jdbcType=VARCHAR},
      placeholder = #{row.placeholder,jdbcType=VARCHAR},
      is_show = #{row.isShow,jdbcType=TINYINT},
      is_required = #{row.isRequired,jdbcType=TINYINT},
      type = #{row.type,jdbcType=TINYINT},
      is_allow_delete = #{row.isAllowDelete,jdbcType=TINYINT},
      is_allow_change = #{row.isAllowChange,jdbcType=TINYINT},
      is_allow_edit = #{row.isAllowEdit,jdbcType=TINYINT},
      sort = #{row.sort,jdbcType=INTEGER},
      status = #{row.status,jdbcType=TINYINT},
      create_time = #{row.createTime,jdbcType=BIGINT},
      update_time = #{row.updateTime,jdbcType=BIGINT},
      question_type = #{row.questionType,jdbcType=TINYINT},
      company_id = #{row.companyId,jdbcType=BIGINT},
      new_pet_access_mode = #{row.newPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler},
      existing_pet_access_mode = #{row.existingPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler},
      new_client_access_mode = #{row.newClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler},
      existing_client_access_mode = #{row.existingClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler},
      extra_json = #{row.extraJson,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_question
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      question = #{row.question,jdbcType=VARCHAR},
      placeholder = #{row.placeholder,jdbcType=VARCHAR},
      is_show = #{row.isShow,jdbcType=TINYINT},
      is_required = #{row.isRequired,jdbcType=TINYINT},
      type = #{row.type,jdbcType=TINYINT},
      is_allow_delete = #{row.isAllowDelete,jdbcType=TINYINT},
      is_allow_change = #{row.isAllowChange,jdbcType=TINYINT},
      is_allow_edit = #{row.isAllowEdit,jdbcType=TINYINT},
      sort = #{row.sort,jdbcType=INTEGER},
      status = #{row.status,jdbcType=TINYINT},
      create_time = #{row.createTime,jdbcType=BIGINT},
      update_time = #{row.updateTime,jdbcType=BIGINT},
      question_type = #{row.questionType,jdbcType=TINYINT},
      company_id = #{row.companyId,jdbcType=BIGINT},
      new_pet_access_mode = #{row.newPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler},
      existing_pet_access_mode = #{row.existingPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler},
      new_client_access_mode = #{row.newClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler},
      existing_client_access_mode = #{row.existingClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_question
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="question != null">
        question = #{question,jdbcType=VARCHAR},
      </if>
      <if test="placeholder != null">
        placeholder = #{placeholder,jdbcType=VARCHAR},
      </if>
      <if test="isShow != null">
        is_show = #{isShow,jdbcType=TINYINT},
      </if>
      <if test="isRequired != null">
        is_required = #{isRequired,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="isAllowDelete != null">
        is_allow_delete = #{isAllowDelete,jdbcType=TINYINT},
      </if>
      <if test="isAllowChange != null">
        is_allow_change = #{isAllowChange,jdbcType=TINYINT},
      </if>
      <if test="isAllowEdit != null">
        is_allow_edit = #{isAllowEdit,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="questionType != null">
        question_type = #{questionType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="newPetAccessMode != null">
        new_pet_access_mode = #{newPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler},
      </if>
      <if test="existingPetAccessMode != null">
        existing_pet_access_mode = #{existingPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler},
      </if>
      <if test="newClientAccessMode != null">
        new_client_access_mode = #{newClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler},
      </if>
      <if test="existingClientAccessMode != null">
        existing_client_access_mode = #{existingClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler},
      </if>
      <if test="extraJson != null">
        extra_json = #{extraJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_question
    set business_id = #{businessId,jdbcType=INTEGER},
      question = #{question,jdbcType=VARCHAR},
      placeholder = #{placeholder,jdbcType=VARCHAR},
      is_show = #{isShow,jdbcType=TINYINT},
      is_required = #{isRequired,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT},
      is_allow_delete = #{isAllowDelete,jdbcType=TINYINT},
      is_allow_change = #{isAllowChange,jdbcType=TINYINT},
      is_allow_edit = #{isAllowEdit,jdbcType=TINYINT},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      question_type = #{questionType,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=BIGINT},
      new_pet_access_mode = #{newPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler},
      existing_pet_access_mode = #{existingPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler},
      new_client_access_mode = #{newClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler},
      existing_client_access_mode = #{existingClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler},
      extra_json = #{extraJson,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_question
    set business_id = #{businessId,jdbcType=INTEGER},
      question = #{question,jdbcType=VARCHAR},
      placeholder = #{placeholder,jdbcType=VARCHAR},
      is_show = #{isShow,jdbcType=TINYINT},
      is_required = #{isRequired,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT},
      is_allow_delete = #{isAllowDelete,jdbcType=TINYINT},
      is_allow_change = #{isAllowChange,jdbcType=TINYINT},
      is_allow_edit = #{isAllowEdit,jdbcType=TINYINT},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      question_type = #{questionType,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=BIGINT},
      new_pet_access_mode = #{newPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler},
      existing_pet_access_mode = #{existingPetAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler},
      new_client_access_mode = #{newClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler},
      existing_client_access_mode = #{existingClientAccessMode,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="getListByBusinessId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_question
    where business_id = #{businessId,jdbcType=INTEGER}
    <if test="type != null">
      and type = #{type}
    </if>
    and status = 1
    order by sort desc ,id
  </select>
  <select id="getShowQuestionsByBusinessId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_question
    where business_id = #{businessId,jdbcType=INTEGER}
    and is_show = 1
    and status = 1
    <if test="type != null">
      and type = #{type}
    </if>
    order by sort desc ,id
  </select>
    <update id="sortMoeQuestion">
        <foreach collection="sortDtos" item="item" separator=";">
            update moe_book_online_question set sort = #{item.sort} where id = #{item.id}
        </foreach>
    </update>
    <select id="getCountWithName" resultType="java.lang.Integer">
        SELECT count(*) as count FROM moe_book_online_question
        WHERE business_id = #{businessId}
        AND status = 1
        AND question = #{name}
        <if test="updateId != null">
            AND id != #{updateId}
        </if>
      AND type = #{type}
    </select>

  <select id="getNextBusinessId" resultType="java.lang.Integer">
    select business_id
    from moe_book_online_question
    where business_id &gt; #{businessId}
    order by business_id asc
    limit 1
  </select>
</mapper>