package com.moego.server.grooming.mapstruct;

import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/7/10
 */
@Mapper(
        uses = {BaseMapper.class},
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BusinessCustomerConverter {
    BusinessCustomerConverter INSTANCE = Mappers.getMapper(BusinessCustomerConverter.class);

    default Byte int2Byte(Integer res) {
        if (Objects.isNull(res)) {
            return null;
        }
        return res.byteValue();
    }

    @Mapping(target = "customQuestions", ignore = true)
    CustomerProfileRequestDTO.PetProfileDTO dto2PetProfileDTO(CustomerPetDetailDTO dto);

    @Mapping(target = "phoneNumber", ignore = true)
    @Mapping(target = "customQuestions", ignore = true)
    CustomerProfileRequestDTO.ClientProfileDTO dto2ClientProfileDTO(MoeBusinessCustomerDTO dto);
}
