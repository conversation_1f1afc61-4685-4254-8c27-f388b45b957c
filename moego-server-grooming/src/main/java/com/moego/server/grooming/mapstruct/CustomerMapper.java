package com.moego.server.grooming.mapstruct;

import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.SaveWithPetCustomerVo;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.web.vo.ob.OBClientDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/7/5
 */
@Mapper(uses = {BaseMapper.class})
public interface CustomerMapper {
    CustomerMapper INSTANCE = Mappers.getMapper(CustomerMapper.class);

    @Mapping(target = "primaryAddress", ignore = true)
    @Mapping(target = "newAddresses", ignore = true)
    @Mapping(target = "questionAnswerList", ignore = true)
    OBClientDetailVO customerInfoDto2ClientDetailVO(CustomerInfoDto customerInfoDto);

    @Mapping(target = "phoneNumber", ignore = true)
    @Mapping(target = "customQuestions", ignore = true)
    CustomerProfileRequestDTO.ClientProfileDTO dto2ClientProfileDTO(MoeBusinessCustomerDTO dto);

    @Mapping(target = "preferredBusinessId", ignore = true)
    @Mapping(target = "source", ignore = true)
    @Mapping(target = "sendAutoMessage", ignore = true)
    @Mapping(target = "sendAutoEmail", ignore = true)
    @Mapping(target = "referralSourceDesc", ignore = true)
    @Mapping(target = "rawUpdateTime", ignore = true)
    @Mapping(target = "rawId", ignore = true)
    @Mapping(target = "rawCreateTime", ignore = true)
    @Mapping(target = "petList", ignore = true)
    @Mapping(target = "isUnsubscribed", ignore = true)
    @Mapping(target = "clientColor", ignore = true)
    @Mapping(target = "avatarPath", ignore = true)
    @Mapping(target = "apptReminderByList", ignore = true)
    @Mapping(target = "preferredDay", qualifiedByName = "string2Array")
    @Mapping(target = "preferredTime", qualifiedByName = "string2Array")
    SaveWithPetCustomerVo abandonEntity2SaveVO(MoeBookOnlineAbandonRecord entity);
}
