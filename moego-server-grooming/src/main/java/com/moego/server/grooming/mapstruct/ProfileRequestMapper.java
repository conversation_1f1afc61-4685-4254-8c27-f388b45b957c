package com.moego.server.grooming.mapstruct;

import com.moego.common.constant.CommonConstant;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO;
import com.moego.server.grooming.web.vo.ob.OBClientDetailVO;
import com.moego.server.grooming.web.vo.ob.OBPetDetailVO;
import com.moego.server.grooming.web.vo.ob.OBRequestDetailVO;
import java.util.Arrays;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2023/6/26
 */
@Mapper(
        imports = {CommonConstant.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProfileRequestMapper {
    ProfileRequestMapper INSTANCE = Mappers.getMapper(ProfileRequestMapper.class);

    @Mappings({
        @Mapping(target = "firstName", source = "customerData.firstName"),
        @Mapping(target = "lastName", source = "customerData.lastName"),
        @Mapping(target = "phoneNumber", source = "customerData.phoneNumber"),
        @Mapping(target = "email", source = "customerData.email"),
        @Mapping(target = "referralSourceId", source = "bookOnlineCustomerAdditionalParams.referralSourceId"),
        @Mapping(target = "preferredGroomerId", source = "bookOnlineCustomerAdditionalParams.preferredGroomerId"),
        @Mapping(
                target = "preferredFrequencyType",
                source = "bookOnlineCustomerAdditionalParams.preferredFrequencyType"),
        @Mapping(target = "preferredFrequencyDay", source = "bookOnlineCustomerAdditionalParams.preferredFrequencyDay"),
        @Mapping(target = "preferredDay", source = "bookOnlineCustomerAdditionalParams.preferredDay"),
        @Mapping(target = "preferredTime", source = "bookOnlineCustomerAdditionalParams.preferredTime"),
        @Mapping(target = "customQuestions", source = "customerData.answersMap"),
    })
    CustomerProfileRequestDTO.ClientProfileDTO params2ClientProfileDTO(BookOnlineSubmitParams obParams);

    List<CustomerProfileRequestDTO.PetProfileDTO> params2PetProfileDTO(List<BookOnlinePetParams> pets);

    @Mappings({
        @Mapping(target = "customQuestions", source = "petQuestionAnswers"),
        @Mapping(
                target = "vaccineList",
                expression = "java(VaccineMapper.INSTANCE.params2DTO(petData.getVaccineList()))"),
    })
    CustomerProfileRequestDTO.PetProfileDTO params2PetProfileDTO(BookOnlinePetParams petData);

    @Mappings({
        @Mapping(target = "questionAnswerList", ignore = true),
        @Mapping(target = "avatarPath", ignore = true),
        @Mapping(target = "clientColor", ignore = true),
    })
    OBClientDetailVO clientProfileDTO2ClientDetailVO(CustomerProfileRequestDTO.ClientProfileDTO dto);

    @Mappings({
        @Mapping(target = "vetPhoneNumber", source = "vetPhone"),
        @Mapping(target = "vaccineList", expression = "java(VaccineMapper.INSTANCE.dto2VO(dto.getVaccineList()))"),
    })
    OBPetDetailVO petProfileDTO2PetDetailVO(CustomerProfileRequestDTO.PetProfileDTO dto);

    @Mapping(target = "addressId", source = "customerAddressId")
    OBRequestDetailVO.AddressDetailVO addressDTO2AddressDetailVO(CustomerAddressDto customerAddressDto);

    @Mappings({
        @Mapping(target = "referer", ignore = true),
        @Mapping(target = "customerId", ignore = true),
        @Mapping(target = "questionAnswerList", expression = "java(List.of())"),
    })
    AbandonClientRecordVO.ClientDetailVO dto2AbandonClientDetailVO(CustomerProfileRequestDTO.ClientProfileDTO dto);

    @Mappings({
        @Mapping(target = "preferredDay", expression = "java(array2list(dto.getPreferredDay()))"),
        @Mapping(target = "preferredTime", expression = "java(array2list(dto.getPreferredTime()))"),
    })
    AbandonClientRecordVO.ClientPreferenceVO dto2AbandonClientPreferenceVO(
            CustomerProfileRequestDTO.ClientProfileDTO dto);

    default List<Integer> array2list(Integer[] array) {
        if (ObjectUtils.isEmpty(array)) {
            return List.of();
        }
        return Arrays.asList(array);
    }

    @Mappings({
        @Mapping(target = "serviceId", ignore = true),
        @Mapping(target = "questionAnswerList", expression = "java(List.of())"),
        @Mapping(target = "vetPhoneNumber", source = "vetPhone"),
        @Mapping(
                target = "vaccineList",
                expression = "java(VaccineMapper.INSTANCE.dtoAbandonVaccineDetailVO(dto.getVaccineList()))"),
    })
    AbandonClientRecordVO.AbandonPetDetailVO dto2AbandonPetDetailVO(CustomerProfileRequestDTO.PetProfileDTO dto);

    @Mappings({
        @Mapping(target = "petId", ignore = true),
        @Mapping(target = "status", ignore = true),
        @Mapping(target = "lifeStatus", ignore = true),
    })
    OBClientApptDTO.OBPetInfoDTO dto2PetInfoDTO(CustomerProfileRequestDTO.PetProfileDTO dto);
}
