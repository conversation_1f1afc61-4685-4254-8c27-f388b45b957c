package com.moego.server.grooming.service;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.grooming.eventbus.AppointmentProducer;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import jakarta.annotation.Nullable;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2024/6/4
 */
@Service
@RequiredArgsConstructor
public class AppointmentRepeatService {

    private final AppointmentMapperProxy appointmentMapper;
    private final ActiveMQService mqService;
    private final AppointmentProducer appointmentProducer;

    /**
     * Update appointment repeat id and book online status
     *
     * @param appointmentId appointment id
     * @param repeatId repeat id
     * @param isScheduleBookingRequest 是否在 schedule booking request，有一些流程在调用这个方法前已经修改了 book online status，导致这个方法里对 isScheduleBookingRequest 的判断不正确，因此需要从外部传入。当为 null 时，使用 appointment 的最新状态判断是否为 booking request
     */
    @Transactional
    public void updateRepeatAppointment(
            Integer appointmentId, Integer repeatId, @Nullable Boolean isScheduleBookingRequest) {
        MoeGroomingAppointment existAppt = appointmentMapper.selectByPrimaryKey(appointmentId);
        if (existAppt == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not exist");
        }
        if (existAppt.getRepeatId() > 0 && !Objects.equals(existAppt.getRepeatId(), repeatId)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "current appointment is already in another repeat");
        }

        boolean scheduleBookingRequest =
                isScheduleBookingRequest != null ? isScheduleBookingRequest : isScheduleBookingRequest(existAppt);

        MoeGroomingAppointment update = new MoeGroomingAppointment();
        update.setId(appointmentId);
        update.setRepeatId(repeatId);
        update.setUpdateTime(CommonUtil.get10Timestamp());
        if (scheduleBookingRequest) {
            update.setBookOnlineStatus(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB);
        }
        if (scheduleBookingRequest || isRepeatOnRules(repeatId)) {
            appointmentMapper.updateByPrimaryKeySelective(update);
            ActivityLogRecorder.record(Action.UPDATE, ResourceType.APPOINTMENT, appointmentId, update);

            ThreadPool.execute(() -> appointmentProducer.pushAppointmentUpdatedEvent(appointmentId));
        }
        // 如果是 booking request 则移除列表，并发布同步事件
        if (scheduleBookingRequest) {
            mqService.publishBookingRequestEvent(new BookingRequestEventParams()
                    .setBusinessId(existAppt.getBusinessId())
                    .setAppointmentId(existAppt.getId())
                    .setEvent(BookingRequestEventParams.BookingRequestEvent.SCHEDULED));
        }
    }

    static boolean isScheduleBookingRequest(MoeGroomingAppointment existAppt) {
        return Objects.equals(existAppt.getSource(), GroomingAppointmentEnum.SOURCE_OB)
                && Objects.equals(existAppt.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB);
    }

    private boolean isRepeatOnRules(Integer repeatId) {
        return repeatId != null;
    }

    public void updateNormalAppointmentToRepeat(Integer appointmentId, Integer repeatId) {
        if (Objects.isNull(appointmentId) || Objects.isNull(repeatId)) {
            return;
        }
        MoeGroomingAppointment existAppt = appointmentMapper.selectByPrimaryKey(appointmentId);
        if (existAppt.getRepeatId() != 0) {
            return;
        }
        MoeGroomingAppointment update = new MoeGroomingAppointment();
        update.setId(appointmentId);
        update.setRepeatId(repeatId);
        update.setUpdateTime(CommonUtil.get10Timestamp());

        appointmentMapper.updateByPrimaryKeySelective(update);
    }
}
