package com.moego.server.grooming.service;

import com.moego.common.utils.WeekUtil;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.dto.LimitDto;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/8/31 12:35 PM
 */
@Service
public class OnlineBookingService {

    public static final String STAFF_WEEK_TIME_SELECT_KEY = "isSelected";
    public static final String STAFF_WEEK_TIME_RANGE_KEY = "timeRange";
    public static final String STAFF_PET_LIMIT_ID_KEY = "limitIds";

    @Autowired
    private StaffTimeSyncService staffTimeSyncService;

    /**
     * 获取商家员工可用预约的OB时间
     *
     * @param businessId business id
     * @return <weekDay, <staffId, List<timeRange>>>
     * @see MoeBookOnlineStaffTime#setStaffTimes(java.lang.String)
     */
    public Map<Integer, Map<Integer, List<TimeRangeDto>>> getParsedStaffTimeByBusinessId(Integer businessId) {
        Map<Integer, BookOnlineStaffTimeDTO> staffTimeMap = staffTimeSyncService.queryStaffTime(businessId, null);
        Map<Integer, Map<Integer, List<TimeRangeDto>>> result = new HashMap<>();
        for (int day : WeekUtil.WEEK_ALL_INDEX) {
            result.put(day, new HashMap<>());
        }
        staffTimeMap.forEach(
                (staffId, staffTimeWeek) -> staffTimeWeek.getStaffTimes().forEach((day, dayTime) -> {
                    // 选中的那天 才是可用时间
                    if (dayTime.getIsSelected()) {
                        result.get(WeekUtil.getDayIndexOfWeek(day)).put(staffId, dayTime.getTimeRange());
                    }
                }));
        return result;
    }

    public Map<Integer, Map<Integer, StaffTime>> getStaffTimeByBusinessIdAndStaffIds(
            Integer businessId, List<Integer> staffIds) {
        Map<Integer, BookOnlineStaffTimeDTO> staffTimeMap = staffTimeSyncService.queryStaffTime(businessId, staffIds);
        Map<Integer, Map<Integer, StaffTime>> result = new HashMap<>();
        for (int day : WeekUtil.WEEK_ALL_INDEX) {
            result.put(day, new HashMap<>());
        }
        staffTimeMap.forEach(
                (staffId, staffTimeWeek) -> staffTimeWeek.getStaffTimes().forEach((day, dayTime) -> {
                    // 选中的那天 才是可用时间
                    if (dayTime.getIsSelected()) {
                        result.get(WeekUtil.getDayIndexOfWeek(day)).put(staffId, dayTime);
                    }
                }));
        return result;
    }

    /**
     * 获取商家员工的宠物限制
     *
     * @param businessId business id
     * @return <weekDay, <staffId, List<limitId>>>
     */
    public Map<Integer, Map<Integer, LimitDto>> getParsedStaffPetLimitIdByBusinessId(Integer businessId) {
        Map<Integer, BookOnlineStaffTimeDTO> staffTimeMap = staffTimeSyncService.queryStaffTime(businessId, null);
        Map<Integer, Map<Integer, LimitDto>> result = new HashMap<>();
        for (int day : WeekUtil.WEEK_ALL_INDEX) {
            result.put(day, new HashMap<>());
        }
        staffTimeMap.forEach((staffId, value2) -> {
            var dayMap = value2.getStaffTimes();
            dayMap.forEach((day, dayHour) -> {
                Integer dayOfWeek = WeekUtil.getDayIndexOfWeek(day);
                // 选中的那天 才是可用时间
                if (dayHour.getLimitDto() == null || dayHour.getLimitDto().isEmpty()) {
                    result.get(dayOfWeek).put(staffId, new LimitDto());
                    return;
                }
                result.get(dayOfWeek).put(staffId, dayHour.getLimitDto());
            });
        });
        return result;
    }
}
