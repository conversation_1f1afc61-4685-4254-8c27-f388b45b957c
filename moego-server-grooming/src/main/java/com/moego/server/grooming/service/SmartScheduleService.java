package com.moego.server.grooming.service;

import static com.moego.server.grooming.service.utils.SmartScheduleUtil.convertTimeSlotList;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.fillServiceAddressV2;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.filterNonWorkingTime;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.isInClosedDate;

import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.common.utils.WeekUtil;
import com.moego.idl.models.map.v1.RouteMatrixElement;
import com.moego.idl.models.smart_scheduler.v1.SmartScheduleSettingModel;
import com.moego.idl.service.appointment.v1.GetStaffPetDetailsRequest;
import com.moego.idl.service.appointment.v1.GetStaffPetDetailsResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessClosedDateClient;
import com.moego.server.business.client.IBusinessSmartSchedulingClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.ParsedCloseDate;
import com.moego.server.business.dto.SmartScheduleDrivingRuleDTO;
import com.moego.server.business.dto.SmartScheduleSettingDTO;
import com.moego.server.business.dto.StaffSmartScheduleSettingDTO;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.GetSmartScheduleSettingParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.ServiceAreaInsideBatchRequestV2;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.LimitDto;
import com.moego.server.grooming.dto.LimitGroupDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.dto.StaffConflictDTO;
import com.moego.server.grooming.dto.ss.ScheduleTimeSlot;
import com.moego.server.grooming.dto.ss.SmartScheduleResultDto;
import com.moego.server.grooming.dto.ss.SmartScheduleStaffMap;
import com.moego.server.grooming.dto.ss.SmartScheduleVO;
import com.moego.server.grooming.dto.ss.StaffCheckStatus;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.params.PreviewRepeatParams;
import com.moego.server.grooming.params.ss.SmartScheduleRequest;
import com.moego.server.grooming.service.dto.DateAndStartTimeDTO;
import com.moego.server.grooming.service.dto.SmartScheduleDTO;
import com.moego.server.grooming.service.ob.OBAddressService;
import com.moego.server.grooming.service.ob.OBPetLimitService;
import com.moego.server.grooming.service.ob.SmartScheduleV2Service;
import com.moego.server.grooming.service.remote.SmartSchedulerService;
import com.moego.server.grooming.service.utils.SmartScheduleUtil;
import com.moego.server.grooming.web.vo.SmartScheduleParam;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 相关文档 https://shimo.im/docs/9DPrJKR8D3TPvXhR
 */
@Slf4j
@Service
public class SmartScheduleService {

    @Autowired
    private GoogleMapService googleMapService;

    @Autowired
    private IBusinessClosedDateClient iBusinessClosedDateClient;

    @Autowired
    private OnlineBookingService onlineBookingService;

    @Autowired
    private MoeGroomingBookOnlineService moeGroomingBookOnlineService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private PetDetailMapperProxy petDetailMapper;

    @Autowired
    private AppointmentMapperProxy appointmentMapper;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private IBusinessSmartSchedulingClient iBusinessSmartSchedulingClient;

    private static final Integer NO_END_POINT_DRIVER_TIME = 9999;
    private static final Integer NO_END_POINT_DRIVER_DISTANCE = 9999;

    @Autowired
    private OBAddressService obAddressService;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private SmartSchedulerService smartSchedulerService;

    @Autowired
    private PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceStub;

    @Autowired
    private CompanyHelper companyHelper;

    @Autowired
    private StaffTimeSyncService staffTimeSyncService;

    /**
     * Only support staff with one working hour range per day.
     * <p>
     * check service area
     * <p>
     * get appointment list for the day, from appointment_detail table; start/end time, customer id,
     * get working hour (start & end time of the day)
     * prefilter by freeTime >= buffer + service duration
     * <p>
     * get customer address for appointments left, if block no customer id
     * get smart schedule setting, from business table
     * check certain area certain day
     * query time/distance needed for drive1 + drive2 for potential time block
     * filter out free time block that doesn't meet totalTime >= buffer + drive1 + drive2 + newApptTime
     * filter out both drive1 (time and distance) and drive2 (time and distance) > business setting
     */
    // public SmartScheduleVO smartSchedule(Integer businessId, SmartScheduleRequest request) {
    //     validateInput(request);
    //     SmartScheduleVO emptyResult = SmartScheduleVO.builder()
    //             .businessId(businessId)
    //             .date(request.getDate())
    //             .availableRange(List.of())
    //             .build();
    //     if (!meetServiceArea(request)) {
    //         return emptyResult;
    //     }
    //
    //     List<MoeGroomingPetDetail> services =
    //             petDetailMapper.queryByBusinessIdAndDateOrderByTime(businessId, request.getDate());
    //     //        if (services.isEmpty()) {
    //     //            return emptyResult;
    //     //        }
    //
    //     Map<Integer, TimeSlot> staffHoursMap = getStaffWorkingHours(businessId, request);
    //     // 获取 staff ss setting map
    //     Map<Integer, StaffSmartScheduleSettingDTO> staffSsSettingMap =
    //             getStaffSsSettingMap(businessId, request.getStaffIds());
    //
    //     // Process staff one by one, until find first one meet requirement and return result
    //     for (int i = 0; i < request.getStaffIds().size(); i++) {
    //         if (!meetCertainAreaCertainDay(request)) {
    //             continue;
    //         }
    //
    //         Integer staffId = request.getStaffIds().get(i);
    //         if (staffHoursMap.get(staffId) == null) {
    //             continue;
    //         }
    //
    //         List<MoeGroomingPetDetail> staffServices = services.stream()
    //                 .filter(s -> staffId.equals(s.getStaffId()))
    //                 .collect(Collectors.toList());
    //         List<TimeSlot> freeTimes = buildFreeTimeSlots(staffServices, staffHoursMap.get(staffId));
    //
    //         StaffSmartScheduleSettingDTO staffSsSetting = staffSsSettingMap.get(staffId);
    //         fillServiceAddress(freeTimes, staffSsSetting);
    //
    //         var serviceDuration = getStaffServiceDuration(
    //                 staffId.longValue(), request.getServiceDuration(), request.getStaffServiceDuration());
    //
    //         // pre-filter by time of service duration + buffer
    //         List<TimeSlot> step1Times = freeTimes.stream()
    //                 .filter(t -> t.getEnd() - t.getStart()
    //                         >= SmartScheduleUtil.getTotalDuration(t, serviceDuration, request.getBufferTime()))
    //                 .collect(Collectors.toList());
    //
    //         // if empty (no time meet pre-filter), continue to next staff
    //         if (step1Times.isEmpty()) {
    //             continue;
    //         }
    //
    //         fillDrivingData(step1Times, request);
    //         log.info("####### print step1Times" + JsonUtil.toJson(step1Times));
    //         List<TimeSlot> step2Times = step1Times.stream()
    //                 .filter(t -> meetEnoughTime(t, request))
    //                 .filter(t -> meetMaxDrivingTimeAndDist(
    //                         t, staffSsSetting == null ? null : staffSsSetting.getDrivingRule()))
    //                 .collect(Collectors.toList());
    //
    //         updateTimeWithDrivingTime(step2Times);
    //         checkFirstOrLastAppt(step2Times);
    //
    //         if (!step2Times.isEmpty()) {
    //             // 根据bufferTime 偏移timeslot
    //             SmartScheduleUtil.timeSlotOffset(step2Times, request.getBufferTime());
    //             return buildResult(step2Times, request, businessId, i);
    //         }
    //     }
    //
    //     // return empty result if no staff is available
    //     return buildResult(List.of(), request, businessId, request.getStaffIds().size() - 1);
    // }

    public SmartScheduleResultDto smartSchedule2(Integer businessId, SmartScheduleRequest request) {
        SmartScheduleResultDto resultDto = new SmartScheduleResultDto();
        Map<String, SmartScheduleStaffMap> resultDayMap = new HashMap<>();
        resultDto.setDayMap(resultDayMap);
        if (!meetServiceArea(request)) {
            return resultDto;
        }

        long companyId = companyHelper.mustGetCompanyId(businessId);
        Map<Integer, StaffSmartScheduleSettingDTO> staffSsSettingMap = getStaffSsSettingMap(businessId, null);

        SmartScheduleParam requestParam = new SmartScheduleParam();
        requestParam.setBusinessId(businessId);
        requestParam.setStaffSsSettingMap(staffSsSettingMap);

        BeanUtils.copyProperties(request, requestParam);
        if (request.isFromOB()) {
            // <dayOfWeek, <staffId, staffTime>>
            var staffAvailableWeekTime =
                    onlineBookingService.getStaffTimeByBusinessIdAndStaffIds(businessId, List.of());
            // 对 weekStaffWorkingHours取交集
            requestParam.setObStaffTime(staffAvailableWeekTime);
        } else {
            requestParam.setCheckCACD(checkCACD((int) companyId));
        }
        if (requestParam.isCheckCACD()) {
            if (!StringUtils.hasText(requestParam.getAddressZipcode())
                    && (StringUtils.hasText(requestParam.getAddressLat())
                            && StringUtils.hasText(requestParam.getAddressLng()))) {
                requestParam.setAddressZipcode(
                        getZipcodeByLatLng(requestParam.getAddressLat(), requestParam.getAddressLng()));
            }
        }

        // 获取商家所有staff的week daily hours
        // Map<Integer, Map<LocalDate, List<TimeRangeDto>>> dateStaffWorkingHour =
        //         iBusinessStaffClient.getStaffWorkingHourWithOverrideDate(
        //                 businessId,
        //                 request.getStaffIds(),
        //                 request.getDate(),
        //                 LocalDate.parse(request.getDate())
        //                         .plusDays(request.getFarthestDay())
        //                         .toString());
        // requestParam.setStaffDateWorkingHours(dateStaffWorkingHour);

        // 获取商家一段时间内员工的 override date 设置
        // Map<Integer, List<String>> staffOverrideDateList = iBusinessStaffClient.getStaffOverrideDateList(
        //         businessId,
        //         request.getStaffIds(),
        //         request.getDate(),
        //         LocalDate.parse(request.getDate())
        //                 .plusDays(request.getFarthestDay())
        //                 .toString());
        // requestParam.setStaffOverrideDateList(staffOverrideDateList);

        BusinessPreferenceDto businessPreference = iBusinessBusinessClient.getBusinessPreference(businessId);

        Map<String, Map<Integer, StaffTime>> staffWorkingHour = staffTimeSyncService.getStaffTimeMap(
                new HashSet<>(request.getStaffIds()),
                request.getDate(),
                LocalDate.parse(request.getDate())
                        .plusDays(request.getFarthestDay())
                        .toString(),
                LocalDate.now(ZoneId.of(businessPreference.getTimezoneName())),
                DateUtil.getNowMinutes(businessPreference.getTimezoneName()),
                businessId,
                companyId);
        requestParam.setStaffTime(staffWorkingHour);

        // 排除closed date
        List<ParsedCloseDate> allClosedDate = iBusinessClosedDateClient.getAllCloseDate(businessId);
        requestParam.setAllClosedDate(allClosedDate);
        // set startDate
        requestParam.setStartDate(LocalDate.parse(request.getDate()));
        // 缓存一次ss的 customerAddresses
        Map<Integer, CustomerAddressDto> customerAddress = new HashMap<>();
        final int STEP = Runtime.getRuntime().availableProcessors() * 2; // 步长： 一次查询次数
        SmartScheduleStaffMap[] resultArray = new SmartScheduleStaffMap[request.getFarthestDay()];
        // initialize params
        SmartScheduleDTO smartScheduleDTO = new SmartScheduleDTO()
                .setResultArray(resultArray)
                .setCustomerAddressDtoMap(customerAddress)
                .setRequest(requestParam);
        // set preferred day and time
        if (Objects.nonNull(request.getApplyClientPreferenceCustomerId())) {
            MoeBusinessCustomerDTO customerDTO =
                    iCustomerCustomerClient.getCustomerWithDeleted(request.getApplyClientPreferenceCustomerId());
            if (customerDTO == null || !Objects.equals(customerDTO.getBusinessId(), businessId)) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
            }
            smartScheduleDTO.setIsApplyClientPreference(Boolean.TRUE);
            smartScheduleDTO.setPreferredDay(customerDTO.getPreferredDay());
            smartScheduleDTO.setPreferredTime(customerDTO.getPreferredTime());
        }

        // timezone
        smartScheduleDTO.setTimezoneName(businessPreference.getTimezoneName());

        int firstSlotIndex = -1;
        int remainCount = request.getCount();
        for (int index = 0; index < request.getFarthestDay() + STEP; index += STEP) {
            // calculate endIndex value
            int endIndex = index + Math.min(remainCount, STEP);
            smartScheduleOpt(businessId, smartScheduleDTO.setStartIndex(index).setEndIndex(endIndex));
            for (int j = 0; j < STEP && j + index < resultArray.length; j++) {
                if (resultArray[j + index] != null) {
                    if (!resultDto.isAvailable()) {
                        firstSlotIndex = j + index;
                        resultDto.setAvailable(true);
                    }
                    SmartScheduleStaffMap availableSlot = resultArray[j + index];
                    resultDayMap.put(availableSlot.getDayStr(), availableSlot);
                    // get count days totally fixme
                    remainCount = request.getCount() - (j + index - firstSlotIndex + 1);
                    if (remainCount <= 0) {
                        // ss each staff each time slot
                        smartScheduleFullyDay(smartScheduleDTO, firstSlotIndex, resultDayMap);
                        // find enough slots
                        return resultDto;
                    }
                }
            }
        }
        // ss each staff each time slot
        smartScheduleFullyDay(smartScheduleDTO, firstSlotIndex, resultDayMap);
        return resultDto;
    }

    public void smartScheduleFullyDay(
            SmartScheduleDTO smartScheduleDTO, int firstSlotIndex, Map<String, SmartScheduleStaffMap> resultDayMap) {
        SmartScheduleParam requestParam = smartScheduleDTO.getRequest();
        boolean notFirstAvailableDay =
                BooleanUtils.isNotTrue(requestParam.getQueryPerHalfDay()) || firstSlotIndex == -1;
        if (notFirstAvailableDay) {
            return;
        }
        requestParam.setQueryPerHalfDay(false);
        smartScheduleOpt(
                requestParam.getBusinessId(),
                smartScheduleDTO.setStartIndex(firstSlotIndex).setEndIndex(firstSlotIndex + 1));
        if (firstSlotIndex >= smartScheduleDTO.getResultArray().length) {
            return;
        }
        SmartScheduleStaffMap availableSlot = smartScheduleDTO.getResultArray()[firstSlotIndex];
        resultDayMap.put(availableSlot.getDayStr(), availableSlot);
    }

    private Map<String, List<SmartScheduleGroomingDetailsDTO>> listToMapByDate(
            List<SmartScheduleGroomingDetailsDTO> allServices, Integer filterGroomingId) {
        Map<String, List<SmartScheduleGroomingDetailsDTO>> result = new TreeMap<>();
        allServices.forEach(srv -> {
            if (!PrimitiveTypeUtil.isNullOrZero(filterGroomingId)
                    && srv.getGroomingId().equals(filterGroomingId)) {
                return;
            }
            List<SmartScheduleGroomingDetailsDTO> list = result.computeIfAbsent(
                    StringUtils.hasText(srv.getStartDate()) ? srv.getStartDate() : srv.getAppointmentDate(),
                    k -> new LinkedList<>());
            list.add(srv);
        });
        return result;
    }

    private void getMoreCustomerAddresses(
            Map<Integer, CustomerAddressDto> customerAddressDtoMap,
            List<SmartScheduleGroomingDetailsDTO> servicesOfAllDates) {
        List<Integer> customerIds = servicesOfAllDates.stream()
                .map(SmartScheduleGroomingDetailsDTO::getCustomerId)
                .filter(id -> id > 0) /* skip dummy customer for block appointment */
                .filter(id -> !customerAddressDtoMap.containsKey(id))
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(customerIds)) {
            customerAddressDtoMap.putAll(obAddressService.batchGetPrimaryAddress(customerIds));
        }
    }

    private boolean checkCACD(Integer companyId) {
        SmartScheduleSettingModel smartScheduleSetting =
                smartSchedulerService.getSmartSchedulerSetting(companyId).getSmartScheduleSetting();

        return smartScheduleSetting.getServiceAreaEnable();
    }

    // 获取staff对应customized的duration，如果没有则返回默认的duration
    public static Integer getStaffServiceDuration(
            Long staffId, Integer serviceDuration, Map<Long, Integer> staffServiceDuration) {
        if (staffServiceDuration != null && staffServiceDuration.containsKey(staffId)) {
            return staffServiceDuration.get(staffId);
        }
        return serviceDuration;
    }

    public List<SmartScheduleGroomingDetailsDTO> queryByBusinessIdBetweenDates(
            Integer businessId, LocalDate startDate, LocalDate endDate, List<Integer> staffIds, String zoneId) {
        long companyId = companyHelper.mustGetCompanyId(businessId);

        List<SmartScheduleGroomingDetailsDTO> petDetailDTOList = new ArrayList<>();
        ZonedDateTime startTime = startDate.atStartOfDay(ZoneId.of(zoneId));
        ZonedDateTime endTime = endDate.atTime(LocalTime.MAX).atZone(ZoneId.of(zoneId));

        GetStaffPetDetailsResponse staffPetDetails =
                petDetailServiceStub.getStaffPetDetails(GetStaffPetDetailsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllStaffIds(
                                staffIds.stream().map(Integer::longValue).collect(Collectors.toSet()))
                        // 限制查询 60 天，避免查询过多数据导致索引失效
                        .setStartTimeRange(
                                buildInterval(endTime.minusDays(60).toEpochSecond(), endTime.toEpochSecond() - 1))
                        .setEndTimeRange(buildInterval(
                                startTime.toEpochSecond(),
                                startTime.plusDays(60).toEpochSecond() - 1))
                        .build());

        if (Objects.isNull(staffPetDetails) || staffPetDetails.getStaffPetDetailsCount() == 0) {
            return petDetailDTOList;
        }

        staffPetDetails.getStaffPetDetailsList().forEach(staffPetDetail -> {
            SmartScheduleGroomingDetailsDTO dto = new SmartScheduleGroomingDetailsDTO();
            dto.setId((int) staffPetDetail.getId());
            dto.setPetId((int) staffPetDetail.getPetId());
            dto.setStaffId((int) staffPetDetail.getStaffId());
            dto.setGroomingId((int) staffPetDetail.getAppointmentId());
            dto.setCustomerId((int) staffPetDetail.getCustomerId());
            dto.setServiceId((int) staffPetDetail.getServiceId());
            dto.setAppointmentDate(staffPetDetail.getAppointmentDate());
            dto.setStartDate(staffPetDetail.getStartDate());
            dto.setStartTime((long) staffPetDetail.getStartTime());
            dto.setEndTime((long) staffPetDetail.getEndTime());
            dto.setServiceItemType(staffPetDetail.getServiceItemType());
            dto.setIsBlock(staffPetDetail.getIsBlock());
            petDetailDTOList.add(dto);
        });

        return petDetailDTOList;
    }

    /**
     * process [startIndex, endIndex) 左闭右开
     *
     * @param smartScheduleDTO
     */
    private void smartScheduleOpt(Integer businessId, SmartScheduleDTO smartScheduleDTO) {
        SmartScheduleStaffMap[] resultArray = smartScheduleDTO.getResultArray();
        Map<Integer, CustomerAddressDto> customerAddressDtoMap = smartScheduleDTO.getCustomerAddressDtoMap();
        SmartScheduleParam request = smartScheduleDTO.getRequest();
        final int startIndex = smartScheduleDTO.getStartIndex();
        final int endIndex = smartScheduleDTO.getEndIndex();

        if (startIndex >= Math.min(endIndex, resultArray.length)) {
            return;
        }
        // check CACD
        Map<String, Map<Integer, Boolean>> dateToInside;
        if (request.isCheckCACD()) {
            String startDate = request.getStartDate().plusDays(startIndex).toString();
            String endDate = request.getStartDate().plusDays(endIndex).toString();
            dateToInside = getCACDDateAvailableMap(businessId, startDate, endDate, request);
        } else {
            dateToInside = Collections.emptyMap();
        }

        boolean isSmart = !Boolean.TRUE.equals(request.getDisableSmartScheduling());
        Map<Integer, Future<SmartScheduleStaffMap>> futures = new HashMap<>();
        // 获取指定日期的所有staff的all kinds of grooming  一次性获取所有appt
        LocalDate firstDay = request.getStartDate().plusDays(startIndex);
        LocalDate lastDay = request.getStartDate().plusDays(Math.min(endIndex, resultArray.length));

        // 查询 staff 指定日期范围内的所有 PetDetail
        List<SmartScheduleGroomingDetailsDTO> servicesOfAllDates = queryByBusinessIdBetweenDates(
                request.getBusinessId(), firstDay, lastDay, request.getStaffIds(), smartScheduleDTO.getTimezoneName());

        Map<String, List<SmartScheduleGroomingDetailsDTO>> dayToServicesMap =
                listToMapByDate(servicesOfAllDates, request.getFilterGroomingId());
        // 补充新的 customerAddresses
        if (isSmart) {
            getMoreCustomerAddresses(customerAddressDtoMap, servicesOfAllDates);
            request.setCustomerAddressDtoMap(customerAddressDtoMap);
        }
        for (int currentDay = startIndex; currentDay < endIndex && currentDay < resultArray.length; currentDay++) {
            LocalDate currLocalDate = request.getStartDate().plusDays(currentDay);
            // check closed date
            if (isInClosedDate(currLocalDate, request.getAllClosedDate())
                    ||
                    // filter preferred day
                    (BooleanUtils.isTrue(smartScheduleDTO.getIsApplyClientPreference())
                            && !isPreferredDay(currLocalDate, smartScheduleDTO.getPreferredDay()))) {
                continue;
            }
            Future<SmartScheduleStaffMap> currentResult = ThreadPool.submit(() -> {
                String currLocalDateStr = currLocalDate.toString();
                SmartScheduleStaffMap currentDayAvailableTimes = new SmartScheduleStaffMap();
                // Process staff one by one, until find first one meet requirement and return result
                for (int i = 0; i < request.getStaffIds().size(); i++) {
                    Integer staffId = request.getStaffIds().get(i);
                    // check cacd
                    if (!CollectionUtils.isEmpty(dateToInside)) {
                        Map<Integer, Boolean> staffFlagMap = dateToInside.get(currLocalDateStr);
                        if (staffFlagMap != null && Boolean.FALSE.equals(staffFlagMap.get(staffId))) {
                            log.info("skip staff cacd check false for {} staff {}", currLocalDateStr, staffId);
                            continue;
                        }
                    }

                    // 计算当天某个staff的可用时间段
                    List<TimeSlot> freeTimeSlots =
                            getStaffCurDateFreeTimeSlots(currLocalDate, staffId, dayToServicesMap, request);
                    // filter preferred time
                    filterTimeByPreference(
                            freeTimeSlots,
                            smartScheduleDTO.getPreferredTime(),
                            getStaffServiceDuration(
                                    staffId.longValue(),
                                    request.getServiceDuration(),
                                    request.getStaffServiceDuration()));
                    // filter today time slot
                    if (currLocalDate.isEqual(LocalDate.now(ZoneId.of(smartScheduleDTO.getTimezoneName())))) {
                        int nowMinutes = DateUtil.getNowMinutes(smartScheduleDTO.getTimezoneName());
                        freeTimeSlots = filterTodayTimeSlot(nowMinutes, freeTimeSlots);
                        log.info(
                                "SS before the business's available time of the day filter result: [{}]",
                                freeTimeSlots);
                    }
                    List<TimeSlot> availableTimeSlots =
                            smartSchedulingOneDay(staffId, freeTimeSlots, request, currLocalDateStr);
                    if (!availableTimeSlots.isEmpty()) {
                        updateTimeWithDrivingTime(availableTimeSlots);
                        checkFirstOrLastAppt(availableTimeSlots);
                        // record this staff results and compute next one
                        if (currentDayAvailableTimes.getDayStr() == null) {
                            currentDayAvailableTimes.setStaffMap(new HashMap<>());
                            currentDayAvailableTimes.setDayStr(currLocalDateStr);
                        }
                        SmartScheduleVO smartScheduleVO = SmartScheduleVO.builder()
                                .availableRange(convertTimeSlotList(availableTimeSlots))
                                .staffId(staffId)
                                .build();
                        currentDayAvailableTimes.getStaffMap().put(staffId, smartScheduleVO);
                    }
                }
                // return empty result if no staff is available
                if (currentDayAvailableTimes.getStaffMap() != null) {
                    return currentDayAvailableTimes;
                } else {
                    return null;
                }
            });
            futures.put(currentDay, currentResult);
        }
        futures.forEach((resultIndex, futureVo) -> {
            try {
                SmartScheduleStaffMap currentVo = futureVo.get();
                if (currentVo != null) {
                    resultArray[resultIndex] = currentVo;
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("get ss result failed", e);
            }
        });
    }

    public List<TimeSlot> filterTodayTimeSlot(int nowMinutes, List<TimeSlot> availableTimeSlots) {
        if (CollectionUtils.isEmpty(availableTimeSlots)) {
            return Collections.emptyList();
        }

        // filter today time
        if (nowMinutes <= availableTimeSlots.get(0).getStart()) {
            return availableTimeSlots;
        }

        int start = 0;
        for (; start < availableTimeSlots.size(); start++) {
            TimeSlot timeSlot = availableTimeSlots.get(start);
            if (nowMinutes <= timeSlot.getStart()) {
                return availableTimeSlots.subList(start, availableTimeSlots.size());
            }
            if (nowMinutes <= timeSlot.getEnd()) {
                timeSlot.setStart(nowMinutes);
                break;
            }
        }
        return availableTimeSlots.subList(start, availableTimeSlots.size());
    }

    public boolean isPreferredDay(LocalDate currLocalDate, Integer[] preferredDay) {
        if (ArrayUtils.isEmpty(preferredDay)) {
            return false;
        }
        int dayOfWeek = currLocalDate.getDayOfWeek().getValue();
        // Sunday convert 7 to 0
        if (dayOfWeek == 7) {
            dayOfWeek = 0;
        }
        return Arrays.asList(preferredDay).contains(dayOfWeek);
    }

    /**
     * ss for repeat
     *
     * @param repeatParams
     * @param serviceDuration
     * @param queryDateMap
     * @return
     */
    public Map<String, DateAndStartTimeDTO> smartScheduleForRepeat(
            PreviewRepeatParams repeatParams, Integer serviceDuration, Map<String, List<LocalDate>> queryDateMap) {
        Integer businessId = repeatParams.getBusinessId();
        Long companyId = repeatParams.getCompanyId().longValue();

        Map<String, DateAndStartTimeDTO> resultMap = new HashMap<>();
        // 请求参数
        Integer staffId = repeatParams.getRepeatStaffInfoParams().get(0).getStaffId();
        Integer startTime = repeatParams.getRepeatStaffInfoParams().get(0).getStartTime();
        Integer customerId = repeatParams.getCustomerId();
        boolean applyPreference =
                repeatParams.getApplyClientPreference() != null ? repeatParams.getApplyClientPreference() : false;

        // customer companyId 检查
        MoeBusinessCustomerDTO customerDTO = iCustomerCustomerClient.getCustomerWithDeleted(customerId);
        if (customerDTO == null || !Objects.equals(customerDTO.getCompanyId(), companyId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }

        // 判断business类型，salon类型不需要CACD、查地址，只需要avoid conflict
        InfoIdParams idParams = new InfoIdParams();
        idParams.setInfoId(businessId);
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(idParams);
        boolean isSalon = Objects.equals(businessDto.getAppType(), BusinessConst.APP_TYPE_SALON);

        // 过滤preferDay外的日期
        if (applyPreference) {
            filterDaysByPreference(queryDateMap, customerDTO.getPreferredDay());
        }

        // 查询closed date
        List<ParsedCloseDate> allClosedDate = iBusinessClosedDateClient.getAllCloseDate(businessId);
        // 查找日期内的service
        List<String> appointmentDates = queryDateMap.values().stream()
                .map(dates -> dates.stream().map(LocalDate::toString).collect(Collectors.toSet()))
                .flatMap(Collection::stream)
                .sorted(Comparator.comparing(date -> date))
                .collect(Collectors.toList());

        // 增加了filterDay后，有可能列表为空，需要做一下判断
        if (CollectionUtils.isEmpty(appointmentDates)) {
            return resultMap;
        }

        // 构建ss请求参数
        SmartScheduleParam smartScheduleParams = buildParamsForSSRepeat(
                businessId,
                staffId,
                customerId,
                serviceDuration,
                isSalon,
                appointmentDates,
                repeatParams.getBufferTime());
        // 查询staff指定日期内的services
        List<SmartScheduleGroomingDetailsDTO> servicesOfAllDates = getServicesByStaffIdAndDates(
                businessId,
                staffId,
                appointmentDates,
                repeatParams.getExistAppointmentId(),
                repeatParams.getRepeatId());
        Map<String, List<SmartScheduleGroomingDetailsDTO>> dayToServicesMap = listToMapByDate(servicesOfAllDates, null);

        Map<String, Map<Integer, Boolean>> dateToInside = new HashMap<>();
        if (!isSalon) {
            // CACD
            if (checkCACD(repeatParams.getCompanyId())) {
                dateToInside.putAll(getCACDDateAvailableMap(
                        businessId,
                        appointmentDates.get(0),
                        appointmentDates.get(appointmentDates.size() - 1),
                        smartScheduleParams));
            }
            // 补充新的 customerAddresses
            getMoreCustomerAddresses(smartScheduleParams.getCustomerAddressDtoMap(), servicesOfAllDates);
        }

        for (var entry : queryDateMap.entrySet()) {
            String date = entry.getKey();
            List<LocalDate> optionDateList = queryDateMap.get(date);
            for (LocalDate curDate : optionDateList) {
                // check closed date
                if (isInClosedDate(curDate, allClosedDate)) {
                    continue;
                }

                // check cacd
                Map<Integer, Boolean> staffFlagMap = dateToInside.get(curDate.toString());
                if (staffFlagMap != null && Boolean.FALSE.equals(staffFlagMap.get(staffId))) {
                    log.info("skip staff cacd check false for {} staff {}", curDate.toString(), staffId);
                    continue;
                }

                List<TimeSlot> freeTimeSlots =
                        getStaffCurDateFreeTimeSlots(curDate, staffId, dayToServicesMap, smartScheduleParams);
                // 判断client preference开关是否开启，并进行过滤
                if (applyPreference) {
                    filterTimeByPreference(freeTimeSlots, customerDTO.getPreferredTime(), serviceDuration);
                    if (CollectionUtils.isEmpty(freeTimeSlots)) {
                        continue;
                    }
                }

                // Salon类型：第一个日期先检查一下start time所在的time slot是否符合，优先选择的时间
                if (isSalon && Objects.equals(date, curDate.toString())) {
                    TimeSlot slot = smartScheduleCheckExactTime(startTime, staffId, freeTimeSlots, smartScheduleParams);
                    if (slot != null) {
                        DateAndStartTimeDTO dateTime = new DateAndStartTimeDTO();
                        dateTime.setDate(curDate.toString());
                        dateTime.setStartTime(slot.getStart());
                        resultMap.put(date, dateTime);
                        break;
                    }
                }

                // 查询一整天的timeslot，找到第一个就返回
                smartScheduleParams.setQueryOne(Boolean.TRUE);
                List<TimeSlot> availableTimes =
                        smartSchedulingOneDay(staffId, freeTimeSlots, smartScheduleParams, curDate.toString());
                if (!CollectionUtils.isEmpty(availableTimes)) {
                    updateTimeWithDrivingTime(availableTimes);
                    DateAndStartTimeDTO dateTime = new DateAndStartTimeDTO();
                    dateTime.setDate(curDate.toString());
                    dateTime.setStartTime(availableTimes.get(0).getStart());
                    resultMap.put(date, dateTime);
                    break;
                }
            }
        }
        return resultMap;
    }

    /**
     * 构建smart schedule查询参数
     *
     * @param businessId
     * @param staffId
     * @param customerId
     * @param serviceDuration
     * @param isSalon
     * @return
     */
    private SmartScheduleParam buildParamsForSSRepeat(
            Integer businessId,
            Integer staffId,
            Integer customerId,
            Integer serviceDuration,
            Boolean isSalon,
            List<String> appointmentDates,
            Integer bufferTime) {
        Map<Integer, StaffSmartScheduleSettingDTO> staffSsSettingMap = getStaffSsSettingMap(businessId, null);
        // Map<Integer, Map<LocalDate, List<TimeRangeDto>>> dateStaffWorkingHour =
        //         iBusinessStaffClient.getStaffWorkingHourWithOverrideDate(
        //                 businessId,
        //                 Collections.singletonList(staffId),
        //                 appointmentDates.get(0),
        //                 appointmentDates.get(appointmentDates.size() - 1));

        var companyId =
                iBusinessBusinessClient.getCompanyIdByBusinessId(businessId).companyId();
        var businessPreference = iBusinessBusinessClient.getBusinessPreference(businessId);

        Map<String, Map<Integer, StaffTime>> staffWorkingHour = staffTimeSyncService.getStaffTimeMap(
                Set.of(staffId),
                appointmentDates.get(0),
                appointmentDates.get(appointmentDates.size() - 1),
                LocalDate.now(ZoneId.of(businessPreference.getTimezoneName())),
                DateUtil.getNowMinutes(businessPreference.getTimezoneName()),
                businessId,
                companyId);

        SmartScheduleParam requestParam = new SmartScheduleParam();
        requestParam.setStaffTime(staffWorkingHour);
        requestParam.setServiceDuration(serviceDuration);
        requestParam.setStaffIds(Collections.singletonList(staffId));
        requestParam.setBusinessId(businessId);
        requestParam.setStaffSsSettingMap(staffSsSettingMap);
        // requestParam.setWeekStaffWorkingHours(weekStaffWorkingHours);
        // requestParam.setObStaffWorkingHours(null); // ss for repeat请求暂时不是从ob来
        requestParam.setCustomerAddressDtoMap(new HashMap<>());
        // requestParam.setStaffDateWorkingHours(dateStaffWorkingHour);
        requestParam.setBufferTime(bufferTime);

        CustomerAddressDto addressDTO = obAddressService.getPrimaryAddress(customerId);
        if (addressDTO != null
                && addressDTO.getCustomerAddressId() != null
                && StringUtils.hasText(addressDTO.getLat())
                && StringUtils.hasText(addressDTO.getLng())) {
            requestParam.setAddressLat(addressDTO.getLat());
            requestParam.setAddressLng(addressDTO.getLng());
            requestParam.setDisableSmartScheduling(isSalon); // customer有地址时，取决于business类型是Salon还是Mobile
        } else {
            // 当前顾客没有地址时，不计算驾驶时间
            requestParam.setDisableSmartScheduling(true);
        }
        return requestParam;
    }

    /**
     * 查询某个staff指定日期内的appointments
     *
     * @param businessId
     * @param staffId
     * @param appointmentDateList
     * @return
     */
    private List<SmartScheduleGroomingDetailsDTO> getServicesByStaffIdAndDates(
            Integer businessId,
            Integer staffId,
            List<String> appointmentDateList,
            Integer exceptId,
            Integer exceptRepeatId) {
        List<Integer> staffIdList = Objects.nonNull(staffId) ? List.of(staffId) : List.of();
        List<Integer> exceptApptIdList = Objects.nonNull(exceptId) ? List.of(exceptId) : List.of();
        List<StaffConflictDTO> apptOfAllDateList = petDetailMapper.queryPetDetailByAppointmentDatesAndStaffIds(
                businessId, appointmentDateList, staffIdList, exceptApptIdList, exceptRepeatId);
        return apptOfAllDateList.stream()
                .map(appt -> {
                    SmartScheduleGroomingDetailsDTO ssDto = new SmartScheduleGroomingDetailsDTO();
                    BeanUtils.copyProperties(appt, ssDto);
                    return ssDto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 过滤不在client preference的日期
     *
     * @param datesMap
     * @param preferDay
     */
    public void filterDaysByPreference(Map<String, List<LocalDate>> datesMap, Integer[] preferDay) {
        datesMap.forEach(
                (date, optionDates) -> optionDates.removeIf(optionDate -> !isPreferredDay(optionDate, preferDay)));
    }

    /**
     * 过滤时间
     * 1.timeslot与preferTime没有交集：过滤
     * 2.timeslot开始时间在preferTime范围内：保留
     * 3.timeslot开始时间大于preferTime开始时间，timeslot结束时间大于preferTime开始时间：
     * 更新timeslot的开始时间为preferTime的开始时间，判断新的时间范围是否满足serviceDuration，满足则保留，不满足则过滤
     *
     * @param timeslots
     * @param preferTime
     */
    public void filterTimeByPreference(List<TimeSlot> timeslots, Integer[] preferTime, Integer serviceDuration) {
        if (preferTime == null || preferTime.length < 2) {
            return;
        }
        Integer preferTimeStart = preferTime[0];
        Integer preferTimeEnd = preferTime[1];

        timeslots.removeIf(timeslot -> {
            int timeslotStart = timeslot.getStart();
            int timeslotEnd = timeslot.getEnd();
            if (timeslotStart >= preferTimeStart && timeslotStart <= preferTimeEnd) {
                return false;
            }
            if (timeslotStart < preferTimeStart && timeslotEnd > preferTimeStart) {
                if (timeslotEnd - preferTimeStart >= serviceDuration) {
                    timeslot.setStart(preferTimeStart);
                    return false;
                }
            }
            return true;
        });
    }

    @Deprecated
    private void mobileGroomingCheck(
            Integer businessId,
            Map<String, Map<Integer, Boolean>> dateToInside,
            String startDate,
            String endDate,
            SmartScheduleParam request) {
        // 判断cacd是否开启
        MoeBusinessBookOnline businessBookOnline = moeGroomingBookOnlineService.getSettingInfoByBusinessId(businessId);
        if (businessBookOnline == null) {
            return;
        }
        final boolean cacdFlag = CustomerContactEnum.BUSINESS_IS_ENABLE.equals(businessBookOnline.getIsEnable())
                && BooleanEnum.VALUE_TRUE.equals(businessBookOnline.getServiceAreaEnable())
                && BooleanEnum.VALUE_TRUE.equals(businessBookOnline.getIsNeedAddress());

        if (cacdFlag) {
            dateToInside.putAll(getCACDDateAvailableMap(businessId, startDate, endDate, request));
        }
    }

    private Map<String, Map<Integer, Boolean>> getCACDDateAvailableMap(
            Integer businessId, String startDate, String endDate, SmartScheduleParam request) {
        ServiceAreaInsideBatchRequestV2 cacdRequest = new ServiceAreaInsideBatchRequestV2();
        try {
            cacdRequest.setLat(Double.parseDouble(request.getAddressLat()));
        } catch (Exception e) {
            cacdRequest.setLat(0.0);
            log.error("parse lat failed: " + request.getAddressLat(), e);
        }
        try {
            cacdRequest.setLng(Double.parseDouble(request.getAddressLng()));
        } catch (Exception e) {
            cacdRequest.setLng(0.0);
            log.error("parse lng failed: " + request.getAddressLng(), e);
        }
        cacdRequest.setZipcode(request.getAddressZipcode());
        cacdRequest.setStaffIds(request.getStaffIds());
        cacdRequest.setStartDate(startDate);
        cacdRequest.setEndDate(endDate);
        return iBusinessStaffClient
                .isLocationInsideAreaBatchV2(businessId, cacdRequest)
                .getIsInsideMap();
    }

    /**
     * 检查具体时间是否满足ss驾驶时间要求
     *
     * @param startTime
     * @param staffId
     * @param freeTimes
     * @param request
     * @return
     */
    private TimeSlot smartScheduleCheckExactTime(
            Integer startTime, Integer staffId, List<TimeSlot> freeTimes, SmartScheduleParam request) {
        StaffSmartScheduleSettingDTO staffSsSetting =
                request.getStaffSsSettingMap().get(staffId);

        boolean isSmart = !Boolean.TRUE.equals(request.getDisableSmartScheduling());

        Integer bufferTime = Objects.nonNull(request.getBufferTime()) ? request.getBufferTime() : 0;

        List<TimeSlot> step1Times = freeTimes.stream()
                .filter(t -> t.getEnd() - t.getStart()
                        >= SmartScheduleUtil.getTotalDuration(t, request.getServiceDuration(), bufferTime))
                .toList();

        // if empty (no time meet pre-filter), continue to next staff
        if (step1Times.isEmpty()) {
            return null;
        }

        int endTime = startTime + request.getServiceDuration();
        for (TimeSlot slot : step1Times) {
            // 只判断service start time所在的time slot
            if (slot.getStart() <= startTime && slot.getEnd() >= endTime) {
                slot.updateAvailableTime(
                        SmartScheduleUtil.getTotalDuration(slot, request.getServiceDuration(), bufferTime));
                // 以单个timeslot去查询驾驶时间，满足条件则返回，不再往下查找
                if (isSmart) {
                    googleMapService.calculateAvailableTime(
                            Collections.singletonList(slot),
                            request.getAddressLat(),
                            request.getAddressLng(),
                            request.getServiceDuration(),
                            bufferTime);
                }
                if (slot.getAvailableTime() >= 0
                        && meetMaxDrivingTimeAndDist(
                                slot, staffSsSetting == null ? null : staffSsSetting.getDrivingRule())) {

                    // 根据bufferTime 偏移timeslot
                    SmartScheduleUtil.timeSlotOffset(List.of(slot), bufferTime);

                    // 对于选中的service time的处理
                    int startWithDriveInTime = slot.getStart() + slot.getDriveInMinutes();
                    int endWithDriveInTime = startWithDriveInTime + request.getServiceDuration();

                    int endOffDriveOutTime = slot.getEnd() - slot.getDriveOutMinutes();
                    int startOffDriveOutTime = endOffDriveOutTime - request.getServiceDuration();

                    if (slot.getStart() + slot.getDriveInMinutes() <= startTime
                            && slot.getEnd() - slot.getDriveOutMinutes() >= endTime) {
                        // 1.start,end准时
                        slot.setStart(startTime);
                        return slot;
                    } else if (startWithDriveInTime >= startTime
                            && endWithDriveInTime + slot.getDriveOutMinutes() <= slot.getEnd()) {
                        // 2.start drive in time不够，但时间充足，把start和end延后
                        slot.setStart(startWithDriveInTime);
                        return slot;
                    } else if (endOffDriveOutTime <= endTime
                            && startOffDriveOutTime - slot.getDriveInMinutes() >= slot.getStart()) {
                        // 3.end drive out time不够，但时间充足，把start和end提前
                        slot.setStart(startOffDriveOutTime);
                        return slot;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 通过SmartScheduling查询一天内的time slot
     *
     * @param staffId
     * @param request
     * @return
     */
    public List<TimeSlot> smartSchedulingOneDay(
            Integer staffId, List<TimeSlot> freeTimeSlots, SmartScheduleParam request, String currentDate) {
        StaffSmartScheduleSettingDTO staffSsSetting =
                request.getStaffSsSettingMap().get(staffId);
        log.info("smartSchedulingOneDay, staffId: {}, staffSsSetting: {}", staffId, staffSsSetting);
        boolean isSmart = !Boolean.TRUE.equals(request.getDisableSmartScheduling());

        // pre-filter by time of service duration + buffer
        Integer bufferTime = Objects.nonNull(request.getBufferTime()) ? request.getBufferTime() : 0;
        List<TimeSlot> step1Times = freeTimeSlots.stream()
                .filter(t -> t.getEnd() - t.getStart()
                        >= SmartScheduleUtil.getTotalDuration(
                                t,
                                getStaffServiceDuration(
                                        staffId.longValue(),
                                        request.getServiceDuration(),
                                        request.getStaffServiceDuration()),
                                bufferTime))
                .collect(Collectors.toList());

        // if empty (no time meet pre-filter), continue to next staff
        if (step1Times.isEmpty()) {
            return Collections.emptyList();
        }

        List<TimeSlot> resultList = new ArrayList<>();
        if (BooleanUtils.isTrue(request.getQueryOne())) {
            // query first time slot by day
            queryOne(staffId, step1Times, request, bufferTime, isSmart, resultList);
        } else if (BooleanUtils.isTrue(request.getQueryPerHalfDay())) {
            // query first time slot by half day
            List<TimeSlot> amSlotList = new ArrayList<>();
            List<TimeSlot> pmSlotList = new ArrayList<>();
            for (TimeSlot slot : step1Times) {
                if (slot.getStart() < DateUtil.getMinsByHourMins(12, 0)) {
                    amSlotList.add(slot);
                } else {
                    pmSlotList.add(slot);
                }
            }
            if (BooleanUtils.isNotTrue(request.getAmTimeSlotMap().get(currentDate))) {
                boolean result = queryOne(staffId, amSlotList, request, bufferTime, isSmart, resultList);
                if (result) {
                    request.getAmTimeSlotMap().put(currentDate, true);
                }
            }
            if (BooleanUtils.isNotTrue(request.getPmTimeSlotMap().get(currentDate))) {
                boolean result = queryOne(staffId, pmSlotList, request, bufferTime, isSmart, resultList);
                if (result) {
                    request.getPmTimeSlotMap().put(currentDate, true);
                }
            }
        } else {
            resultList = step1Times;
            // 依次获取每个地址到新地址的driving time
            if (isSmart) {
                // googleMapService.fillDrivingDistancesAndTime(step1Times, request);
                googleMapService.calculateAvailableTime(
                        step1Times,
                        request.getAddressLat(),
                        request.getAddressLng(),
                        getStaffServiceDuration(
                                staffId.longValue(), request.getServiceDuration(), request.getStaffServiceDuration()),
                        bufferTime);
                resultList = step1Times.stream()
                        // 剩余时间足够
                        .filter(t -> (0 <= t.getAvailableTime()))
                        // 开车时间合适
                        .filter(t -> meetMaxDrivingTimeAndDist(
                                t, staffSsSetting == null ? null : staffSsSetting.getDrivingRule()))
                        .collect(Collectors.toList());
            }
        }

        // 根据bufferTime 偏移timeslot
        SmartScheduleUtil.timeSlotOffset(resultList, bufferTime);

        return resultList;
    }

    /**
     * query first time slot by all time slot list
     *
     * @param slotList   time slot list
     * @param request    request params
     * @param bufferTime buffer time, default 10 mins
     * @param isSmart    ss switch
     * @param resultList time slot result
     * @return filter result
     */
    public boolean queryOne(
            Integer staffId,
            List<TimeSlot> slotList,
            SmartScheduleParam request,
            Integer bufferTime,
            boolean isSmart,
            List<TimeSlot> resultList) {
        StaffSmartScheduleSettingDTO staffSsSetting =
                request.getStaffSsSettingMap().get(staffId);
        for (TimeSlot slot : slotList) {
            var serviceDuration = getStaffServiceDuration(
                    staffId.longValue(), request.getServiceDuration(), request.getStaffServiceDuration());
            Integer totalDuration = SmartScheduleUtil.getTotalDuration(slot, serviceDuration, bufferTime);
            slot.updateAvailableTime(totalDuration);
            // 以单个timeslot去查询驾驶时间，满足条件则返回，不再往下查找
            if (isSmart) {
                googleMapService.calculateAvailableTime(
                        Collections.singletonList(slot),
                        request.getAddressLat(),
                        request.getAddressLng(),
                        serviceDuration,
                        bufferTime);
            }
            if (slot.getAvailableTime() >= 0
                    && meetMaxDrivingTimeAndDist(
                            slot, staffSsSetting == null ? null : staffSsSetting.getDrivingRule())) {
                resultList.add(slot);
                return true;
            }
        }
        return false;
    }

    /**
     * 根据当前日期的services，构建空闲时间的timeslot列表
     *
     * @param currLocalDate
     * @param staffId
     * @param dayToServicesMap
     * @param request
     * @return
     */
    private List<TimeSlot> getStaffCurDateFreeTimeSlots(
            LocalDate currLocalDate,
            Integer staffId,
            Map<String, List<SmartScheduleGroomingDetailsDTO>> dayToServicesMap,
            SmartScheduleParam request) {
        List<SmartScheduleGroomingDetailsDTO> targetDayTickets = dayToServicesMap.get(currLocalDate.toString());
        List<SmartScheduleGroomingDetailsDTO> staffServices = targetDayTickets != null
                ? targetDayTickets.stream()
                        .filter(s -> staffId.equals(s.getStaffId()))
                        .collect(Collectors.toList())
                : Collections.emptyList();
        // 剔除已占用时间，并根据staffHour获取可用时间列表; ob直接使用obStaffTime不用考虑businessWorkingHours
        return buildFreeTimeSlotsByStaffId(staffServices, staffId, currLocalDate, request);
    }

    private void setEndAddressDriveOutTime(TimeSlot lastSlot, SmartScheduleSettingDTO smartScheduleInfo) {
        if (StringUtils.isEmpty(smartScheduleInfo.getEndLocationLat())
                && StringUtils.isEmpty(smartScheduleInfo.getEndLocationLng())
                && lastSlot.getAfterApptId() < 0) {
            lastSlot.setDriveOutMiles(NO_END_POINT_DRIVER_DISTANCE);
            lastSlot.setDriveOutMinutes(NO_END_POINT_DRIVER_TIME);
        }
    }

    /**
     * 上闭下开：cond1： 1. time ok-> true  2. time not ok-> cond4-> false
     * 上闭下闭：
     * 上开下闭：
     * 上开下开：cond3->true
     *
     * @param t
     * @param drivingRule
     * @return
     */
    public boolean meetMaxDrivingTimeAndDist(TimeSlot t, SmartScheduleDrivingRuleDTO drivingRule) {
        if (drivingRule == null) {
            // 没有 ss 配置，不限制
            return true;
        }
        // cond1
        if (t.isBeforeAddressValid()) {
            if (t.getDriveInMinutes() <= drivingRule.getMaxTime()
                    || t.getDriveInMiles() <= drivingRule.getMaxDistInMile()) {
                return true;
            }
        }
        // cond2
        if (t.isAfterAddressValid()) {
            if (t.getDriveOutMinutes() <= drivingRule.getMaxTime()
                    || t.getDriveOutMiles() <= drivingRule.getMaxDistInMile()) {
                return true;
            }
        }
        // cond3
        // widely open
        if (!t.isAfterAddressValid() && !t.isBeforeAddressValid()) {
            return true;
        }
        // cond4
        return false;
    }

    /**
     * 检查该时间段是否为一天第一个或者最后一个预约时间，如果是标记
     * <p>
     * 用以显示开车时间是从出发点到预约点，或者预约点到结束点。
     */
    void checkFirstOrLastAppt(List<TimeSlot> step2Times) {
        for (TimeSlot t : step2Times) {
            if (t.getBeforeServiceId() < 0) {
                t.setDriveFromStartLocation(true);
            }
            if (t.getAfterServiceId() < 0) {
                t.setDriveToEndLocation(true);
            }
        }
    }

    /**
     * 时间段开始时间根据driving time延后
     * 时间段结束时间根据driving time提前
     */
    void updateTimeWithDrivingTime(List<TimeSlot> step2Times) {
        for (TimeSlot t : step2Times) {
            t.setStart(t.getStart() + t.getDriveInMinutes());
            t.setEnd(t.getEnd() - t.getDriveOutMinutes());
        }
    }

    static SmartScheduleVO buildResult(
            List<TimeSlot> availableTimes, SmartScheduleRequest request, Integer businessId, int staffIndex) {
        List<ScheduleTimeSlot> ranges = availableTimes.stream()
                .map(t -> ScheduleTimeSlot.builder()
                        .startTime(t.getStart())
                        .endTime(t.getEnd())
                        .driveInMinutes(t.getDriveInMinutes())
                        .driveOutMinutes(t.getDriveOutMinutes())
                        .driveInMiles(t.getDriveInMiles())
                        .driveOutMiles(t.getDriveOutMiles())
                        .isDriveFromStart(t.getBeforeApptId() < 0)
                        .isDriveToEnd(t.getAfterApptId() < 0)
                        .build())
                .collect(Collectors.toList());

        ScheduleTimeSlot firstSlot = null;
        if (!availableTimes.isEmpty()) {
            TimeSlot firstTime = availableTimes.get(0);
            firstSlot = ScheduleTimeSlot.builder()
                    .startTime(firstTime.getStart())
                    .driveInMinutes(firstTime.getDriveInMinutes())
                    .driveOutMinutes(firstTime.getDriveOutMinutes())
                    .driveInMiles(firstTime.getDriveInMiles())
                    .driveOutMiles(firstTime.getDriveOutMiles())
                    .isDriveFromStart(firstTime.getBeforeApptId() < 0)
                    .isDriveToEnd(firstTime.getAfterApptId() < 0)
                    .build();
        }

        StaffCheckStatus staffCheckStatus = StaffCheckStatus.builder()
                .foundStaff(request.getStaffIds().get(staffIndex))
                .checkedStaffs(request.getStaffIds().subList(0, staffIndex + 1))
                .allGivenStaffs(request.getStaffIds())
                .build();

        return SmartScheduleVO.builder()
                .businessId(businessId)
                .date(request.getDate())
                .firstSlot(firstSlot)
                .availableRange(ranges)
                .staffId(staffCheckStatus.getFoundStaff())
                .staffCheckStatus(staffCheckStatus)
                .build();
    }

    /**
     * @param myServices    所有已占用时间块
     * @param staffId       指定staff
     * @param currLocalDate 当前日期
     * @param request       全局参数
     * @return
     */
    List<TimeSlot> buildFreeTimeSlotsByStaffId(
            List<SmartScheduleGroomingDetailsDTO> myServices,
            Integer staffId,
            LocalDate currLocalDate,
            SmartScheduleParam request) {
        final int weekDay = WeekUtil.getDayOfWeek(currLocalDate);
        List<TimeSlot> result = new ArrayList<>();
        TimeRangeDto staffWorkingHour;
        List<TimeRangeDto> staffWorkingHourList;
        // LimitDto limitDto;
        List<LimitGroupDTO> limitGroups;
        StaffTime currentStaffTime =
                SmartScheduleV2Service.getStaffTime(request.getStaffTime(), staffId, currLocalDate);
        // ob working hour 不为空，并且 staff 没有开启 sync with working hour
        if (!CollectionUtils.isEmpty(request.getObStaffTime())
                && !request.getSyncWithWorkingHourStaff().contains(staffId)) {
            // 没开 sync
            // Map<Integer, List<String>> staffOverrideDateList = request.getStaffOverrideDateList();

            // if (!CollectionUtils.isEmpty(staffOverrideDateList)
            //         && !CollectionUtils.isEmpty(staffOverrideDateList.get(staffId))
            //         && staffOverrideDateList.get(staffId).contains(currLocalDate.toString())) {
            if (Objects.nonNull(currentStaffTime) && currentStaffTime.getIsOverrideDate()) {
                // 使用 override date 覆盖
                staffWorkingHourList = currentStaffTime.getTimeRange();
            } else {
                // 使用 OB 配置
                Map<Integer, StaffTime> oneDayTimes = request.getObStaffTime().get(weekDay);
                if (oneDayTimes == null || oneDayTimes.get(staffId) == null) {
                    log.warn("no working hours for all staff on {} in OB", currLocalDate);
                    return result;
                }
                staffWorkingHourList = oneDayTimes.get(staffId).getTimeRange();
                if (CollectionUtils.isEmpty(staffWorkingHourList)) {
                    log.warn("no working hours for staff {} in OB", staffId);
                    return result;
                }
                // List<TimeRangeDto> staffCurrentWorkingHour =
                //     Objects.nonNull(currentStaffTime) && !currentStaffTime.getIsOverrideDate()
                //         ? currentStaffTime.getTimeRange()
                //         : List.of();
                // staffWorkingHourList = filterOutNotAvailableTimeForDay(
                //     staffCurrentWorkingHour, staffWorkingHourList, Collections.emptyList());
            }
            // limitDto = getLimitDto(request.getObStaffTime(), weekDay, staffId);
            limitGroups = getLimitGroups(request.getObStaffTime(), weekDay, staffId);
            if (CollectionUtils.isEmpty(staffWorkingHourList)) {
                log.warn("no working hours for staff {} in OB", staffId);
                return result;
            }
            Integer startTime = staffWorkingHourList.stream()
                    .map(TimeRangeDto::getStartTime)
                    .min(Integer::compareTo)
                    .get();
            Integer endTime = staffWorkingHourList.stream()
                    .map(TimeRangeDto::getEndTime)
                    .max(Integer::compareTo)
                    .get();
            staffWorkingHour = new TimeRangeDto(startTime, endTime);
        } else {
            // 开了 sync
            staffWorkingHourList = Objects.nonNull(currentStaffTime) ? currentStaffTime.getTimeRange() : List.of();
            if (CollectionUtils.isEmpty(staffWorkingHourList)) {
                log.warn("no working hours for staff {}, date {}", staffId, currLocalDate);
                return result;
            }
            Integer startTime = staffWorkingHourList.stream()
                    .map(TimeRangeDto::getStartTime)
                    .min(Integer::compareTo)
                    .get();
            Integer endTime = staffWorkingHourList.stream()
                    .map(TimeRangeDto::getEndTime)
                    .max(Integer::compareTo)
                    .get();
            staffWorkingHour = new TimeRangeDto(startTime, endTime);
            // limitDto = currentStaffTime.getLimitDto();
            limitGroups = currentStaffTime.getLimitGroups();
        }

        // boolean noMorePetQuantity = OBPetLimitService.judgePetLimit(
        //         request.getObPetLimitFilterDTO(), staffId, currLocalDate.toString(), limitDto, null);
        var petLimitResult = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                request.getObPetLimitFilterDTO(),
                staffId,
                currLocalDate.toString(),
                limitGroups,
                null,
                request.getObPetLimitFilterDTO().getPetIndexSubList());
        if (Boolean.FALSE.equals(petLimitResult.getIsAllPetsAvailable())) {
            return result;
        }

        SmartScheduleGroomingDetailsDTO preService = new SmartScheduleGroomingDetailsDTO();
        preService.setEndTime((long) staffWorkingHour.getStartTime());
        preService.setGroomingId(-1);
        preService.setServiceId(-1);
        preService.setCustomerId(-1);

        for (int i = 0; i < myServices.size(); i++) {
            SmartScheduleGroomingDetailsDTO curService = myServices.get(i);
            if (preService.getEndTime() <= curService.getStartTime()) {
                if (preService.getEndTime() >= staffWorkingHour.getEndTime()
                        || curService.getStartTime() > staffWorkingHour.getEndTime()) {
                    // preService 结束后，已到达ob关门时间
                    // curService.getStartTime的开始时间在ob关门后，不能作为最后一个slot的endTime
                    break;
                }
                TimeSlot slot = TimeSlot.builder()
                        .start(Math.toIntExact(preService.getEndTime()))
                        .end(Math.toIntExact(curService.getStartTime()))
                        .beforeApptId(preService.getGroomingId())
                        .beforeServiceId(preService.getServiceId())
                        .beforeCustomerId(preService.getCustomerId())
                        .beforeApptIsBlock(preService.getIsBlock())
                        .afterApptId(curService.getGroomingId())
                        .afterServiceId(curService.getServiceId())
                        .afterCustomerId(curService.getCustomerId())
                        .afterApptIsBlock(curService.getIsBlock())
                        .build();
                result.add(slot);
                preService = curService;
            } else if (preService.getEndTime() < curService.getEndTime()) {
                preService = curService;
            }
        }

        if (preService.getEndTime() < staffWorkingHour.getEndTime()) {
            result.add(TimeSlot.builder()
                    .start(Math.toIntExact(preService.getEndTime()))
                    .end(staffWorkingHour.getEndTime())
                    .beforeApptId(preService.getGroomingId())
                    .beforeServiceId(preService.getServiceId())
                    .beforeCustomerId(preService.getCustomerId())
                    .beforeApptIsBlock(preService.getIsBlock())
                    .afterApptId(-1)
                    .afterServiceId(-1)
                    .build());
        }
        // new resultList
        List<TimeSlot> newResultList = filterNonWorkingTime(result, staffWorkingHourList);
        log.info("build staff[{}] on date [{}] TimeSlotsV2 built: {}", staffId, currLocalDate, newResultList);

        // staff ss setting
        StaffSmartScheduleSettingDTO staffSsSetting =
                request.getStaffSsSettingMap().get(staffId);
        // 查询  customer addresses for all customer；若没有地址，从相邻 slot 获取
        if (!Boolean.TRUE.equals(request.getDisableSmartScheduling())) {
            fillServiceAddressV2(newResultList, staffSsSetting, request.getCustomerAddressDtoMap());
        }
        return newResultList;
    }

    private static List<LimitGroupDTO> getLimitGroups(
            final Map<Integer, Map<Integer, StaffTime>> obStaffTime, final int weekDay, final Integer staffId) {
        return Optional.ofNullable(obStaffTime)
                .map(map -> map.get(weekDay))
                .map(map -> map.get(staffId))
                .map(StaffTime::getLimitGroups)
                .orElse(new ArrayList<LimitGroupDTO>());
    }

    private static LimitDto getLimitDto(
            final Map<Integer, Map<Integer, StaffTime>> obStaffTime, final int weekDay, final Integer staffId) {
        return Optional.ofNullable(obStaffTime)
                .map(map -> map.get(weekDay))
                .map(map -> map.get(staffId))
                .map(StaffTime::getLimitDto)
                .orElse(null);
    }

    public static List<TimeRangeDto> getStaffCurrentWorkingHour(
            Map<Integer, Map<LocalDate, List<TimeRangeDto>>> dateStaffWorkingHours,
            Integer staffId,
            LocalDate currLocalDate) {
        Map<LocalDate, List<TimeRangeDto>> oneDayOfAllStaff = dateStaffWorkingHours.get(staffId);
        if (oneDayOfAllStaff == null) {
            log.warn("no working hours for all staff on {}", currLocalDate);
            return Collections.emptyList();
        }
        List<TimeRangeDto> staffWorkingHour = oneDayOfAllStaff.get(currLocalDate);
        if (staffWorkingHour == null) {
            log.warn("no working hours for staff {}", staffId);
            return Collections.emptyList();
        }
        return staffWorkingHour;
    }

    /**
     * @param myServices requires: service already sorted by start_time, not empty
     * @return free time slots for this staff of the day
     */
    static List<TimeSlot> buildFreeTimeSlots(List<MoeGroomingPetDetail> myServices, TimeSlot staffHour) {
        List<TimeSlot> result = new ArrayList<>();

        MoeGroomingPetDetail preService = new MoeGroomingPetDetail();
        preService.setEndTime((long) staffHour.getStart());
        preService.setGroomingId(-1);
        preService.setServiceId(-1);

        for (int i = 0; i < myServices.size(); i++) {
            MoeGroomingPetDetail curService = myServices.get(i);
            if (preService.getEndTime() <= curService.getStartTime()) {
                result.add(TimeSlot.builder()
                        .start(Math.toIntExact(preService.getEndTime()))
                        .end(Math.toIntExact(curService.getStartTime()))
                        .beforeApptId(preService.getGroomingId())
                        .beforeServiceId(preService.getServiceId())
                        .afterApptId(curService.getGroomingId())
                        .afterServiceId(curService.getServiceId())
                        .build());
                preService = curService;
            } else if (preService.getEndTime() < curService.getEndTime()) {
                preService = curService;
            }
        }

        if (preService.getEndTime() < staffHour.getEnd()) {
            result.add(TimeSlot.builder()
                    .start(Math.toIntExact(preService.getEndTime()))
                    .end(staffHour.getEnd())
                    .beforeApptId(preService.getGroomingId())
                    .beforeServiceId(preService.getServiceId())
                    .afterApptId(-1)
                    .afterServiceId(-1)
                    .build());
        }

        log.info("TimeSlots built: {}", result);
        return result;
    }

    /**
     * Query database and fill in before & after appointments customers' lat & lng
     */
    private void fillServiceAddress(List<TimeSlot> timeSlots, StaffSmartScheduleSettingDTO staffSsSetting) {
        log.info("filling current service address, start with timeslots: {}", timeSlots);
        Set<Integer> apptIds = new HashSet<>();
        timeSlots.forEach(t -> {
            apptIds.add(t.getAfterApptId());
            apptIds.add(t.getBeforeApptId());
        });
        apptIds.remove(-1);
        Map<Integer, CustomerAddressDto> customerAddress = new HashMap<>();
        Map<Integer, MoeGroomingAppointment> appointmentMap = new HashMap<>();
        if (!apptIds.isEmpty()) {
            List<MoeGroomingAppointment> appointments = appointmentMapper.queryByPrimaryIds(new ArrayList<>(apptIds));
            List<Integer> customerIds = appointments.stream()
                    .map(MoeGroomingAppointment::getCustomerId)
                    .distinct()
                    .filter(id -> id > 0) /* skip dummy customer for block appointment */
                    .collect(Collectors.toList());
            if (!customerIds.isEmpty()) {
                customerAddress = obAddressService.batchGetPrimaryAddress(customerIds);
            }
            appointmentMap =
                    appointments.stream().collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity()));
        }

        // ss location lat and lng
        String startLocationLat = "";
        String startLocationLng = "";
        String endLocationLat = "";
        String endLocationLng = "";
        if (Objects.nonNull(staffSsSetting) && Objects.nonNull(staffSsSetting.getLocation())) {
            startLocationLat = staffSsSetting.getLocation().getStartLat();
            startLocationLng = staffSsSetting.getLocation().getStartLng();
            endLocationLat = staffSsSetting.getLocation().getEndLat();
            endLocationLng = staffSsSetting.getLocation().getEndLng();
        }

        for (int i = 0; i < timeSlots.size(); i++) {
            TimeSlot timeSlot = timeSlots.get(i);
            if (timeSlot.getBeforeApptId() < 0) {
                assert i == 0;
                // start location
                timeSlot.setBeforeLat(startLocationLat);
                timeSlot.setBeforeLng(startLocationLng);
            } else {
                MoeGroomingAppointment beforeAppt = appointmentMap.get(timeSlot.getBeforeApptId());
                boolean beforeAddressExist = false;
                if (beforeAppt != null && beforeAppt.getCustomerId() > 0) {
                    timeSlot.setBeforeCustomerId(beforeAppt.getCustomerId());
                    CustomerAddressDto beforeAddress = customerAddress.get(beforeAppt.getCustomerId());
                    if (beforeAddress != null && beforeAddress.getCustomerAddressId() != null) {
                        beforeAddressExist = true;
                        timeSlot.setBeforeLat(beforeAddress.getLat());
                        timeSlot.setBeforeLng(beforeAddress.getLng());
                    }
                }
                if (!beforeAddressExist) {
                    // find previous timeSlot addresses(前一个time slot已经搜索了更前面的slot，所以只需检查前面一个slot就行，无需递归检查到start slot）
                    // 复杂度 o（N）
                    if (i == 0) {
                        // start location
                        timeSlot.setBeforeLat(startLocationLat);
                        timeSlot.setBeforeLng(startLocationLng);
                    } else {
                        TimeSlot prevSlot = timeSlots.get(i - 1);
                        if (prevSlot.isBeforeAddressValid()) {
                            timeSlot.setBeforeLat(prevSlot.getBeforeLat());
                            timeSlot.setBeforeLng(prevSlot.getBeforeLng());
                        }
                    }
                }
            }
        }
        for (int i = timeSlots.size() - 1; i >= 0; i--) {
            TimeSlot timeSlot = timeSlots.get(i);
            if (timeSlot.getAfterApptId() < 0) {
                assert i == timeSlots.size() - 1;
                // end location
                timeSlot.setAfterLat(endLocationLat);
                timeSlot.setAfterLng(endLocationLng);
            } else {
                MoeGroomingAppointment afterAppt = appointmentMap.get(timeSlot.getAfterApptId());
                boolean afterAddressExist = false;
                if (afterAppt != null && afterAppt.getCustomerId() > 0) {
                    timeSlot.setAfterCustomerId(afterAppt.getCustomerId());
                    CustomerAddressDto afterAddress = customerAddress.get(afterAppt.getCustomerId());
                    if (afterAddress != null && afterAddress.getCustomerAddressId() != null) {
                        afterAddressExist = true;
                        timeSlot.setAfterLat(afterAddress.getLat());
                        timeSlot.setAfterLng(afterAddress.getLng());
                    }
                }
                if (!afterAddressExist) {
                    if (i == timeSlots.size() - 1) {
                        // end location
                        timeSlot.setAfterLat(endLocationLat);
                        timeSlot.setAfterLng(endLocationLng);
                    } else {
                        // find next timeSlot addresses
                        TimeSlot nextSlot = timeSlots.get(i + 1);
                        if (nextSlot.isAfterAddressValid()) {
                            timeSlot.setAfterLat(nextSlot.getAfterLat());
                            timeSlot.setAfterLng(nextSlot.getAfterLng());
                        }
                    }
                }
            }
        }
        log.info("filled current service address, end with timeslots: {}", timeSlots);
    }

    /**
     * Query google api and fill in driving time and distance needed for:
     * 1. last appointment to new appointment
     * 2. new appointment to next appointment
     */
    private void fillDrivingData(List<TimeSlot> timeSlots, SmartScheduleRequest request) {
        log.info("filling driving data, start with timeslots: {}", timeSlots);
        Map<TimeSlot, Future<RouteMatrixElement>> driveInMap = new HashMap<>();
        Map<TimeSlot, Future<RouteMatrixElement>> driveOutMap = new HashMap<>();
        for (TimeSlot timeSlot : timeSlots) {
            if (timeSlot.isBeforeAddressValid()) {
                Future<RouteMatrixElement> call = ThreadPool.submit(() -> callGoogleAPI(
                        timeSlot.getBeforeLat(),
                        timeSlot.getBeforeLng(),
                        request.getAddressLat(),
                        request.getAddressLng()));
                driveInMap.put(timeSlot, call);
            } else {
                timeSlot.setDriveInMinutes(0);
                timeSlot.setDriveInMiles(0);
            }

            if (timeSlot.isAfterAddressValid()) {
                Future<RouteMatrixElement> call = ThreadPool.submit(() -> callGoogleAPI(
                        request.getAddressLat(),
                        request.getAddressLng(),
                        timeSlot.getAfterLat(),
                        timeSlot.getAfterLng()));
                driveOutMap.put(timeSlot, call);
            } else {
                timeSlot.setDriveOutMinutes(0);
                timeSlot.setDriveOutMiles(0);
            }
        }
        try {
            for (Map.Entry<TimeSlot, Future<RouteMatrixElement>> entry : driveInMap.entrySet()) {
                var before = entry.getValue().get();
                entry.getKey()
                        .setDriveInMinutes(Math.toIntExact(before.getDuration().getSeconds() / 60));
                entry.getKey().setDriveInMiles(GoogleMapService.metersToMiles1ScaleFloor(before.getDistance()));
            }
            for (Map.Entry<TimeSlot, Future<RouteMatrixElement>> entry : driveOutMap.entrySet()) {
                var after = entry.getValue().get();
                entry.getKey()
                        .setDriveOutMinutes(Math.toIntExact(after.getDuration().getSeconds() / 60));
                entry.getKey().setDriveOutMiles(GoogleMapService.metersToMiles1ScaleFloor(after.getDistance()));
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Failed calling google api: {}, {}", e.getMessage(), e);
            throw new CommonException(ResponseCodeEnum.GOOGLE_INVALID_ADDRESS, "Failed calling google api", e);
        }

        log.info("Filled driving data, got timeslots: {}", timeSlots);
    }

    public RouteMatrixElement callGoogleAPI(String fromLat, String fromLng, String toLat, String toLng) {
        if (StringUtils.isEmpty(fromLat)
                || StringUtils.isEmpty(fromLng)
                || StringUtils.isEmpty(toLat)
                || StringUtils.isEmpty(toLng)) {
            throw new CommonException(
                    ResponseCodeEnum.GOOGLE_INVALID_ADDRESS,
                    String.format(
                            "Exception parameters: fromLat [%s], fromLng [%s], toLat [%s], toLng [%]",
                            fromLat, fromLng, toLat, toLng));
        }

        var from = GoogleMapService.toGoogleLatLng(fromLat, fromLng);
        var to = GoogleMapService.toGoogleLatLng(toLat, toLng);
        return googleMapService.queryMatrix(from, to);
    }

    public String getZipcodeByLatLng(String lat, String lng) {
        double doubleLat = Double.parseDouble(lat);
        double doubleLng = Double.parseDouble(lng);
        var address = googleMapService.queryAddress(doubleLat, doubleLng);
        if (address != null && address.hasPostalCode()) {
            return address.getPostalCode();
        }
        return null;
    }

    private boolean meetEnoughTime(TimeSlot t, SmartScheduleRequest request) {
        return (t.getEnd() - t.getStart()
                >= SmartScheduleUtil.getTotalDuration(t, request.getServiceDuration(), request.getBufferTime())
                        + t.getDriveInMinutes()
                        + t.getDriveOutMinutes());
    }

    private boolean meetServiceArea(SmartScheduleRequest request) {
        // todo call service area api
        return true;
    }

    private boolean meetCertainAreaCertainDay(SmartScheduleRequest request) {
        // todo call certain area certain day api
        return true;
    }

    /**
     * Get all staffs' working hours for the day.
     */
    private Map<Integer, TimeSlot> getStaffWorkingHours(Integer businessId, SmartScheduleRequest request) {
        WorkingDailyQueryRangeVo queryRangeVo = new WorkingDailyQueryRangeVo();
        queryRangeVo.setStaffIdList(request.getStaffIds());
        queryRangeVo.setStartDate(request.getDate());
        queryRangeVo.setEndDate(request.getDate());
        ResponseResult<List<StaffWorkingRangeDto>> workingHours = iBusinessStaffClient.queryRange(
                businessId, request.getStaffIds().get(0), queryRangeVo);
        Map<Integer, TimeSlot> staffHoursMap = new HashMap<>();
        workingHours.getData().stream()
                .filter(r -> !r.getTimeRange().get(request.getDate()).isEmpty())
                .forEach(r -> {
                    TimeRangeDto range = r.getTimeRange().get(request.getDate()).get(0);
                    staffHoursMap.put(
                            r.getStaffId(),
                            TimeSlot.builder()
                                    .start(range.getStartTime())
                                    .end(range.getEndTime())
                                    .build());
                });
        return staffHoursMap;
    }

    private void validateInput(SmartScheduleRequest request) {
        if (request.getStaffIds() == null
                || request.getStaffIds().isEmpty()
                || request.getDate() == null
                || request.getAddressLat() == null
                || request.getAddressLng() == null
                || request.getServiceDuration() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Request parameters can't be null or empty.");
        }
    }

    private Map<Integer, StaffSmartScheduleSettingDTO> getStaffSsSettingMap(
            Integer businessId, List<Integer> staffIds) {
        return iBusinessSmartSchedulingClient.getStaffSmartScheduleSettingMap(
                new GetSmartScheduleSettingParams(businessId, staffIds));
    }

    private static Interval buildInterval(long startTimestamp, long endTimestamp) {
        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder().setSeconds(startTimestamp).build())
                .setEndTime(Timestamp.newBuilder().setSeconds(endTimestamp).build())
                .build();
    }
}
