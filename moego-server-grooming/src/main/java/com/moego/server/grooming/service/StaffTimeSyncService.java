package com.moego.server.grooming.service;

import com.google.gson.reflect.TypeToken;
import com.google.type.DayOfWeek;
import com.moego.common.utils.GsonUtil;
import com.moego.common.utils.WeekUtil;
import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.BookingLimitationDef;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.SlotAvailabilityDayDef;
import com.moego.idl.models.organization.v1.SlotDailySettingDef;
import com.moego.idl.models.organization.v1.SlotHourSettingDef;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.idl.models.organization.v1.TimeAvailabilityDayDef;
import com.moego.idl.models.organization.v1.TimeDailySettingDef;
import com.moego.idl.models.organization.v1.TimeHourSettingDef;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.UpdateStaffAvailabilityRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.mapper.MoeBookOnlinePetLimitBreedBindingMapper;
import com.moego.server.grooming.mapper.MoeBookOnlinePetLimitMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineStaffTimeMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlinePetLimit;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffTime;
import com.moego.server.grooming.mapstruct.BookOnlineStaffTimeMapper;
import com.moego.server.grooming.utils.StaffTimeSyncUtil;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 用于兼容旧接口，将 staff times 的数据同步到新的表中
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StaffTimeSyncService {

    private final MoeBookOnlineStaffTimeMapper bookOnlineStaffTimeMapper;
    private final OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub obStaffAvailabilityService;
    private final MoeBookOnlinePetLimitMapper moeBookOnlinePetLimitMapper;
    private final StaffAvailableSyncService staffAvailableSyncService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;
    private final MoeBookOnlinePetLimitBreedBindingMapper moeBookOnlinePetLimitBreedBindingMapper;
    private final com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;

    public static final Map<String, DayOfWeek> DEFAULT_WEEK_MAP = Map.of(
            WeekUtil.KEY_OF_MONDAY, DayOfWeek.MONDAY,
            WeekUtil.KEY_OF_TUESDAY, DayOfWeek.TUESDAY,
            WeekUtil.KEY_OF_WEDNESDAY, DayOfWeek.WEDNESDAY,
            WeekUtil.KEY_OF_THURSDAY, DayOfWeek.THURSDAY,
            WeekUtil.KEY_OF_FRIDAY, DayOfWeek.FRIDAY,
            WeekUtil.KEY_OF_SATURDAY, DayOfWeek.SATURDAY,
            WeekUtil.KEY_OF_SUNDAY, DayOfWeek.SUNDAY);

    /**
     * 重定向 bookOnlineStaffTimeMapper 的方法，插入数据后，异步触发数据同步
     *
     * @param record
     * @return
     */
    public int insertSelective(MoeBookOnlineStaffTime record) {
        var row = bookOnlineStaffTimeMapper.insertSelective(record);
        ThreadPool.execute(() -> {
            asyncStaffTimeWithEnable(record.getId());
        });
        return row;
    }

    /**
     * 重定向 bookOnlineStaffTimeMapper 的方法，更新数据后，异步触发数据同步
     *
     * @param record
     * @return
     */
    public int updateByPrimaryKeySelective(Integer businessId, Integer staffId, MoeBookOnlineStaffTime record) {
        var staffTime = bookOnlineStaffTimeMapper.selectByStaffId(businessId, staffId);
        if (staffTime == null) {
            return 0;
        }
        record.setId(staffTime.getId());
        var row = bookOnlineStaffTimeMapper.updateByPrimaryKeySelective(record);
        ThreadPool.execute(() -> {
            asyncStaffTime(record.getId());
        });
        return row;
    }

    /**
     * 同步 staff time 到新库
     *
     * @param id
     */
    public void asyncStaffTime(Integer id) {
        var staffTime = bookOnlineStaffTimeMapper.selectByPrimaryKey(id);
        if (staffTime == null) {
            return;
        }
        // 解析转化 json
        var timeObj = BookOnlineStaffTimeMapper.INSTANCE.entity2DTO(staffTime);
        // 查询 staff available 状态
        var staffAvailable =
                staffAvailableSyncService.queryStaffObAvailable(staffTime.getBusinessId(), staffTime.getStaffId());
        // 保存到新表
        var staffAvailableDefBuilder = StaffAvailabilityDef.newBuilder();
        if (timeObj.getStaffSlots() != null) {
            staffAvailableDefBuilder.addAllSlotAvailabilityDayList(staffSlotConvertDayDef(timeObj.getStaffSlots()));
        }
        obStaffAvailabilityService.updateStaffAvailability(UpdateStaffAvailabilityRequest.newBuilder()
                .setCompanyId(staffTime.getCompanyId())
                .setBusinessId(staffTime.getBusinessId())
                .addStaffAvailabilityList(staffAvailableDefBuilder
                        .setStaffId(staffTime.getStaffId())
                        .setIsAvailable(staffAvailable)
                        .addAllTimeAvailabilityDayList(staffTimeConvertDayDef(timeObj.getStaffTimes()))
                        .build())
                .build());
    }

    public void asyncStaffTimeWithEnable(Integer id) {
        var staffTime = bookOnlineStaffTimeMapper.selectByPrimaryKey(id);
        if (staffTime == null) {
            return;
        }
        // 解析转化 json
        var timeObj = BookOnlineStaffTimeMapper.INSTANCE.entity2DTO(staffTime);
        // 保存到新表
        var staffAvailableDefBuilder = StaffAvailabilityDef.newBuilder();
        if (timeObj.getStaffSlots() != null) {
            staffAvailableDefBuilder.addAllSlotAvailabilityDayList(staffSlotConvertDayDef(timeObj.getStaffSlots()));
        }
        obStaffAvailabilityService.updateStaffAvailability(UpdateStaffAvailabilityRequest.newBuilder()
                .setCompanyId(staffTime.getCompanyId())
                .setBusinessId(staffTime.getBusinessId())
                .addStaffAvailabilityList(staffAvailableDefBuilder
                        .setStaffId(staffTime.getStaffId())
                        .setIsAvailable(true)
                        .addAllTimeAvailabilityDayList(staffTimeConvertDayDef(timeObj.getStaffTimes()))
                        .build())
                .build());
    }

    /**
     * 将 StaffSlot 转化为 Slot...DayDef
     *
     * @param staffSlots
     * @return
     */
    private List<SlotAvailabilityDayDef> staffSlotConvertDayDef(
            Map<String, BookOnlineStaffTimeDTO.StaffSlot> staffSlots) {
        // slot day availability def
        List<SlotAvailabilityDayDef> slotAvailabilityDayList = new ArrayList<>();
        for (Map.Entry<String, BookOnlineStaffTimeDTO.StaffSlot> entry : staffSlots.entrySet()) {
            var staffSlotDto = entry.getValue();
            // 转化小时数据
            List<SlotHourSettingDef> slotHourSettingList = new ArrayList<>();
            Integer minStartTime = 540;
            Integer maxEndTime = 18 * 60;
            Integer capacity = 0;
            for (BookOnlineStaffTimeDTO.StaffSlot.CapacityTimeslot capacityTimeslot : staffSlotDto.getTimeSlot()) {
                var builder = SlotHourSettingDef.newBuilder()
                        .setStartTime(capacityTimeslot.getStartTime())
                        .setCapacity(capacityTimeslot.getCapacity() == null ? 0 : capacityTimeslot.getCapacity());
                if (!CollectionUtils.isEmpty(capacityTimeslot.getLimitIds())) {
                    builder.setLimit(limitIdsConvertToLimitationDef(capacityTimeslot.getLimitIds()));
                }
                slotHourSettingList.add(builder.build());
                minStartTime = Math.min(minStartTime, capacityTimeslot.getStartTime());
                maxEndTime = Math.max(maxEndTime, capacityTimeslot.getStartTime());
                capacity += builder.getCapacity();
            }
            slotAvailabilityDayList.add(SlotAvailabilityDayDef.newBuilder()
                    .setDayOfWeek(DEFAULT_WEEK_MAP.get(entry.getKey()))
                    .setIsAvailable(staffSlotDto.getIsSelected())
                    .addAllSlotHourSettingList(slotHourSettingList)
                    .setSlotDailySetting(SlotDailySettingDef.newBuilder()
                            .setStartTime(minStartTime)
                            .setEndTime(maxEndTime)
                            .setCapacity(capacity)
                            .build())
                    .build());
        }
        return slotAvailabilityDayList;
    }

    /**
     * 将 StaffTime 转化为 Time...DayDef
     *
     * @param staffTimes
     * @return
     */
    private List<TimeAvailabilityDayDef> staffTimeConvertDayDef(Map<String, StaffTime> staffTimes) {
        // slot day availability def
        List<TimeAvailabilityDayDef> timeAvailabilityDayList = new ArrayList<>();
        for (Map.Entry<String, StaffTime> entry : staffTimes.entrySet()) {
            var staffTimeDto = entry.getValue();
            // 转化小时数据
            List<TimeHourSettingDef> timeHourSettingList = new ArrayList<>();
            for (TimeRangeDto timeRangeDto : staffTimeDto.getTimeRange()) {
                var timeHourBuilder = TimeHourSettingDef.newBuilder()
                        .setStartTime(timeRangeDto.getStartTime())
                        .setEndTime(timeRangeDto.getEndTime());
                timeHourSettingList.add(timeHourBuilder.build());
            }
            // 构建 day builder
            var dayDefBuilder = TimeAvailabilityDayDef.newBuilder()
                    .setDayOfWeek(DEFAULT_WEEK_MAP.get(entry.getKey()))
                    .setIsAvailable(staffTimeDto.getIsSelected())
                    .addAllTimeHourSettingList(timeHourSettingList);
            // 检查 limit ids 是否存在
            if (!CollectionUtils.isEmpty(staffTimeDto.getLimitIds())) {
                dayDefBuilder.setTimeDailySetting(TimeDailySettingDef.newBuilder()
                        .setLimit(limitIdsConvertToLimitationDef(staffTimeDto.getLimitIds()))
                        .build());
            }
            timeAvailabilityDayList.add(dayDefBuilder.build());
        }
        return timeAvailabilityDayList;
    }

    /**
     * 将 limitids 转化为 lomitation def
     *
     * @param limitIds
     * @return
     */
    public BookingLimitationDef limitIdsConvertToLimitationDef(List<Long> limitIds) {
        if (CollectionUtils.isEmpty(limitIds)) {
            return BookingLimitationDef.newBuilder().build();
        }
        var limitList = moeBookOnlinePetLimitMapper.selectByIdList(null, limitIds);
        if (CollectionUtils.isEmpty(limitList)) {
            return BookingLimitationDef.newBuilder().build();
        }
        // pet size
        List<BookingLimitationDef.PetSizeLimitation> petSizeLimitationList = new ArrayList<>();
        Map<Long, Integer> breedBindingCapacityMap = new HashMap<>();
        for (MoeBookOnlinePetLimit petLimit : limitList) {
            // 根据不同的 type，分别处理
            if (BookOnlinePetLimitService.LIMIT_BY_SIZE == petLimit.getType()) {
                petSizeLimitationList.add(BookingLimitationDef.PetSizeLimitation.newBuilder()
                        .addAllPetSizeIds(Collections.singleton(petLimit.getFindId()))
                        // 当前不存在为 true 的 case
                        .setIsAllSize(false)
                        .setCapacity(petLimit.getMaxNumber())
                        .build());
            } else {
                breedBindingCapacityMap.put(petLimit.getFindId(), petLimit.getMaxNumber());
            }
        }
        // pet type + breed
        List<BookingLimitationDef.PetBreedLimitation> petBreedLimitationList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(breedBindingCapacityMap.keySet())) {
            var breedBindingList = moeBookOnlinePetLimitBreedBindingMapper.selectByIdList(
                    breedBindingCapacityMap.keySet().stream().toList());
            for (var breedBinding : breedBindingList) {
                petBreedLimitationList.add(BookingLimitationDef.PetBreedLimitation.newBuilder()
                        .setPetTypeId(breedBinding.getPetTypeId())
                        .setIsAllBreed(BookOnlinePetLimitService.ALL_BREED == breedBinding.getAllBreed())
                        .addAllBreedIds(
                                GsonUtil.fromJson(breedBinding.getBreedIdList(), new TypeToken<List<Long>>() {}))
                        .setCapacity(breedBindingCapacityMap.get(breedBinding.getId()))
                        .build());
            }
        }
        // init def
        var builder = BookingLimitationDef.newBuilder();
        if (!CollectionUtils.isEmpty(petSizeLimitationList)) {
            builder.addAllPetSizeLimits(petSizeLimitationList);
        }
        if (!CollectionUtils.isEmpty(petBreedLimitationList)) {
            builder.addAllPetBreedLimits(petBreedLimitationList);
        }
        return builder.build();
    }

    public Map<Integer, BookOnlineStaffTimeDTO> queryStaffTime(Integer businessId, List<Integer> staffIds) {
        // query companyId
        long companyId = businessServiceBlockingStub
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build())
                .getCompanyId();
        var requestBuilder =
                GetStaffAvailabilityRequest.newBuilder().setCompanyId(companyId).setBusinessId(businessId);
        if (!CollectionUtils.isEmpty(staffIds)) {
            requestBuilder.addAllStaffIdList(
                    staffIds.stream().map(Integer::longValue).toList());
        }
        var response = obStaffAvailabilityService.getStaffAvailability(requestBuilder.build());
        return response.getStaffAvailabilityListList().stream()
                .collect(Collectors.toMap(
                        staffAvailability -> (int) staffAvailability.getStaffId(),
                        staffAvailability ->
                                StaffTimeSyncUtil.convertProtoStaffAvailabilityToDto(businessId, staffAvailability),
                        (a, b) -> b));
    }

    public Map<Integer, BookOnlineStaffTimeDTO> queryStaffTime(
            Integer businessId, Long companyId, Collection<Integer> staffIds, AvailabilityType availabilityType) {
        var requestBuilder = GetStaffAvailabilityRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAvailabilityType(availabilityType);
        if (!CollectionUtils.isEmpty(staffIds)) {
            requestBuilder.addAllStaffIdList(
                    staffIds.stream().map(Integer::longValue).toList());
        }
        var response = obStaffAvailabilityService.getStaffAvailability(requestBuilder.build());
        return response.getStaffAvailabilityListList().stream()
                .collect(Collectors.toMap(
                        staffAvailability -> (int) staffAvailability.getStaffId(),
                        staffAvailability ->
                                StaffTimeSyncUtil.convertProtoStaffAvailabilityToDto(businessId, staffAvailability),
                        (a, b) -> b));
    }

    public Map<Integer, StaffAvailability> queryShiftManagementStaffSlot(
            Integer businessId, Long companyId, Collection<Integer> staffIds) {
        var requestBuilder = com.moego.idl.service.organization.v1.GetStaffAvailabilityRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAvailabilityType(AvailabilityType.BY_SLOT);
        if (!CollectionUtils.isEmpty(staffIds)) {
            requestBuilder.addAllStaffIdList(
                    staffIds.stream().map(Integer::longValue).toList());
        }

        var response = staffService.getStaffAvailability(requestBuilder.build());
        return response.getStaffAvailabilityListList().stream()
                .collect(Collectors.toMap(
                        staffAvailability -> Math.toIntExact(staffAvailability.getStaffId()),
                        Function.identity(),
                        (a, b) -> b));
    }

    public Map<Integer, List<SlotAvailabilityDay>> queryShiftManagementOverrideStaffSlot(
            Integer businessId, Long companyId, Collection<Integer> staffIds) {
        var requestBuilder = com.moego.idl.service.organization.v1.GetStaffAvailabilityOverrideRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAvailabilityType(AvailabilityType.BY_SLOT);
        if (!CollectionUtils.isEmpty(staffIds)) {
            requestBuilder.addAllStaffIds(
                    staffIds.stream().map(Integer::longValue).distinct().toList());
        }

        var response = staffService.getStaffAvailabilityOverride(requestBuilder.build());
        return response.getOverrideDaysMap().entrySet().stream()
                .collect(Collectors.toMap(
                        staffAvailability -> Math.toIntExact(staffAvailability.getKey()),
                        staffAvailability -> staffAvailability.getValue().getSlotsList().stream()
                                .filter(day -> !(day.getIsAvailable()
                                        && day.getSlotDailySetting().getStartTime() < 0
                                        && day.getSlotDailySetting().getEndTime() < 0))
                                .toList()));
    }

    public Map<Integer, StaffAvailability> queryShiftManagementStaffTime(
            Integer businessId, Long companyId, Collection<Integer> staffIds) {
        var requestBuilder = com.moego.idl.service.organization.v1.GetStaffAvailabilityRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAvailabilityType(AvailabilityType.BY_TIME);
        if (!CollectionUtils.isEmpty(staffIds)) {
            requestBuilder.addAllStaffIdList(
                    staffIds.stream().map(Integer::longValue).toList());
        }

        var response = staffService.getStaffAvailability(requestBuilder.build());
        return response.getStaffAvailabilityListList().stream()
                .collect(Collectors.toMap(
                        staffAvailability -> Math.toIntExact(staffAvailability.getStaffId()),
                        Function.identity(),
                        (a, b) -> b));
    }

    public Map<Integer, List<TimeAvailabilityDay>> queryShiftManagementOverrideStaffTime(
            Integer businessId, Long companyId, Collection<Integer> staffIds) {
        var requestBuilder = com.moego.idl.service.organization.v1.GetStaffAvailabilityOverrideRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAvailabilityType(AvailabilityType.BY_TIME);
        if (!CollectionUtils.isEmpty(staffIds)) {
            requestBuilder.addAllStaffIds(
                    staffIds.stream().map(Integer::longValue).distinct().toList());
        }

        var response = staffService.getStaffAvailabilityOverride(requestBuilder.build());
        return response.getTimeOverrideDaysMap().entrySet().stream()
                .collect(Collectors.toMap(
                        staffAvailability -> Math.toIntExact(staffAvailability.getKey()),
                        staffAvailability -> staffAvailability.getValue().getSlotsList()));
    }

    public Map<String, Map<Integer, StaffTime>> getStaffTimeMap(
            final Collection<Integer> availableStaffIds,
            final String startDate,
            final String endDate,
            final LocalDate currentDate,
            final int nowMinutes,
            final Integer businessId,
            final Long companyId) {

        LocalDate startLocalDate = LocalDate.parse(startDate);
        LocalDate endLocalDate = LocalDate.parse(endDate);
        List<String> needQueryDays = Stream.iterate(startLocalDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startLocalDate, endLocalDate) + 1)
                .map(LocalDate::toString)
                .toList();
        return getStaffTimeMap(availableStaffIds, needQueryDays, currentDate, nowMinutes, businessId, companyId);
    }

    private Map<String, Map<Integer, StaffTime>> getStaffTimeMap(
            final Collection<Integer> availableStaffIds,
            final List<String> needQueryDays,
            final LocalDate currentDate,
            final int nowMinutes,
            final Integer businessId,
            final Long companyId) {
        var shiftManagementStaffTimeMap = queryShiftManagementStaffTime(businessId, companyId, availableStaffIds);
        var shiftManagementOverrideStaffTime =
                queryShiftManagementOverrideStaffTime(businessId, companyId, availableStaffIds);

        // 从timeslot的配置解析出需要查询日期里的所有的timeslot，以date为key，每个staff的timeslot的map为值
        Map<String /* date */, Map<Integer /* staff id */, StaffTime>> timeslotMapByDate =
                new LinkedHashMap<>(needQueryDays.size());

        needQueryDays.forEach(date -> {
            var localDate = LocalDate.parse(date);
            var staffMap = shiftManagementStaffTimeMap.entrySet().stream()
                    .map(entry -> shiftManagementOverrideStaffTime.getOrDefault(entry.getKey(), List.of()).stream()
                            .filter(override -> Objects.equals(override.getOverrideDate(), date))
                            .findFirst()
                            .orElseGet(() -> getStaffSlotTimeDataByCurrentDate(entry.getValue(), localDate)))
                    .collect(Collectors.toMap(
                            timeAvailabilityDay -> Math.toIntExact(timeAvailabilityDay.getStaffId()),
                            Function.identity()));

            staffMap.forEach((staffId, timeAvailabilityDay) -> {
                if (!availableStaffIds.contains(staffId) || !timeAvailabilityDay.getIsAvailable()) {
                    return;
                }

                var timeslotDTOList = timeAvailabilityDay.getTimeHourSettingListList().stream()
                        .map(timeSlot -> {
                            TimeRangeDto timeRangeDto = new TimeRangeDto();
                            timeRangeDto.setStartTime(timeSlot.getStartTime());
                            timeRangeDto.setEndTime(timeSlot.getEndTime());
                            return timeRangeDto;
                        })
                        .toList();

                var timeDailySetting = timeAvailabilityDay.getTimeDailySetting();

                // convert dto
                StaffTime timeslotDTO = new StaffTime();
                timeslotDTO.setIsSelected(timeAvailabilityDay.getIsAvailable());
                timeslotDTO.setTimeRange(timeslotDTOList);
                // timeslotDTO.setLimitDto(StaffTimeSyncUtil.convertDailySettingToDto(timeDailySetting));
                timeslotDTO.setIsOverrideDate(StringUtils.hasText(timeAvailabilityDay.getOverrideDate()));
                timeslotDTO.setLimitGroups(
                        StaffTimeSyncUtil.convertProtoLimitationToGroupDto(timeDailySetting.getLimitationGroupsList()));

                // if today filter out of today time range
                if (Objects.equals(currentDate.toString(), date)) {
                    timeslotDTO.setTimeRange(timeslotDTO.getTimeRange().stream()
                            .map(slot -> {
                                if (slot.getEndTime() < nowMinutes) {
                                    return null;
                                }
                                if (slot.getStartTime() >= nowMinutes) {
                                    return slot;
                                }
                                slot.setStartTime(nowMinutes);
                                return slot;
                            })
                            .filter(Objects::nonNull)
                            .toList());
                }

                // 以staffId为key，存放当天内的timeslot
                timeslotMapByDate.computeIfAbsent(date, k -> new HashMap<>()).put(staffId, timeslotDTO);
            });
        });
        if (log.isDebugEnabled()) {
            log.debug("timeslotMapByDate: {}", timeslotMapByDate);
        }
        return timeslotMapByDate;
    }

    static TimeAvailabilityDay getStaffSlotTimeDataByCurrentDate(
            StaffAvailability availability, LocalDate currentLocalDate) {
        var timeAvailabilityDayListList = availability.getTimeAvailabilityDayListList();
        LocalDate workingHourStartDate = LocalDate.parse(availability.getTimeStartSunday());
        var scheduleType = availability.getTimeScheduleType();
        var dayOfWeek = currentLocalDate.getDayOfWeek();

        // 如果设置为每周相同时间，则直接按星期获取时间数据
        if (Objects.equals(scheduleType, ScheduleType.ONE_WEEK)) {
            return timeAvailabilityDayListList.stream()
                    .filter(availabilityDay -> ScheduleType.ONE_WEEK.equals(availabilityDay.getScheduleType()))
                    .filter(availabilityDay -> Objects.equals(
                            dayOfWeek.getValue(), availabilityDay.getDayOfWeek().getNumber()))
                    .findFirst()
                    .orElse(null);
        }

        // 计算 date 距离 absoluteStartDate 的天数差
        long daysBetween = ChronoUnit.DAYS.between(workingHourStartDate, currentLocalDate);
        // 计算 date 是距离 absoluteStartDate 的星期差
        long weeksBetween = Math.floorDiv(daysBetween, 7);
        // 计算循环中第几周，1 是第一周，2 是第二周，以此类推
        int weekNumber = Math.toIntExact(Math.floorMod(weeksBetween, scheduleType.getNumber())) + 1;

        return timeAvailabilityDayListList.stream()
                .filter(availabilityDay -> Objects.equals(
                        weekNumber, availabilityDay.getScheduleType().getNumber()))
                .filter(availabilityDay -> Objects.equals(
                        dayOfWeek.getValue(), availabilityDay.getDayOfWeek().getNumber()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 保留 select 给前端 setting 的访问
     * @param businessId
     * @return
     */
    public List<MoeBookOnlineStaffTime> selectByBusinessId(Integer businessId) {
        return bookOnlineStaffTimeMapper.selectByBusinessId(businessId);
    }

    /**
     * 手动执行，触发数据同步，将 staff time 数据同步到新表
     */
    public void taskSyncAllTimeRecord() {
        // 触发 staff time sync
        int max_id = bookOnlineStaffTimeMapper.selectMaxId() + 1000;
        IntStream.range(0, max_id).forEach(id -> {
            try {
                asyncStaffTime(id);
            } catch (Exception e) {
                log.error("asyncStaffTime error, staff time id:{}", id, e);
            }
        });
    }

    public void syncOneStaffTimesRecord(Integer id) {
        asyncStaffTime(id);
    }
}
