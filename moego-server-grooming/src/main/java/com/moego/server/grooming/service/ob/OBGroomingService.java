package com.moego.server.grooming.service.ob;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.idl.models.offering.v1.ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.lib.utils.CollectionUtils.firstElement;
import static com.moego.server.grooming.enums.AppointmentAction.SUBMIT_OB;
import static com.moego.svc.activitylog.event.enums.ResourceType.APPOINTMENT;
import static java.util.Comparator.comparing;
import static java.util.Comparator.comparingInt;
import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsFirst;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.moego.common.dto.notificationDto.NotificationExtraOBReqestDto;
import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ScopeModifyTypeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.risk_control.v1.RecaptchaAction;
import com.moego.idl.models.risk_control.v1.RecaptchaVersion;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveRequest;
import com.moego.idl.service.appointment.v1.UpdatePetDetailRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.SyncBookingRequestFromAppointmentRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestStatusRequest;
import com.moego.idl.service.risk_control.v1.RecaptchaServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.risk.control.config.RiskControlConstant;
import com.moego.lib.risk.control.recaptcha.RecaptchaUtils;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.api.IProfileRequestAddressService;
import com.moego.server.customer.client.ICustomerGroomingClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.GroomingQueryCustomerParams;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.BookOnlineAutoMoveAppointmentDTO;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerHasRequestDTO;
import com.moego.server.grooming.dto.CustomerLastFinishedApptMapDto;
import com.moego.server.grooming.dto.GroomingBookingDTO;
import com.moego.server.grooming.dto.GroomingCustomerPetdetailDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentGroupSettingDTO;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.dto.ss.ScheduleTimeSlot;
import com.moego.server.grooming.dto.waitlist.WaitListCompatibleDTO;
import com.moego.server.grooming.enums.AbandonDeleteTypeEnum;
import com.moego.server.grooming.enums.AppointmentSourcePlatform;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.OBRequestSubmittedAutoTypeEnum;
import com.moego.server.grooming.enums.PetDetailStatusEnum;
import com.moego.server.grooming.eventbus.OnlineBookingProducer;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.AutoAssignMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapper.OrderDecouplingFlowMarkerMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.AutoAssignExample;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeWaitList;
import com.moego.server.grooming.mapperbean.OrderDecouplingFlowMarker;
import com.moego.server.grooming.mapstruct.DatePreferenceMapper;
import com.moego.server.grooming.mapstruct.TimePreferenceMapper;
import com.moego.server.grooming.params.AppointmentParams;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.params.InvoiceAmountVo;
import com.moego.server.grooming.params.InvoiceValueType;
import com.moego.server.grooming.params.PetDetailParams;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.server.grooming.service.ActiveMQService;
import com.moego.server.grooming.service.CalendarSyncService;
import com.moego.server.grooming.service.GroomingApptAsyncService;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.InvoiceService;
import com.moego.server.grooming.service.MembershipService;
import com.moego.server.grooming.service.MoeAppointmentQueryService;
import com.moego.server.grooming.service.MoeBookOnlineDepositService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.MoeGroomingNoteService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.WaitListService;
import com.moego.server.grooming.service.bo.AssignBO;
import com.moego.server.grooming.service.dto.OBAvailableTimeDto;
import com.moego.server.grooming.service.dto.OBAvailableTimeStaffAvailableTimeDto;
import com.moego.server.grooming.service.dto.OBAvailableTimeStaffDto;
import com.moego.server.grooming.service.dto.OBSubmitResult;
import com.moego.server.grooming.service.utils.ServiceUtil;
import com.moego.server.grooming.web.vo.GroomingBookingQueryVO;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.params.OnlineBookWaitingNotifyParams;
import com.moego.server.message.params.notification.ob.NotificationOBRequestReceivedParams;
import com.moego.server.payment.client.IPaymentStripeClient;
import com.moego.server.payment.dto.CustomerStripInfoSaveResponse;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020-11-13
 */
@Service
@Slf4j
public class OBGroomingService {

    public static final String PM = "pm";

    public static final String OB_AUTO_MOVE_WAITLIST_SCROLL_ID = "ob:auto_move_waitlist:scroll_id";

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private INotificationClient iNotificationClient;

    @Autowired
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Autowired
    private MoeGroomingBookOnlineService moeGroomingBookOnlineService;

    @Autowired
    private MoeBookOnlineDepositService moeBookOnlineDepositService;

    @Autowired
    private GroomingApptAsyncService groomingApptAsyncService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private IPetClient iPetClient;

    @Autowired
    private PrepayService prepayService;

    @Autowired
    private OBCustomerService customerService;

    @Autowired
    private MoeBookOnlineAbandonRecordMapper abandonRecordMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private PetDetailMapperProxy petDetailMapper;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private ICustomerGroomingClient iCustomerGroomingClient;

    @Autowired
    private MoeAppointmentQueryService appointmentQueryService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private OBPetService petService;

    @Autowired
    private AutoAssignMapper autoAssignMapper;

    @Autowired
    private OBClientTimeSlotService timeSlotService;

    @Autowired
    private RecaptchaServiceGrpc.RecaptchaServiceBlockingStub recaptchaServiceBlockingStub;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ICustomerCustomerService customerApi;

    @Autowired
    private OBAddressService obAddressService;

    @Resource
    private OrderService orderService;

    @Autowired
    private OBBusinessService businessService;

    @Autowired
    private OBBusinessStaffService obBusinessStaffService;

    @Autowired
    private WaitListService waitListService;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private IProfileRequestAddressService profileRequestAddressApi;

    @Autowired
    private ActiveMQService activeMQService;

    @Autowired
    private ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;

    @Autowired
    private PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailStub;

    @Autowired
    private AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    @Autowired
    private OnlineBookingProducer onlineBookingProducer;

    @Autowired
    private MembershipService membershipService;

    @Autowired
    private BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestStub;

    @Autowired
    private IPaymentStripeClient iPaymentStripeClient;

    @Autowired
    private MoeGroomingNoteService moeGroomingNoteService;

    @Autowired
    private OrderDecouplingFlowMarkerMapper orderDecouplingFlowMarkerMapper;

    @Autowired
    private CalendarSyncService calendarSyncService;

    @Autowired
    private com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;

    @Autowired
    private OBDepositService obDepositService;

    @Autowired
    private NewOrderHelper newOrderHelper;

    /**
     * @deprecated by Freeman, use {@link #submitBookingRequest(BookOnlineSubmitParams, MoeBusinessBookOnline)} instead
     */
    @Deprecated(forRemoval = true, since = "2024/12/25")
    public OBSubmitResult bookOnlineSubmit(BookOnlineSubmitParams obParams, MoeBusinessBookOnline bookOnline) {
        // 检查OB定金支付状态
        MoeBookOnlineDeposit obDeposit = moeBookOnlineDepositService.getOBDepositByDeGuid(obParams.getPrepayGuid());
        checkOBDepositStatus(obDeposit, bookOnline, obParams.getCustomerData().getCustomerId());

        setDateAndTimeIfNecessary(obParams);

        // insert or update customer info
        boolean isNewCustomer = Objects.isNull(obParams.getCustomerData().getCustomerId());
        Integer customerId;
        boolean canAutoAccept;
        // 如果不存在 customer id， 当新用户处理
        if (isNewCustomer) {
            customerId = customerService.newCustomer(obParams);
            canAutoAccept = StringUtils.hasText(obParams.getAppointmentDate())
                    && OBRequestSubmittedAutoTypeEnum.isAutoAcceptRequest(
                            bookOnline.getRequestSubmittedAutoType(), false);
        } else {
            obAddressService.createCustomerAddress(obParams.getCustomerData());

            customerId = obParams.getCustomerData().getCustomerId();
            boolean autoAcceptAll =
                    OBRequestSubmittedAutoTypeEnum.isAutoAcceptConflict(bookOnline.getRequestSubmittedAutoType());
            // OB-C update info
            customerService.updateCustomerInfo(obParams, autoAcceptAll);
            CustomerHasRequestDTO requestDTO = customerService.getCustomerHasRequestUpdate(
                    obParams.getBusinessId(), obParams.getCustomerData().getCustomerId());
            canAutoAccept = StringUtils.hasText(obParams.getAppointmentDate())
                    && OBRequestSubmittedAutoTypeEnum.isAutoAcceptRequest(
                            bookOnline.getRequestSubmittedAutoType(), requestDTO.hasRequestUpdate());
            if (obParams.getFromPetParentPortal() != null && obParams.getFromPetParentPortal()) {
                obAddressService.setPrimaryAddress(obParams.getCustomerData().getAddressId());
            }
        }

        // 记录卡信息, 如果绑卡失败，要回退上面的修改
        if (!Boolean.TRUE.equals(obParams.getCustomerData().getHasStripeCard())) {
            prepayService.saveCard(
                    obParams.getCustomerData().getChargeToken(), obParams.getBusinessId(), customerId, isNewCustomer);
        }

        // 保存pet data
        petService.savePet(customerId, obParams);

        // assemble the appointment information and insert
        AppointmentParams appointmentParams =
                assembleAppointmentInfo(customerId, canAutoAccept, obParams, bookOnline, isNewCustomer);

        ActivityLogRecorder.recordRoot(obParams.getBusinessId(), SUBMIT_OB, APPOINTMENT, null, obParams);

        AddResultDTO appointmentResultData = moeGroomingAppointmentService.addGroomingAppointment(appointmentParams);

        Long companyId = bookOnline.getCompanyId();
        Integer businessId = bookOnline.getBusinessId();
        Integer appointmentId = appointmentResultData.getId();
        Integer invoiceId = appointmentResultData.getInvoiceId();

        if (needPayment(obParams)) {
            // 注意：老流程（先 pay 后 submit）肯定已经是 confirmed PaymentIntent
            // 由于 confirm PaymentIntent 回调里修改为 PROCESSING 和创建 BookingRequest 这两个逻辑是异步的，
            // 不能确定是回调先执行还是 BookingRequest 先创建，所以这里必须手动将 PaymentStatus 设置为 PROCESSING
            updateBookingRequestPaymentStatus(businessId, appointmentId, BookingRequestModel.PaymentStatus.PROCESSING);
        }

        // use discount code
        if (Objects.nonNull(obParams.getDiscountCodeParams())) {
            orderService.setDiscountWithDiscountCodeId(
                    obParams.getBusinessId(),
                    invoiceId,
                    obParams.getDiscountCodeParams().getDiscountCodeId());
        }

        // membership info for obc submit grooming
        ThreadPool.execute(() -> {
            if (Objects.isNull(obParams.getMembershipParams())) {
                return;
            }
            membershipService.applyMemberships(
                    businessId.longValue(),
                    companyId,
                    invoiceId.longValue(),
                    0L,
                    obParams.getMembershipParams().getMembershipIds());
        });

        if (obParams.getPreAuthDetail() != null) {
            prepayService.insertPreAuthRecord(obParams, appointmentId);

            // Yunxiang: 在 PreAuth 的场景，因为整体的支付流程会非常长，并且存在最后 Capture 的时候调整实际收取
            // 的金额的场景。复用其他即时支付的增加 Tips 的逻辑（即支付成功后再增加到 Order 上）会有 Tips 丢失的风险。
            // 此外，在 PreAuth Capture 之前，Business 都无法在订单上看到 Client 添加的 Tips 导致误解。
            // 因此，当 PreAuth 有 Tips 时，应当在创建订单的时候就添加到订单上。
            BigDecimal tipsAmount = obParams.getPreAuthDetail().getTipsAmount();
            if (tipsAmount != null && tipsAmount.compareTo(BigDecimal.ZERO) > 0) {
                orderService.setTips(
                        businessId,
                        null,
                        InvoiceAmountVo.builder()
                                .invoiceId(invoiceId)
                                .valueType(InvoiceValueType.AMOUNT.value())
                                .value(obParams.getPreAuthDetail().getTipsAmount())
                                .build());
            }
        }
        // 记录customer 签名信息： save result to moe_customer_agreement (depend on: moe_business_agreement)
        moeGroomingBookOnlineService.saveAgreementForObSubmit(obParams.getAgreements(), appointmentId);

        // 有deposit的情况下，更新对应的payment记录、创建invoice deposit记录、绑定stripe customer
        if (!Objects.isNull(obDeposit)) {
            obDeposit.setGroomingId(appointmentId);
            obParams.getCustomerData().setCustomerId(customerId);
            prepayService.updateDepositAndPaymentRecord(obDeposit, obParams, invoiceId);
        }
        if (canAutoAssign(bookOnline, obParams)) {
            ThreadPool.execute(() -> {
                autoAssignAndSendNotification(obParams, appointmentId, customerId, canAutoAccept);
                if (!canAutoAccept) {
                    activeMQService.publishBookingRequestEvent(new BookingRequestEventParams()
                            .setBusinessId(obParams.getBusinessId())
                            .setAppointmentId(appointmentId)
                            .setEvent(BookingRequestEventParams.BookingRequestEvent.SUBMITTED));
                }
            });
        } else {
            ThreadPool.execute(() -> {
                if (canAutoAccept) {
                    sendAcceptNotification(companyId, businessId, appointmentId);
                } else {
                    sendSubmitNotification(companyId, businessId, appointmentId, null);
                    activeMQService.publishBookingRequestEvent(new BookingRequestEventParams()
                            .setBusinessId(businessId)
                            .setAppointmentId(appointmentId)
                            .setEvent(BookingRequestEventParams.BookingRequestEvent.SUBMITTED));
                }
            });
            asyncNotificationOBRequest(appointmentId, customerId, obParams.getStaffId());
        }

        // auto accept时，异步capture，完成扣款
        if (canAutoAccept) {
            ThreadPool.execute(
                    () -> moeBookOnlineDepositService.capturePaymentIntent(bookOnline.getBusinessId(), appointmentId));
        }
        onlineBookingProducer.pushOnlineBookingSubmittedEvent(appointmentResultData.getId());

        deleteAbandonedRecord4Customer(
                obParams.getBusinessId(), customerId, obParams.getCustomerData().getPhoneNumber());

        return new OBSubmitResult()
                .setCustomerId(customerId)
                .setAppointmentId(appointmentId)
                .setAutoAcceptRequest(canAutoAccept);
    }

    /**
     * 提交 BookingRequest，和 {@link #bookOnlineSubmit(BookOnlineSubmitParams, MoeBusinessBookOnline)} 一样，但是不包含 prepay 相关的逻辑
     */
    public OBSubmitResult submitBookingRequest(BookOnlineSubmitParams obParams, MoeBusinessBookOnline bookOnline) {

        setDateAndTimeIfNecessary(obParams);

        // 必须在创建 customer 之前
        var depositPreviewParams = obDepositService.getDepositOrderPreviewParams(obParams);

        // insert or update customer info
        boolean isNewCustomer = Objects.isNull(obParams.getCustomerData().getCustomerId());
        Integer customerId;
        // 如果不存在 customer id， 当新用户处理
        if (isNewCustomer) {
            customerId = customerService.newCustomer(obParams);
        } else {
            obAddressService.createCustomerAddress(obParams.getCustomerData());

            customerId = obParams.getCustomerData().getCustomerId();
            boolean autoAcceptAll =
                    OBRequestSubmittedAutoTypeEnum.isAutoAcceptConflict(bookOnline.getRequestSubmittedAutoType());
            // OB-C update info
            customerService.updateCustomerInfo(obParams, autoAcceptAll);
            if (obParams.getFromPetParentPortal() != null && obParams.getFromPetParentPortal()) {
                obAddressService.setPrimaryAddress(obParams.getCustomerData().getAddressId());
            }
        }

        // 记录卡信息, 如果绑卡失败，要回退上面的修改
        if (!Boolean.TRUE.equals(obParams.getCustomerData().getHasStripeCard())) {
            prepayService.saveCard(
                    obParams.getCustomerData().getChargeToken(), obParams.getBusinessId(), customerId, isNewCustomer);
        }

        // 保存pet data
        petService.savePet(customerId, obParams);

        // assemble the appointment information and insert
        // accept 逻辑单独抽出来
        AppointmentParams appointmentParams =
                assembleAppointmentInfo(customerId, false, obParams, bookOnline, isNewCustomer);

        var depositParams = obDepositService.getDepositOrderParams(depositPreviewParams, obParams, appointmentParams);

        ActivityLogRecorder.recordRoot(obParams.getBusinessId(), SUBMIT_OB, APPOINTMENT, null, obParams);

        AddResultDTO appointmentResultData =
                moeGroomingAppointmentService.addGroomingAppointment(appointmentParams, depositParams);
        if (Boolean.FALSE.equals(appointmentResultData.getResult())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Failed to create appointment");
        }

        Long companyId = bookOnline.getCompanyId();
        Integer businessId = bookOnline.getBusinessId();
        Integer appointmentId = appointmentResultData.getId();
        Integer invoiceId = appointmentResultData.getInvoiceId();

        if (needPayment(obParams)) {
            // 如果需要 payment，把 BookingRequest 的状态设置为 WAITING，等待发起支付
            // v3 版本 submit，支付在 submit 之后，所以状态设置为 WAITING
            updateBookingRequestPaymentStatus(businessId, appointmentId, BookingRequestModel.PaymentStatus.WAITING);
        }

        if (invoiceId != null && invoiceId > 0) {
            applyPromotions(companyId, businessId, invoiceId, obParams);
        }

        if (obParams.getPreAuthDetail() != null) {
            prepayService.insertPreAuthRecord(obParams, appointmentId);

            // Yunxiang: 在 PreAuth 的场景，因为整体的支付流程会非常长，并且存在最后 Capture 的时候调整实际收取
            // 的金额的场景。复用其他即时支付的增加 Tips 的逻辑（即支付成功后再增加到 Order 上）会有 Tips 丢失的风险。
            // 此外，在 PreAuth Capture 之前，Business 都无法在订单上看到 Client 添加的 Tips 导致误解。
            // 因此，当 PreAuth 有 Tips 时，应当在创建订单的时候就添加到订单上。

            // 新order flow会在一开始创建deposit order时包含 tips，不要在这里set
            if (!newOrderHelper.enableNewOrder(companyId)) {
                BigDecimal tipsAmount = obParams.getPreAuthDetail().getTipsAmount();
                if (tipsAmount != null && tipsAmount.compareTo(BigDecimal.ZERO) > 0) {
                    orderService.setTips(
                            businessId,
                            null,
                            InvoiceAmountVo.builder()
                                    .invoiceId(invoiceId)
                                    .valueType(InvoiceValueType.AMOUNT.value())
                                    .value(obParams.getPreAuthDetail().getTipsAmount())
                                    .build());
                }
            }
        }

        // 记录customer 签名信息： save result to moe_customer_agreement (depend on: moe_business_agreement)
        moeGroomingBookOnlineService.saveAgreementForObSubmit(obParams.getAgreements(), appointmentId);

        if (StringUtils.hasText(obParams.getCustomerData().getStripeCustomerId())) {
            iPaymentStripeClient.saveStripeCustomer(
                    businessId, customerId, obParams.getCustomerData().getStripeCustomerId());
        }

        if (canAutoAssign(bookOnline, obParams)) {

            autoAssign(obParams, appointmentId, customerId);

            // 将 auto assign 的数据同步到 BookingRequest
            syncBookingRequestFromAppointment(appointmentId);
        }

        // 如果需要 payment，会在 payment 成功后再处理 auto accept，发送通知等逻辑
        // 在不需要 payment 的情况下，处理 auto accept，发送通知等逻辑，整个流程结束
        boolean isAutoAccepted = !needPayment(obParams) && triggerBookingRequestAutoAccepted(appointmentId);

        onlineBookingProducer.pushOnlineBookingSubmittedEvent(appointmentResultData.getId());

        deleteAbandonedRecord4Customer(
                obParams.getBusinessId(), customerId, obParams.getCustomerData().getPhoneNumber());
        // DONE new order flow 未开启 prepay 时没有订单，所以 invoiceId 为 0
        if (invoiceId != null && invoiceId != 0) {
            // 记录 OrderDecouplingFlowMarker，用于标记 order 是由 order decoupling 新流程创建的，用于迁移过渡
            insertOrderDecouplingFlowMarker(appointmentResultData.getInvoiceId().longValue());
        }

        return new OBSubmitResult()
                .setCustomerId(customerId)
                .setAppointmentId(appointmentId)
                .setAutoAcceptRequest(isAutoAccepted)
                .setOrderId(appointmentResultData.getInvoiceId().longValue());
    }

    // Apply separate discounts and discount from memberships.
    private void applyPromotions(long companyId, long businessId, long invoiceId, BookOnlineSubmitParams obParams) {
        var order = Objects.requireNonNull(orderService.getOrderDetailByOrderId((int) businessId, (int) invoiceId));

        // New order flow 建的是 deposit order 单，还不能应用 promotions
        if (OrderModel.OrderType.DEPOSIT.equals(order.getOrder().getOrderType())) {
            return;
        }

        // use discount code
        if (Objects.nonNull(obParams.getDiscountCodeParams())) {
            orderService.setDiscountWithDiscountCodeId(
                    obParams.getBusinessId(),
                    (int) invoiceId,
                    obParams.getDiscountCodeParams().getDiscountCodeId());
        }

        // membership info for obc submit grooming
        ThreadPool.execute(() -> {
            if (Objects.isNull(obParams.getMembershipParams())) {
                return;
            }
            membershipService.applyMemberships(
                    businessId,
                    companyId,
                    invoiceId,
                    0L,
                    obParams.getMembershipParams().getMembershipIds());
        });
    }

    private void updateBookingRequestPaymentStatus(
            Integer businessId, Integer appointmentId, BookingRequestModel.PaymentStatus paymentStatus) {
        bookingRequestStub.updateBookingRequest(UpdateBookingRequestRequest.newBuilder()
                .setId(mustGetBookingRequest(businessId, appointmentId).getId())
                .setPaymentStatus(paymentStatus)
                .build());
    }

    private void insertOrderDecouplingFlowMarker(long orderId) {
        var marker = new OrderDecouplingFlowMarker();
        marker.setOrderId(orderId);
        orderDecouplingFlowMarkerMapper.insertSelective(marker);
    }

    /**
     * Check if this appointmentId can be auto accepted.
     *
     * @param appointmentId appointment id
     * @return whether the booking request can be auto accepted
     */
    private boolean canAutoAccept(int appointmentId) {

        var appointment = mustGetAppointment(appointmentId);

        if (!StringUtils.hasText(appointment.getAppointmentDate())
                || Boolean.TRUE.equals(appointment.getNoStartTime())) {
            return false;
        }

        var bookOnline = moeGroomingBookOnlineService.getSettingInfoByBusinessId(appointment.getBusinessId());

        var profileChanged = customerService
                .getCustomerHasRequestUpdate(appointment.getBusinessId(), appointment.getCustomerId())
                .hasRequestUpdate();

        return OBRequestSubmittedAutoTypeEnum.isAutoAcceptRequest(
                bookOnline.getRequestSubmittedAutoType(), profileChanged);
    }

    /**
     * 触发 BookingRequest 的 auto accept 逻辑，会处理：
     * <p> 1. Auto accept
     * <p> 2. Capture PaymentIntent
     * <p> 3. 发送通知
     *
     * @param appointmentId appointment id
     */
    public boolean triggerBookingRequestAutoAccepted(int appointmentId) {

        var canAutoAccept = canAutoAccept(appointmentId);

        if (canAutoAccept) {
            acceptBookingRequest(appointmentId);
        }

        ThreadPool.execute(() -> sendNotificationForBooingRequest(appointmentId));

        return canAutoAccept;
    }

    private void acceptBookingRequest(int appointmentId) {
        var updateBean = new MoeGroomingAppointment();
        updateBean.setId(appointmentId);
        updateBean.setIsAutoAccept(true);
        updateBean.setBookOnlineStatus(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB);
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(updateBean);

        // 添加 note
        // see com.moego.server.grooming.service.MoeGroomingAppointmentService.buildGroomNote
        var appointment = mustGetAppointment(appointmentId);

        var note = appointmentQueryService.getCustomerAlertNote(
                appointment.getCompanyId(), null, appointment.getCustomerId());
        if (StringUtils.hasText(note)) {
            var groomingNote = new MoeGroomingNote();
            groomingNote.setBusinessId(appointment.getBusinessId());
            groomingNote.setCompanyId(appointment.getCompanyId());
            groomingNote.setCustomerId(appointment.getCustomerId());
            groomingNote.setGroomingId(appointmentId);
            groomingNote.setCreateBy(appointment.getCreatedById());
            groomingNote.setUpdateBy(appointment.getCreatedById());
            groomingNote.setCreateTime(Instant.now().getEpochSecond());
            groomingNote.setUpdateTime(Instant.now().getEpochSecond());
            groomingNote.setType(GroomingAppointmentEnum.NOTE_ALERT);
            groomingNote.setNote(note);
            moeGroomingNoteService.atomicityInsertOrUpdate(groomingNote);
        }

        // accept BookingRequest，更新 BookingRequest 的状态为 SCHEDULED
        bookingRequestStub.updateBookingRequestStatus(UpdateBookingRequestStatusRequest.newBuilder()
                .setId(mustGetBookingRequest(appointment.getBusinessId(), appointmentId)
                        .getId())
                .setStatus(BookingRequestStatus.SCHEDULED)
                .build());

        // 最后 capture PaymentIntent
        // 注意：这里 capturePaymentIntent 的实现需要是幂等的
        // 因为调用方可能来自两个场景：有 payment，无 payment
        ThreadPool.execute(() -> {
            moeBookOnlineDepositService.capturePaymentIntent(appointment.getBusinessId(), appointmentId);
            groomingApptAsyncService.apptAddNotifyV2(appointmentId);

            // sync google calendar
            calendarSyncService.checkBusinessHaveGoogleCalendarSync(
                    appointment.getBusinessId(), appointment.getId(), appointment.getAppointmentDate());
        });
    }

    private BookingRequestModel mustGetBookingRequest(int businessId, int appointmentId) {
        return bookingRequestStub
                .listBookingRequests(ListBookingRequestsRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addAppointmentIds(appointmentId)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageSize(1)
                                .setPageNum(1)
                                .build())
                        .build())
                .getBookingRequestsList()
                .stream()
                .findFirst()
                .orElseThrow(() -> bizException(
                        Code.CODE_PARAMS_ERROR, "BookingRequest not found for appointment: " + appointmentId));
    }

    private void syncBookingRequestFromAppointment(Integer appointmentId) {
        bookingRequestStub.syncBookingRequestFromAppointment(SyncBookingRequestFromAppointmentRequest.newBuilder()
                .addAppointmentId(appointmentId)
                .build());
    }

    private void sendNotificationForBooingRequest(int appointmentId) {

        var appointment = mustGetAppointment(appointmentId);

        if (Objects.equals(appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)) {
            // 还在 BookingRequest 状态，发送 submit notification
            sendSubmitNotification(
                    appointment.getCompanyId(),
                    appointment.getBusinessId(),
                    appointmentId,
                    getAutoAssign(appointmentId));
        } else {
            // 被 auto accept 了，发送 accept notification
            sendAcceptNotification(appointment.getCompanyId(), appointment.getBusinessId(), appointmentId);
        }

        // 给 B 发送通知
        var staffId = getPetDetails(appointmentId).stream()
                .filter(e -> Objects.equals(e.getServiceType(), ServiceType.SERVICE_VALUE))
                .map(MoeGroomingPetDetail::getStaffId)
                .filter(CommonUtil::isNormal)
                .findFirst()
                .orElse(null);
        asyncNotificationOBRequest(appointmentId, appointment.getCustomerId(), staffId);
    }

    private MoeGroomingAppointment mustGetAppointment(int appointmentId) {
        var appointment = appointmentQueryService.getAppointmentById(appointmentId);
        if (appointment == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Appointment not found: " + appointmentId);
        }
        return appointment;
    }

    @Nullable
    private AssignBO getAutoAssign(int appointmentId) {
        var e = new AutoAssignExample();
        e.createCriteria().andAppointmentIdEqualTo(appointmentId);
        var autoAssign = firstElement(autoAssignMapper.selectByExample(e));
        if (autoAssign == null) {
            return null;
        }

        var result = new AssignBO();
        result.setStaffId(autoAssign.getStaffId());
        result.setAppointmentTime(autoAssign.getAppointmentTime());
        return result;
    }

    private static boolean needPayment(BookOnlineSubmitParams obParams) {
        return StringUtils.hasText(obParams.getPrepayGuid());
    }

    private void autoAssignAndSendNotification(
            BookOnlineSubmitParams obParams, Integer appointmentId, Integer customerId, boolean canAutoAccept) {
        Long companyId = obParams.getCompanyId();
        Integer businessId = obParams.getBusinessId();
        AssignBO autoAssign = autoAssign(obParams, appointmentId, customerId);
        if (canAutoAccept) {
            // 可能会出现 auto assign 失败的情况，如果失败，就不能 auto accept
            MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
            if (Boolean.TRUE.equals(appointment.getNoStartTime()) || isNoStaff(appointmentId)) {
                // 这时候还没有 staff 或者 time，意味着 auto assign 失败
                // 更新 isAutoAccept 为 false，将 bookOnlineStatus 设置为 1，让它出现在 Booking requests 里
                resetOBRequest(appointmentId);
                sendSubmitNotification(companyId, businessId, appointmentId, autoAssign);
            } else {
                sendAcceptNotification(companyId, businessId, appointmentId);
            }
        } else {
            sendSubmitNotification(companyId, businessId, appointmentId, autoAssign);
        }

        Integer assignedStaffId =
                Optional.of(autoAssign).map(AssignBO::getStaffId).orElseGet(obParams::getStaffId);
        asyncNotificationOBRequest(appointmentId, customerId, assignedStaffId);
    }

    private void resetOBRequest(Integer groomingId) {
        MoeGroomingAppointment updateBean = new MoeGroomingAppointment();
        updateBean.setId(groomingId);
        updateBean.setIsAutoAccept(false);
        updateBean.setBookOnlineStatus(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB);
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(updateBean);
    }

    private boolean isNoStaff(Integer groomingId) {
        return getPetDetails(groomingId).stream()
                .filter(e -> Objects.equals(ServiceType.SERVICE_VALUE, e.getServiceType()))
                .map(MoeGroomingPetDetail::getStaffId)
                .anyMatch(id -> id == null || id == 0);
    }

    private AssignBO autoAssign(BookOnlineSubmitParams obParams, Integer appointmentId, Integer customerId) {

        AssignBO autoAssign;
        try {
            autoAssign = doAssign(obParams, customerId, appointmentId);
        } catch (Exception e) {
            // auto assign 失败不应该影响到后续的流程，比如发送 notification
            log.warn("Auto assign failed", e);
            autoAssign = new AssignBO();
        }

        Optional.ofNullable(autoAssign.getAppointmentTime())
                .ifPresent(time -> updateTimeForAppointment(appointmentId, time));

        Optional.ofNullable(autoAssign.getStaffId())
                .ifPresent(staffId -> updateStaffForAppointment(
                        obParams.getCompanyId(), obParams.getBusinessId(), appointmentId, staffId));

        insertAutoAssign(autoAssign, appointmentId);

        return autoAssign;
    }

    private void updateStaffForAppointment(long companyId, Integer businessId, Integer appointmentId, Integer staffId) {
        var petDetailList = getPetDetails(appointmentId);
        if (ObjectUtils.isEmpty(petDetailList)) {
            return;
        }

        var customizedServiceList = listCustomizedService(companyId, businessId, staffId, petDetailList);

        // 1. 更新 pet details
        var start = petDetailList.get(0).getStartTime().intValue();
        var end = start;
        for (var petDetail : petDetailList) {
            var customizedService = findCustomizedService(customizedServiceList, petDetail, staffId);
            if (customizedService == null) {
                continue;
            }

            var builder = UpdatePetDetailRequest.newBuilder();
            builder.setId(petDetail.getId());
            builder.setStaffId(staffId);
            builder.setServicePrice(customizedService.getPrice());
            builder.setPriceOverrideType(customizedService.getPriceOverrideType());
            builder.setStartTime(Math.toIntExact(end));
            builder.setServiceTime(customizedService.getDuration());
            builder.setDurationOverrideType(customizedService.getDurationOverrideType());
            builder.setEndTime(Math.toIntExact(end + customizedService.getDuration()));
            petDetailStub.updatePetDetail(builder.build());

            end = builder.getEndTime();
        }

        // 2. 更新 appointment time
        var builder = UpdateAppointmentSelectiveRequest.newBuilder();
        builder.setId(appointmentId);
        builder.setAppointmentStartTime(start);
        builder.setAppointmentEndTime(end);
        appointmentStub.updateAppointmentSelective(builder.build());
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            long companyId, Integer businessId, Integer staffId, List<MoeGroomingPetDetail> petDetailList) {
        var builder = BatchGetCustomizedServiceRequest.newBuilder();
        builder.setCompanyId(companyId);
        for (var petDetail : petDetailList) {
            var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                    .setServiceId(petDetail.getServiceId())
                    .setBusinessId(businessId)
                    .setStaffId(staffId)
                    .setPetId(petDetail.getPetId());
            builder.addQueryConditionList(condBuilder.build());
        }
        return serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();
    }

    @Nullable
    private CustomizedServiceView findCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            MoeGroomingPetDetail petDetail,
            Integer staffId) {
        return customizedServiceList.stream()
                .filter(condAndService -> {
                    var cond = condAndService.getQueryCondition();
                    return Objects.equals(
                                    cond.getServiceId(),
                                    petDetail.getServiceId().longValue())
                            && Objects.equals(
                                    cond.getPetId(), petDetail.getPetId().longValue())
                            && Objects.equals(cond.getStaffId(), staffId.longValue());
                })
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    private void updateTimeForAppointment(Integer groomingId, Integer time) {
        List<MoeGroomingPetDetail> petDetails = getPetDetails(groomingId);
        if (ObjectUtils.isEmpty(petDetails)) {
            return;
        }

        // 将第一个 pet detail start time 设置为 auto assign 的时间
        // 根据 service duration 依次往后推
        Long startTime = Long.valueOf(time);
        for (MoeGroomingPetDetail petDetail : petDetails) {
            MoeGroomingPetDetail pd = new MoeGroomingPetDetail();
            pd.setId(petDetail.getId());
            pd.setStartTime(startTime);
            pd.setEndTime(startTime + petDetail.getServiceTime());
            pd.setUpdateTime(CommonUtil.get10Timestamp());
            petDetailMapper.updateByPrimaryKeySelective(pd);
            startTime = pd.getEndTime();
        }

        Integer end = Math.toIntExact(startTime);

        MoeGroomingAppointment updateBean = new MoeGroomingAppointment();
        updateBean.setId(groomingId);
        updateBean.setNoStartTime(false);
        updateBean.setAppointmentStartTime(time);
        updateBean.setAppointmentEndTime(end);
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(updateBean);
    }

    private List<MoeGroomingPetDetail> getPetDetails(Integer groomingId) {
        MoeGroomingPetDetailExample example = new MoeGroomingPetDetailExample();
        example.createCriteria()
                .andGroomingIdEqualTo(groomingId)
                .andStatusEqualTo(PetDetailStatusEnum.NORMAL.getValue().byteValue())
                .andServiceItemTypeEqualTo(ServiceItemType.GROOMING_VALUE);
        return petDetailMapper.selectByExample(example).stream()
                .sorted(comparingInt(MoeGroomingPetDetail::getId))
                .toList();
    }

    public boolean canAutoAssign(MoeBusinessBookOnline bookOnline, BookOnlineSubmitParams obParams) {
        return StringUtils.hasText(obParams.getAppointmentDate()) // 需要 date 才能 auto assign
                && (obParams.getStaffId() == null || obParams.isNoStartTime())
                // && obParams.isNoStartTime()
                && Objects.equals(bookOnline.getUseVersion(), BookOnlineDTO.UseVersion.OB_3_0);
    }

    private void insertAutoAssign(AssignBO assignBO, Integer appointmentId) {
        AutoAssign aa = new AutoAssign();
        aa.setAppointmentId(appointmentId);
        aa.setAppointmentTime(assignBO.getAppointmentTime());
        aa.setStaffId(assignBO.getStaffId());
        autoAssignMapper.insertSelective(aa);
    }

    public AssignBO doAssign(BookOnlineSubmitParams obParams, Integer customerId, @Nullable Integer appointmentId) {
        return doAssignV2(obParams, customerId, appointmentId);

        /*     if (ObjectUtils.isEmpty(obParams.getAppointmentDate())) {
            // no date, cannot auto assign
            return new AssignBO();
        }

        boolean hasStaff = obParams.getStaffId() != null;
        boolean hasTime = !obParams.isNoStartTime();
        if (hasStaff && hasTime) {
            return new AssignBO();
        }

        OBTimeSlotDTO obTimeSlot = buildOBTimeSlot(obParams, customerId);
        obTimeSlot.setFilterAppointmentId(appointmentId);
        Map<String, OBAvailableTimeDto> dateToAvailableTime = timeSlotService.getTimeSlotList(obTimeSlot);
        OBAvailableTimeDto availableTimeDto = dateToAvailableTime.get(obParams.getAppointmentDate());
        if (availableTimeDto == null) {
            log.warn("No available time slot for {}", obParams.getAppointmentDate());
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }

        List<OBAvailableTimeStaffDto> staffList = availableTimeDto.getStaffList();
        if (ObjectUtils.isEmpty(staffList)) {
            log.warn("No available staff for {}", obParams.getAppointmentDate());
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }

        if (hasStaff) {
            return assignBasedOnStaff(obParams, staffList);
        }

        if (hasTime) {
            return assignBasedOnTime(obParams, staffList, obTimeSlot);
        }

        return assignBasedOnAvailability(obParams, customerId, staffList); */
    }

    public AssignBO doAssignV2(BookOnlineSubmitParams obParams, Integer customerId, @Nullable Integer appointmentId) {
        if (ObjectUtils.isEmpty(obParams.getAppointmentDate())) {
            // no date, cannot auto assign
            return new AssignBO();
        }

        var staffIdList = obParams.getPetData().stream()
                .map(BookOnlinePetParams::getStaffId)
                .filter(Objects::nonNull)
                .collect(toSet());
        boolean hasStaff = obParams.getStaffId() != null || !staffIdList.isEmpty();
        boolean hasTime = !obParams.isNoStartTime();
        if (hasStaff && hasTime) {
            return new AssignBO();
        }

        OBTimeSlotDTO obTimeSlot = buildOBTimeSlot(obParams, customerId);
        obTimeSlot.setFilterAppointmentId(appointmentId);

        var availableDateTimes = timeSlotService.getTimeSlotListV2(obTimeSlot).getAvailableDateTimes();
        if (CollectionUtils.isEmpty(availableDateTimes)) {
            log.warn("No available time slot for {}", obParams.getAppointmentDate());
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }
        OBAvailableTimeDto availableTimeDto = availableDateTimes.get(obParams.getAppointmentDate());
        if (availableTimeDto == null) {
            log.warn("No available time slot for {}", obParams.getAppointmentDate());
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }

        List<OBAvailableTimeStaffDto> staffList = availableTimeDto.getStaffList();
        if (ObjectUtils.isEmpty(staffList)) {
            log.warn("No available staff for {}", obParams.getAppointmentDate());
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }

        if (hasStaff) {
            if (!staffList.isEmpty()) {
                obParams.setStaffId(staffList.get(0).getId());
            }
            return assignBasedOnStaff(obParams, staffList);
        }

        if (hasTime) {
            return assignBasedOnTime(obParams, staffList, obTimeSlot);
        }

        return assignBasedOnAvailability(obParams, customerId, staffList);
    }

    private AssignBO assignBasedOnAvailability(
            BookOnlineSubmitParams obParams, Integer customerId, List<OBAvailableTimeStaffDto> staffList) {
        // no staff, no time, 找可用时间最早的 staff
        SortedMap<Integer, Set<Integer>> timeToStaffs = getTimeStaffsMap(staffList);

        if (timeToStaffs.isEmpty()) {
            log.warn("No available time for {}", obParams.getAppointmentDate());
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }

        Set<Integer> totalStaffIds =
                timeToStaffs.values().stream().flatMap(Collection::stream).collect(toSet());
        if (totalStaffIds.isEmpty()) {
            log.warn("No available staff for {}", obParams.getAppointmentDate());
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }

        if (totalStaffIds.size() == 1) {
            Integer staffId = totalStaffIds.iterator().next();
            return new AssignBO()
                    .setStaffId(staffId)
                    .setAppointmentTime(getFirstAvailableTimeForStaff(staffList, staffId));
        }

        if (customerId != null) {
            Integer preferredGroomerId = getPreferredGroomerId(customerId);
            if (preferredGroomerId != null && totalStaffIds.contains(preferredGroomerId)) {
                return new AssignBO()
                        .setStaffId(preferredGroomerId)
                        .setAppointmentTime(getFirstAvailableTimeForStaff(staffList, preferredGroomerId));
            }
        }

        // 先选时间最早，再选 staff
        Map.Entry<Integer, Set<Integer>> entry =
                timeToStaffs.entrySet().iterator().next();

        Integer time = entry.getKey();
        Set<Integer> availableStaffIds = entry.getValue();

        Integer staffId =
                getTopRankedStaff(obParams.getBusinessId(), obParams.getAppointmentDate(), time, availableStaffIds);

        return new AssignBO().setAppointmentTime(time).setStaffId(staffId);
    }

    private AssignBO assignBasedOnTime(
            BookOnlineSubmitParams obParams, List<OBAvailableTimeStaffDto> staffList, OBTimeSlotDTO obTimeSlot) {
        // has time, no staff
        Integer time = obParams.getAppointmentStartTime();
        Set<Integer> availableStaffIds = getAvailableStaffIds(staffList, obParams, obTimeSlot);

        if (ObjectUtils.isEmpty(availableStaffIds)) {
            log.warn("No available staff at {} {}", obParams.getAppointmentDate(), time);
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }
        if (availableStaffIds.size() == 1) {
            return new AssignBO().setStaffId(availableStaffIds.iterator().next());
        }

        if (obTimeSlot.getCustomerId() != null) {
            Integer preferredGroomerId = getPreferredGroomerId(obTimeSlot.getCustomerId());
            if (preferredGroomerId != null && availableStaffIds.contains(preferredGroomerId)) {
                return new AssignBO().setStaffId(preferredGroomerId);
            }
        }

        Integer staffId =
                getTopRankedStaff(obParams.getBusinessId(), obParams.getAppointmentDate(), time, availableStaffIds);

        return new AssignBO().setStaffId(staffId);
    }

    private Set<Integer> getAvailableStaffIds(
            List<OBAvailableTimeStaffDto> staffList, BookOnlineSubmitParams params, OBTimeSlotDTO obTimeSlot) {
        MoeBusinessBookOnline bookOnline =
                moeGroomingBookOnlineService.getSettingInfoByBusinessId(params.getBusinessId());

        if (Objects.equals(bookOnline.getAvailableTimeType(), BookOnlineDTO.AvailableTimeType.BY_SLOT)) {
            SortedMap<Integer, Set<Integer>> timeToStaffs = getTimeStaffsMap(staffList);
            return timeToStaffs.get(params.getAppointmentStartTime());
        }

        Map<Long, Integer> customizeDurationMap = getServiceDuration(params, obTimeSlot, staffList);

        return staffList.stream()
                .filter(it -> isInAvailableSlot(
                        it,
                        params.getAppointmentStartTime(),
                        customizeDurationMap.get(it.getId().longValue())))
                .map(OBAvailableTimeStaffDto::getId)
                .collect(toSet());
    }

    private Map<Long, Integer> getServiceDuration(
            BookOnlineSubmitParams params, OBTimeSlotDTO obTimeSlot, List<OBAvailableTimeStaffDto> staffList) {
        Set<Integer> staffIdList =
                staffList.stream().map(OBAvailableTimeStaffDto::getId).collect(toSet());
        return timeSlotService.getServiceDurationWithStaffList(params.getCompanyId(), obTimeSlot, staffIdList);
    }

    private static boolean isInAvailableSlot(OBAvailableTimeStaffDto it, Integer time, Integer duration) {
        List<ScheduleTimeSlot> slots =
                Optional.ofNullable(it.getAvailableRange()).orElseGet(List::of);
        for (ScheduleTimeSlot slot : slots) {
            if (slot.getStartTime() <= time && time + duration <= slot.getEndTime()) {
                return true;
            }
        }
        return false;
    }

    private Integer getTopRankedStaff(Integer businessId, String date, Integer time, Set<Integer> availableStaffIds) {
        if (isBySlot(businessId)) {
            availableStaffIds = getStaffIdsWithFewestAppointments(businessId, date, time, availableStaffIds);
        }

        if (availableStaffIds.size() == 1) {
            return availableStaffIds.iterator().next();
        }

        return iBusinessStaffClient
                .getStaffList(new StaffIdListParams(businessId, List.copyOf(availableStaffIds)))
                .stream()
                .max(comparing(MoeStaffDto::getSort, nullsFirst(naturalOrder())))
                .map(MoeStaffDto::getId)
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "No staff found"));
    }

    private Set<Integer> getStaffIdsWithFewestAppointments(
            Integer businessId, String date, Integer time, Set<Integer> availableStaffIds) {
        Map<Integer, Integer> staffIdToApptCount =
                new HashMap<>(getStaffApptCountMap(businessId, date, time, availableStaffIds));
        availableStaffIds.forEach(availableStaffId -> staffIdToApptCount.putIfAbsent(availableStaffId, 0));

        Integer min = staffIdToApptCount.values().stream()
                .min(naturalOrder())
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "No min value found"));

        Set<Integer> staffIds = staffIdToApptCount.entrySet().stream()
                .filter(it -> Objects.equals(it.getValue(), min))
                .map(Map.Entry::getKey)
                .collect(toSet());

        if (ObjectUtils.isEmpty(staffIds)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "No staff found with min value " + min);
        }
        return staffIds;
    }

    private Map<Integer, Integer> getStaffApptCountMap(
            Integer businessId, String date, Integer time, Set<Integer> availableStaffIds) {
        if (ObjectUtils.isEmpty(availableStaffIds)) {
            return Map.of();
        }

        MoeGroomingAppointmentExample example = new MoeGroomingAppointmentExample();
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andAppointmentDateEqualTo(date)
                .andAppointmentStartTimeEqualTo(time)
                .andStatusIn(AppointmentStatusSet.ACTIVE_STATUS_SET.stream()
                        .map(AppointmentStatusEnum::getValue)
                        .toList())
                .andIsWaitingListEqualTo(GroomingAppointmentEnum.NOT_WAITING_LIST)
                .andServiceTypeIncludeEqualTo(ServiceItemType.GROOMING_VALUE);

        List<MoeGroomingAppointment> appointments = moeGroomingAppointmentMapper.selectByExample(example);

        if (ObjectUtils.isEmpty(appointments)) {
            return Map.of();
        }

        MoeGroomingPetDetailExample e = new MoeGroomingPetDetailExample();
        e.createCriteria()
                .andGroomingIdIn(
                        appointments.stream().map(MoeGroomingAppointment::getId).toList())
                .andStaffIdIn(List.copyOf(availableStaffIds))
                .andStatusEqualTo(PetDetailStatusEnum.NORMAL.getValue().byteValue())
                .andServiceItemTypeEqualTo(ServiceItemType.GROOMING_VALUE);

        return petDetailMapper.selectByExample(e).stream()
                .collect(groupingBy(
                        MoeGroomingPetDetail::getStaffId,
                        mapping(MoeGroomingPetDetail::getGroomingId, collectingAndThen(toSet(), Set::size))));
    }

    private boolean isBySlot(Integer businessId) {
        MoeBusinessBookOnline bookOnline = moeGroomingBookOnlineService.getSettingInfoByBusinessId(businessId);
        return Objects.equals(bookOnline.getAvailableTimeType(), BookOnlineDTO.AvailableTimeType.BY_SLOT);
    }

    private static AssignBO assignBasedOnStaff(
            BookOnlineSubmitParams obParams, List<OBAvailableTimeStaffDto> staffList) {
        // has staff, no time
        Optional<OBAvailableTimeStaffDto> atsOpt = staffList.stream()
                .filter(s -> Objects.equals(s.getId(), obParams.getStaffId()))
                .findFirst();
        if (atsOpt.isEmpty()) {
            log.warn("staff {} is not available for {}", obParams.getStaffId(), obParams.getAppointmentDate());
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }
        List<Integer> slots = getSlots(atsOpt.get());
        if (slots.isEmpty()) {
            log.warn("staff {} has no available time for {}", obParams.getStaffId(), obParams.getAppointmentDate());
            throw bizException(
                    Code.CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE,
                    ResponseCodeEnum.APPOINTMENT_TIME_IS_NOT_AVAILABLE.getMessage());
        }
        return new AssignBO().setAppointmentTime(slots.get(0));
    }

    private static Integer getFirstAvailableTimeForStaff(List<OBAvailableTimeStaffDto> staffList, Integer staffId) {
        OBAvailableTimeStaffDto ats = staffList.stream()
                .filter(it -> Objects.equals(it.getId(), staffId))
                .findFirst()
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "No staff with id " + staffId));
        List<Integer> slots = getSlots(ats);
        if (slots.isEmpty()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "No available slot for staff " + staffId);
        }
        return slots.get(0);
    }

    private static SortedMap<Integer, Set<Integer>> getTimeStaffsMap(List<OBAvailableTimeStaffDto> staffList) {
        SortedMap<Integer, Set<Integer>> timeToStaffs = new TreeMap<>();
        staffList.forEach(it -> {
            Optional.ofNullable(it.getAvailableTime().getAm()).ifPresent(ams -> {
                for (Integer am : ams) {
                    timeToStaffs.computeIfAbsent(am, k -> new HashSet<>()).add(it.getId());
                }
            });
            Optional.ofNullable(it.getAvailableTime().getPm()).ifPresent(pms -> {
                for (Integer pm : pms) {
                    timeToStaffs.computeIfAbsent(pm, k -> new HashSet<>()).add(it.getId());
                }
            });
        });
        return timeToStaffs;
    }

    private static List<Integer> getSlots(OBAvailableTimeStaffDto ats) {
        OBAvailableTimeStaffAvailableTimeDto slots = ats.getAvailableTime();
        List<Integer> all = new ArrayList<>();
        List<Integer> ams = Optional.ofNullable(slots.getAm()).orElseGet(List::of);
        List<Integer> pms = Optional.ofNullable(slots.getPm()).orElseGet(List::of);
        all.addAll(ams);
        all.addAll(pms);
        return all;
    }

    @Nullable
    private Integer getPreferredGroomerId(Integer customerId) {
        MoeBusinessCustomerDTO customer = customerApi.getCustomerWithDeleted(customerId);
        if (customer == null) {
            return null;
        }
        Integer preferredGroomerId = customer.getPreferredGroomerId();
        if (preferredGroomerId == null || Objects.equals(preferredGroomerId, 0)) {
            return null;
        }
        return preferredGroomerId;
    }

    private OBTimeSlotDTO buildOBTimeSlot(BookOnlineSubmitParams obParams, Integer customerId) {
        OBTimeSlotDTO ts = new OBTimeSlotDTO();
        ts.setBusinessId(obParams.getBusinessId());
        ts.setCustomerId(customerId);
        ts.setDate(obParams.getAppointmentDate());
        ts.setCount(1);
        if (obParams.getStaffId() != null) {
            ts.setStaffIdList(List.of(obParams.getStaffId()));
        } else {
            MoeBusinessBookOnline businessBookOnline =
                    businessService.getSettingInfoByBusinessId(obParams.getBusinessId());
            ts.setStaffIdList(
                    obBusinessStaffService
                            .getOBAvailableStaffList(
                                    obParams.getBusinessId(), businessBookOnline.getAvailableTimeType())
                            .stream()
                            .map(MoeStaffDto::getId)
                            .toList());
        }
        if (obParams.getCustomerData().getAddressId() != null) {
            Integer addressId = obParams.getCustomerData().getAddressId();
            if (Boolean.TRUE.equals(obParams.getCustomerData().getIsProfileRequestAddress())) {
                Optional.ofNullable(profileRequestAddressApi.get(addressId)).ifPresent(e -> {
                    ts.setLat(e.getLat());
                    ts.setLng(e.getLng());
                });
            } else {
                Optional.ofNullable(obAddressService.getCustomerAddress(addressId))
                        .ifPresent(e -> {
                            ts.setLat(e.getLat());
                            ts.setLng(e.getLng());
                        });
            }
        } else {
            ts.setLat(obParams.getCustomerData().getLat());
            ts.setLng(obParams.getCustomerData().getLng());
        }
        // 先和前端保持一致这里给一个 pet id 否则 new client 计算 total duration 不正确
        AtomicInteger petId = new AtomicInteger(0);
        ts.setPetServices(obParams.getPetData().stream()
                .filter(BookOnlinePetParams::getIsSelected)
                .collect(toMap(
                        bookOnlinePetParams -> Objects.nonNull(bookOnlinePetParams.getPetId())
                                ? bookOnlinePetParams.getPetId()
                                : petId.incrementAndGet(),
                        bookOnlinePetParams -> {
                            List<Integer> services = new ArrayList<>();
                            services.add(bookOnlinePetParams.getServiceId());
                            services.addAll(getAddOnIds(bookOnlinePetParams));
                            return services;
                        })));
        ts.setServiceIds(obParams.getPetData().stream()
                .filter(BookOnlinePetParams::getIsSelected)
                .map(p -> {
                    List<Integer> services = new ArrayList<>();
                    services.add(p.getServiceId());
                    services.addAll(getAddOnIds(p));
                    return services;
                })
                .flatMap(List::stream)
                .toList());
        ts.setPetParamList(obParams.getPetData().stream()
                .filter(BookOnlinePetParams::getIsSelected)
                .map(p -> {
                    OBPetDataDTO petData = new OBPetDataDTO();
                    petData.setPetId(p.getPetId());
                    petData.setWeight(p.getWeight());
                    petData.setPetTypeId(p.getPetTypeId());
                    petData.setBreed(p.getBreed());
                    petData.setStaffId(p.getStaffId());
                    return petData;
                })
                .toList());
        return ts;
    }

    private static List<Integer> getAddOnIds(BookOnlinePetParams bookOnlinePetParams) {
        if (!ObjectUtils.isEmpty(bookOnlinePetParams.getAddons())) {
            return bookOnlinePetParams.getAddons().stream()
                    .map(BookOnlinePetParams.Addon::getId)
                    .toList();
        }
        return bookOnlinePetParams.getAddOnIds() != null ? bookOnlinePetParams.getAddOnIds() : List.of();
    }

    /**
     * 检查OB定金支付状态
     *
     * @param obDeposit      ob deposit record
     * @param obBusinessInfo business ob settings
     * @param customerId
     */
    public void checkOBDepositStatus(
            @Nullable MoeBookOnlineDeposit obDeposit, MoeBusinessBookOnline obBusinessInfo, Integer customerId) {
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(obBusinessInfo.getBusinessId()).build());
        // 1.Stripe Only; 2.enableNoShowFee=1; 3.protectedBy>0
        boolean prepayEnable = Objects.equals(
                        businessInfo.getPrimaryPayType(), PaymentMethodEnum.CARD_PROCESSOR_TYPE_STRIPE)
                && Objects.equals(obBusinessInfo.getEnableNoShowFee(), OnlineBookingConst.NO_SHOW_PROTECTION_PREPAY);
        BookOnlinePaymentGroupSettingDTO settingDTO =
                moeGroomingBookOnlineService.checkGroupPaymentTypeForClient(obBusinessInfo, customerId);
        if (settingDTO != null) {
            prepayEnable = Objects.equals(
                            businessInfo.getPrimaryPayType(), PaymentMethodEnum.CARD_PROCESSOR_TYPE_STRIPE)
                    && Objects.equals(settingDTO.getPaymentType(), OnlineBookingConst.NO_SHOW_PROTECTION_PREPAY);
        }
        // 没开 prepay 跳过检查，但需要风控 (这里是为了降低用户不刷新页面 prepay 后提交预约被风控不通过导致钱被冻结)，待上线稳定后可将此处风控移除，改为注解
        if (!prepayEnable) {
            String enable = stringRedisTemplate.opsForValue().get(RiskControlConstant.RISK_CONTROL_SWITCH_KEY);
            if (StringUtils.hasText(enable) && Boolean.parseBoolean(enable)) {
                RecaptchaUtils.verify(
                        RecaptchaAction.RECAPTCHA_ACTION_OB_V3_SUBMIT,
                        List.of(RecaptchaVersion.RECAPTCHA_VERSION_V2, RecaptchaVersion.RECAPTCHA_VERSION_V3),
                        recaptchaServiceBlockingStub);
            }
            return;
        }
        // 开关开启时，depositGuid 不能为空，必须有 deposit 记录
        if (Objects.isNull(obDeposit)) {
            throw bizException(Code.CODE_OB_DEPOSIT_NOT_PAID);
        }
        // 检查定金支付状态
        if (!Objects.equals(obDeposit.getStatus(), BookOnlineDepositConst.PROCESSING)) {
            throw bizException(Code.CODE_OB_DEPOSIT_NOT_PAID);
        }
    }

    /**
     * 判断是否简化提交流程
     *
     * @param obParams
     * @return
     */
    private static void setDateAndTimeIfNecessary(BookOnlineSubmitParams obParams) {
        if (Objects.isNull(obParams.getAppointmentStartTime())) {
            // pet detail start_time is set 0
            obParams.setAppointmentStartTime(0);
            obParams.setNoStartTime(true);
        }
        if (!StringUtils.hasText(obParams.getAppointmentDate())) {
            obParams.setAppointmentDate("");
        }
    }

    /**
     * 组装预约信息
     *
     * @param customerId        customer id
     * @param autoAcceptRequest auto accept request
     * @param obParams          ob submit params
     * @param obBusinessInfo    business ob settings
     * @param isNewCustomer     is new customer
     * @return appointment params
     */
    private AppointmentParams assembleAppointmentInfo(
            Integer customerId,
            boolean autoAcceptRequest,
            BookOnlineSubmitParams obParams,
            MoeBusinessBookOnline obBusinessInfo,
            boolean isNewCustomer) {
        AppointmentParams appointmentParams = new AppointmentParams();
        appointmentParams.setBusinessId(obParams.getBusinessId());
        appointmentParams.setCompanyId(obParams.getCompanyId());
        appointmentParams.setCustomerId(customerId);
        appointmentParams.setAppointmentDateString(obParams.getAppointmentDate());
        appointmentParams.setAppointmentStartTime(obParams.getAppointmentStartTime());
        appointmentParams.setNoStartTime(obParams.isNoStartTime());
        appointmentParams.setSource(GroomingAppointmentEnum.SOURCE_OB);
        // https://moego.atlassian.net/browse/ERP-4232
        appointmentParams.setAdditionalNote(obParams.getNote());
        appointmentParams.setColorCode(ServiceEnum.DEFAULT_COLOR);
        appointmentParams.setCreatedById(0);
        appointmentParams.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue());
        appointmentParams.setOutOfArea(obParams.getOutOfArea());
        appointmentParams.setBookOnlineStatus(ServiceEnum.OB_NOT_CONFIRM);
        // set ob status to 0 when auto accept
        appointmentParams.setIsAutoAccept(autoAcceptRequest);
        if (autoAcceptRequest) {
            appointmentParams.setBookOnlineStatus(ServiceEnum.OB_DEFAULT_NORMAL);
            appointmentParams.setAlertNotes(
                    appointmentQueryService.getCustomerAlertNote(obParams.getCompanyId(), null, customerId));
        }
        // 收集被选中的宠物详情
        List<PetDetailParams> petServices = collectSelectedPetServiceList(obParams, obBusinessInfo);
        appointmentParams.setPetServices(petServices);
        if (obParams.getPreAuthDetail() != null) {
            appointmentParams.setPreAuthEnable(true);
            if (!StringUtils.hasText(obParams.getPreAuthDetail().getPaymentMethodId())) {
                CustomerStripInfoSaveResponse response = prepayService.saveCard(
                        obParams.getPreAuthDetail().getChargeToken(),
                        obParams.getBusinessId(),
                        customerId,
                        isNewCustomer);
                if (response != null) {
                    appointmentParams.setPreAuthPaymentMethod(response.getPaymentMethodId());
                    appointmentParams.setPreAuthCardNumber(response.getCardType() + response.getCardNumber());
                }
            } else {
                appointmentParams.setPreAuthPaymentMethod(
                        obParams.getPreAuthDetail().getPaymentMethodId());
                appointmentParams.setPreAuthCardNumber(
                        obParams.getPreAuthDetail().getCardNumber());
            }
        }
        if (obParams.getFromPetParentPortal() != null && obParams.getFromPetParentPortal()) {
            appointmentParams.setSourcePlatform(AppointmentSourcePlatform.PET_PARENT_APP.name());
        }

        return appointmentParams;
    }

    /**
     * 收集被选中的宠物服务
     *
     * @param obParams
     * @param obBusinessInfo
     * @return
     */
    private List<PetDetailParams> collectSelectedPetServiceList(
            BookOnlineSubmitParams obParams, MoeBusinessBookOnline obBusinessInfo) {
        List<PetDetailParams> petServiceList = new ArrayList<>();

        final AtomicInteger startTime = new AtomicInteger(obParams.getAppointmentStartTime());

        var customizedServiceList = listCustomizedService(obBusinessInfo.getCompanyId(), obParams);

        boolean needResetStartTime = isNeedResetStartTime(obBusinessInfo);

        obParams.getPetData().stream()
                .filter(BookOnlinePetParams::getIsSelected)
                .forEach(bookOnlinePetParams -> {
                    // 根据 service id list 获取服务详情
                    List<Integer> selectedServices = new ArrayList<>();
                    selectedServices.add(bookOnlinePetParams.getServiceId());
                    selectedServices.addAll(bookOnlinePetParams.getAddOnIds());
                    List<MoeGroomingServiceDTO> details =
                            groomingServiceService.getServicesByServiceIds(obParams.getBusinessId(), selectedServices);
                    Map<Integer, MoeGroomingServiceDTO> serviceMap =
                            details.stream().collect(toMap(MoeGroomingServiceDTO::getId, s -> s));

                    if (Objects.nonNull(bookOnlinePetParams.getStartTime())) {
                        startTime.set(bookOnlinePetParams.getStartTime());
                    } else if (needResetStartTime) {
                        // by slot类型所有的pet在同一个开始时间，重置时间
                        startTime.set(obParams.getAppointmentStartTime());
                    }

                    // 按顺序设置service开始时间
                    for (Integer serviceId : selectedServices) {
                        if (!serviceMap.containsKey(serviceId)) {
                            continue;
                        }
                        MoeGroomingServiceDTO tmpService = serviceMap.get(serviceId);
                        PetDetailParams petDetailParam = new PetDetailParams();
                        petDetailParam.setPetId(bookOnlinePetParams.getPetId());
                        petDetailParam.setServiceId(tmpService.getId());
                        petDetailParam.setServiceType(Integer.valueOf(tmpService.getType()));
                        petDetailParam.setServiceTime(tmpService.getDuration());
                        petDetailParam.setServicePrice(tmpService.getPrice());
                        petDetailParam.setStaffId(
                                Objects.nonNull(bookOnlinePetParams.getStaffId())
                                        ? bookOnlinePetParams.getStaffId()
                                        : obParams.getStaffId());
                        petDetailParam.setScopeTypePrice(ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType());
                        petDetailParam.setScopeTypeTime(ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType());
                        petDetailParam.setStartTime(startTime.get());

                        applyServiceOverride(customizedServiceList, petDetailParam);

                        // by slot类型不同的pet使用同一个时间，同一个pet的不同service时间正常叠加
                        startTime.addAndGet(petDetailParam.getServiceTime());
                        petServiceList.add(petDetailParam);
                    }
                });

        if (petServiceList.isEmpty()) {
            throw new CommonException(ResponseCodeEnum.NO_AVAILABLE_SERVICE_SELECTED, "no service is selected");
        }

        return petServiceList;
    }

    private boolean isNeedResetStartTime(final MoeBusinessBookOnline obBusinessInfo) {
        Byte availableTimeType;
        var enableSync = BooleanEnum.VALUE_TRUE.equals(obBusinessInfo.getAvailableTimeSync());
        if (enableSync) {
            var availabilityType = staffService
                    .getBusinessStaffAvailabilityType(
                            com.moego.idl.service.organization.v1.GetBusinessStaffAvailabilityTypeRequest.newBuilder()
                                    .setCompanyId(obBusinessInfo.getCompanyId())
                                    .setBusinessId(obBusinessInfo.getBusinessId())
                                    .build())
                    .getAvailabilityType();
            availableTimeType = switch (availabilityType) {
                case BY_TIME -> OnlineBookingConst.AVAILABLE_TIME_TYPE_WORKING_HOUR;
                case BY_SLOT -> OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT;
                default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid availability type");};
        } else {
            availableTimeType = obBusinessInfo.getAvailableTimeType();
        }
        return availableTimeType == OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT;
    }

    private static void applyServiceOverride(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            PetDetailParams petDetailParam) {

        var customizedService = ServiceUtil.getCustomizedService(
                customizedServiceList,
                petDetailParam.getPetId(),
                petDetailParam.getServiceId(),
                petDetailParam.getStaffId());

        if (customizedService != null) {
            // 这里保留之前的逻辑，如果有 override，直接覆盖；如果没有，使用原有的值
            if (customizedService.getDurationOverrideType() != SERVICE_OVERRIDE_TYPE_UNSPECIFIED) {
                petDetailParam.setServiceTime(customizedService.getDuration());
                petDetailParam.setDurationOverrideType(customizedService.getDurationOverrideType());
            }
            if (customizedService.getPriceOverrideType() != SERVICE_OVERRIDE_TYPE_UNSPECIFIED) {
                petDetailParam.setServicePrice(BigDecimal.valueOf(customizedService.getPrice()));
                petDetailParam.setPriceOverrideType(customizedService.getPriceOverrideType());
            }
        }
    }

    public List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            long companyId, BookOnlineSubmitParams obParams) {

        var selectedPetList = obParams.getPetData().stream()
                .filter(BookOnlinePetParams::getIsSelected)
                .toList();

        var builder = BatchGetCustomizedServiceRequest.newBuilder();
        builder.setCompanyId(companyId);
        for (var pet : selectedPetList) {
            var condBuilder = CustomizedServiceQueryCondition.newBuilder();
            condBuilder.setServiceId(pet.getServiceId());
            condBuilder.setBusinessId(obParams.getBusinessId());
            if (isNormal(obParams.getStaffId())) { // 可能没选 staff（auto assign）
                condBuilder.setStaffId(obParams.getStaffId());
            }
            if (isNormal(pet.getPetId())) { // 可能是 new pet
                condBuilder.setPetId(pet.getPetId());
            }
            builder.addQueryConditionList(condBuilder.build());
        }

        return serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();
    }

    private void sendSubmitNotification(
            Long companyId, Integer businessId, Integer appointmentId, @Nullable AssignBO autoAssign) {
        OnlineBookWaitingNotifyParams param = new OnlineBookWaitingNotifyParams();
        param.setCompanyId(companyId);
        param.setGroomingId(appointmentId);
        param.setBusinessId(businessId);
        param.setType(OnlineBookWaitingNotifyParams.TYPE_SUBMIT);
        if (autoAssign != null) {
            param.setAutoAssign(new OnlineBookWaitingNotifyParams.AutoAssign()
                    .setStaffId(autoAssign.getStaffId())
                    .setAppointmentTime(autoAssign.getAppointmentTime()));
        }
        iNotificationClient.bookOnlineNotify(param);
    }

    private void sendAcceptNotification(Long companyId, Integer businessId, Integer appointmentId) {
        OnlineBookWaitingNotifyParams param = new OnlineBookWaitingNotifyParams();
        param.setGroomingId(appointmentId);
        param.setCompanyId(companyId);
        param.setBusinessId(businessId);
        param.setType(OnlineBookWaitingNotifyParams.TYPE_ACCEPT);
        iNotificationClient.bookOnlineNotify(param);
    }

    private void asyncNotificationOBRequest(Integer groomingId, Integer customerId, Integer staffId) {
        ThreadPool.execute(() -> {
            MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
            Set<Integer> staffIds = appointmentQueryService.getAppointmentRelatedStaffIds(groomingId);
            // 调用通知发送
            NotificationOBRequestReceivedParams obRequestReceivedParams = new NotificationOBRequestReceivedParams();
            obRequestReceivedParams.setBusinessId(appointment.getBusinessId());
            obRequestReceivedParams.setStaffIdList(staffIds);
            // 给前端的数据体
            NotificationExtraOBReqestDto obReqestDto = new NotificationExtraOBReqestDto();
            obReqestDto.setGroomingId(groomingId);
            obReqestDto.setAppointmentDate(appointment.getAppointmentDate());
            obReqestDto.setAppointmentStartTime(appointment.getAppointmentStartTime());
            obReqestDto.setNoStartTime(appointment.getNoStartTime());
            obReqestDto.setAppointmentEndTime(appointment.getAppointmentEndTime());
            obReqestDto.setCustomerId(customerId);
            obReqestDto.setStaffId(staffId);
            // customer firstName 和 lastName 在notification模块组装
            obRequestReceivedParams.setWebPushDto(obReqestDto);
            iNotificationClient.sendNotificationOBRequestReceived(obRequestReceivedParams);
        });
    }

    private void deleteAbandonedRecord4Customer(Integer businessId, Integer customerId, String phoneNumber) {
        ThreadPool.execute(() -> abandonRecordMapper.removeAbandonRecords(
                businessId, AbandonDeleteTypeEnum.SUBMITTED.getType(), customerId, phoneNumber));
    }

    public Boolean autoMoveWaitlist() {
        List<BookOnlineAutoMoveAppointmentDTO> res =
                moeGroomingAppointmentService.queryAllBookingAppointmentForAutoMove();
        if (CollectionUtils.isEmpty(res)) {
            return Boolean.TRUE;
        }
        List<Integer> allAppointmentIds = new ArrayList<>();
        AtomicInteger scrollId = new AtomicInteger(queryTaskScrollId());
        res.forEach(dto -> {
            if (CollectionUtils.isEmpty(dto.getGroomingIds())) {
                return;
            }
            dto.getGroomingIds().forEach(groomingId -> {
                allAppointmentIds.add(groomingId);
                if (groomingId > scrollId.get()) {
                    scrollId.set(groomingId);
                }
            });
        });
        waitListService.addToWaitList(allAppointmentIds, true);
        // sync max id as scrollId to redis
        updateTaskScrollId(scrollId.get());
        return Boolean.TRUE;
    }

    public int queryTaskScrollId() {
        int scrollId = 0;
        String value = redisUtil.get(OBGroomingService.OB_AUTO_MOVE_WAITLIST_SCROLL_ID);
        if (StringUtils.hasText(value)) {
            scrollId = Integer.parseInt(value);
        }
        return scrollId;
    }

    public void updateTaskScrollId(int scrollId) {
        log.info("updateTaskScrollId scrollId:{}", scrollId);
        redisUtil.set(OB_AUTO_MOVE_WAITLIST_SCROLL_ID, String.valueOf(scrollId));
    }

    /**
     * query appointment for online booking
     *
     * @param bookingQueryVO
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageInfo<GroomingBookingDTO> queryGroomingBookingAppointment(
            GroomingBookingQueryVO bookingQueryVO, Integer pageNum, Integer pageSize) {
        var migrateInfo = migrateHelper.getMigrationInfo(bookingQueryVO.getBusinessId());
        // 分页查询只支持单表，没有关联宠物信息
        List<GroomingBookingDTO> groomingList;
        try (Page<GroomingBookingDTO> page = PageHelper.startPage(pageNum, pageSize)) {
            page.doSelectPage(() -> moeGroomingAppointmentMapper.queryGroomingBookingAppointment(bookingQueryVO));
            groomingList = page.getResult();
            if (CollectionUtils.isEmpty(groomingList)) {
                return new PageInfo<>(groomingList);
            }
            addGroomingBookingExtraInfo(
                    migrateInfo.isMigrate(), migrateInfo.companyId(), bookingQueryVO.getBusinessId(), groomingList);
            return new PageInfo<>(groomingList);
        }
    }

    void addGroomingBookingExtraInfo(
            boolean migrated, long companyId, Integer businessId, List<GroomingBookingDTO> groomingList) {
        List<Integer> groomingIds =
                groomingList.stream().map(GroomingBookingDTO::getGroomingId).collect(toList());
        List<GroomingBookingDTO> groomingAppointmentWithPetList =
                moeGroomingAppointmentMapper.queryGroomingBookingAppointmentJoin(groomingIds);
        Map<Integer, GroomingBookingDTO> groomingAppointmentWithPetMap =
                groomingAppointmentWithPetList.stream().collect(toMap(GroomingBookingDTO::getGroomingId, a -> a));

        // 搜集宠物ID 和 customer ID
        Set<Integer> petIds = groomingAppointmentWithPetList.stream()
                .map(GroomingBookingDTO::getPetList)
                .flatMap(List::stream)
                .map(GroomingCustomerPetdetailDTO::getPetId)
                .collect(toSet());
        Set<Integer> customerIdSet = new HashSet<>();
        Set<Integer> staffIds = new HashSet<>();
        for (GroomingBookingDTO customerGrooming : groomingList) {
            if (Objects.nonNull(customerGrooming.getCustomerId())) {
                customerIdSet.add(customerGrooming.getCustomerId());
            }
            if (Objects.nonNull(customerGrooming.getStaffId())) {
                staffIds.add(customerGrooming.getStaffId());
            }
        }

        // staff detail
        CompletableFuture<Map<Integer, MoeStaffDto>> staffDetailFuture = CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(staffIds)) {
                        return Map.of();
                    }
                    StaffIdListParams staffIdListParams = new StaffIdListParams();
                    staffIdListParams.setBusinessId(businessId);
                    staffIdListParams.setStaffIdList(new ArrayList<>(staffIds));
                    return iBusinessStaffClient.getStaffNames(staffIdListParams);
                },
                ThreadPool.getSubmitExecutor());

        // customer details
        CompletableFuture<Map<Integer, com.moego.server.customer.dto.GroomingCustomerInfoDTO>> customerDetailFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            if (CollectionUtils.isEmpty(customerIdSet)) {
                                return Map.of();
                            }
                            GroomingQueryCustomerParams groomingQueryCustomerParams = new GroomingQueryCustomerParams();
                            groomingQueryCustomerParams.setBusinessId(businessId);
                            groomingQueryCustomerParams.setCustomerIds(new ArrayList<>(customerIdSet));
                            List<com.moego.server.customer.dto.GroomingCustomerInfoDTO> customerInfoDTOS =
                                    iCustomerGroomingClient.getCustomerGroomingInfo(groomingQueryCustomerParams);
                            return customerInfoDTOS.stream()
                                    .collect(toMap(
                                            com.moego.server.customer.dto.GroomingCustomerInfoDTO::getCustomerId,
                                            Function.identity(),
                                            (c1, c2) -> c1));
                        },
                        ThreadPool.getSubmitExecutor());

        // finished appt
        CompletableFuture<Map<Integer, CustomerGroomingAppointmentDTO>> lastFinishedApptFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            if (CollectionUtils.isEmpty(customerIdSet)) {
                                return Map.of();
                            }
                            CustomerLastFinishedApptMapDto lastFinishedApptMapDto =
                                    appointmentQueryService.getCustomerLastFinishedAppointment(
                                            migrated, companyId, businessId, new ArrayList<>(customerIdSet));
                            return lastFinishedApptMapDto.getLastFinishedApptMap();
                        },
                        ThreadPool.getSubmitExecutor());

        // pet detail
        CompletableFuture<Map<Integer, CustomerPetDetailDTO>> petDetailFuture = CompletableFuture.supplyAsync(
                () -> {
                    List<CustomerPetDetailDTO> customerPetListByIdList =
                            iPetClient.getCustomerPetListByIdList(new ArrayList<>(petIds));
                    return customerPetListByIdList.stream()
                            .collect(toMap(CustomerPetDetailDTO::getPetId, Function.identity(), (p1, p2) -> p1));
                },
                ThreadPool.getSubmitExecutor());

        // customer has request update
        CompletableFuture<Map<Integer, CustomerHasRequestDTO>> customerHasUpdateFuture = CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(customerIdSet)) {
                        return Map.of();
                    }
                    return customerService.listCustomerHasRequestUpdate(businessId, new ArrayList<>(customerIdSet));
                },
                ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(
                        staffDetailFuture,
                        customerDetailFuture,
                        lastFinishedApptFuture,
                        customerHasUpdateFuture,
                        petDetailFuture)
                .join();

        Map<Integer, MoeStaffDto> staffDtoMap = staffDetailFuture.join();
        Map<Integer, com.moego.server.customer.dto.GroomingCustomerInfoDTO> customerInfoDTOMap =
                customerDetailFuture.join();
        Map<Integer, CustomerGroomingAppointmentDTO> lastFinishedApptMap = lastFinishedApptFuture.join();
        Map<Integer, CustomerHasRequestDTO> customerHasUpdateMap = customerHasUpdateFuture.join();
        Map<Integer, CustomerPetDetailDTO> petDetailDTOMap = petDetailFuture.join();

        for (GroomingBookingDTO groomingBookingDto : groomingList) {
            CustomerHasRequestDTO requestDTO = customerHasUpdateMap.get(groomingBookingDto.getCustomerId());
            groomingBookingDto.setHasRequestUpdate(Objects.nonNull(requestDTO) && requestDTO.hasRequestUpdate());
            Integer groomingId = groomingBookingDto.getGroomingId();
            // staff detail
            if (Objects.nonNull(groomingBookingDto.getStaffId())) {
                MoeStaffDto staffDto = staffDtoMap.get(groomingBookingDto.getStaffId());
                if (Objects.nonNull(staffDto)) {
                    groomingBookingDto.setStaffFirstName(staffDto.getFirstName());
                    groomingBookingDto.setStaffLastName(staffDto.getLastName());
                }
            }

            // 补充pet 信息 ERP-849
            GroomingBookingDTO bookInfo = groomingAppointmentWithPetMap.get(groomingId);
            if (bookInfo != null && !CollectionUtils.isEmpty(bookInfo.getPetList())) {
                groomingBookingDto.setPetList(bookInfo.getPetList());

                for (GroomingCustomerPetdetailDTO petDetail : groomingBookingDto.getPetList()) {
                    Map<Integer, CustomerProfileRequestDTO.PetProfileDTO> requestPetMap = Map.of();
                    if (Objects.nonNull(requestDTO)
                            && Objects.nonNull(requestDTO.profileRequest())
                            && !CollectionUtils.isEmpty(
                                    requestDTO.profileRequest().getPets())) {
                        requestPetMap = requestDTO.profileRequest().getPets().stream()
                                .collect(toMap(CustomerProfileRequestDTO.PetProfileDTO::getPetId, Function.identity()));
                    }
                    CustomerPetDetailDTO petDetailDTO = petDetailDTOMap.get(petDetail.getPetId());
                    if (Objects.nonNull(petDetailDTO)) {
                        CustomerProfileRequestDTO.PetProfileDTO requestPet = requestPetMap.get(petDetail.getPetId());
                        // Only the pet name is allowed to be modified here
                        petDetail.setPetName(
                                Objects.nonNull(requestPet) && StringUtils.hasText(requestPet.getPetName())
                                        ? requestPet.getPetName()
                                        : petDetailDTO.getPetName());
                        petDetail.setPetBreed(petDetailDTO.getBreed());
                    }
                }
            }

            // 补充customer信息 get customer detail
            com.moego.server.customer.dto.GroomingCustomerInfoDTO customerInfoDTO =
                    customerInfoDTOMap.get(groomingBookingDto.getCustomerId());
            if (Objects.nonNull(customerInfoDTO)) {
                groomingBookingDto.setPhoneNumber(customerInfoDTO.getPhoneNumber());
                groomingBookingDto.setCustomerFirstName(customerInfoDTO.getFirstName());
                groomingBookingDto.setCustomerLastName(customerInfoDTO.getLastName());
                groomingBookingDto.setAvatarPath(customerInfoDTO.getAvatarPath());
                groomingBookingDto.setIsNewCustomer(
                        Objects.isNull(lastFinishedApptMap.get(groomingBookingDto.getCustomerId())));
            }

            // 增加查询支付相关金额
            MoeGroomingInvoice invoice = invoiceService.queryInvoiceByGroomingId(businessId, groomingId);
            if (!Objects.isNull(invoice)) {
                groomingBookingDto.setPaidAmount(invoice.getPaidAmount());
                groomingBookingDto.setRefundAmount(invoice.getRefundedAmount());
                // 查询定金记录
                MoeBookOnlineDeposit deposit =
                        moeBookOnlineDepositService.getOBDepositByGroomingId(businessId, groomingId);
                if (!Objects.isNull(deposit)) {
                    if (DepositPaymentTypeEnum.PrePay.equals(deposit.getDepositType())) {
                        groomingBookingDto.setPrepaidAmount(deposit.getAmount());
                        groomingBookingDto.setPrepayStatus(deposit.getStatus());
                        groomingBookingDto.setPrepayRate(
                                moeBookOnlineDepositService.getPrepayRate(deposit, invoice.getPaymentAmount()));
                    } else {
                        groomingBookingDto.setEnablePreAuth(true);
                    }
                }
            }
        }
    }

    public PageInfo<GroomingBookingDTO> queryGroomingBookingAppointmentV2(
            boolean migrated,
            long companyId,
            GroomingBookingQueryVO bookingQueryVO,
            Integer pageNum,
            Integer pageSize) {

        Byte isWaitingList;
        if (bookingQueryVO.getType() == 2) {
            isWaitingList = GroomingAppointmentEnum.IS_WAITING_LIST;
        } else {
            isWaitingList = GroomingAppointmentEnum.NOT_WAITING_LIST;
        }
        String order;
        if (bookingQueryVO.getOrderType().equals("desc")) {
            order = "create_time desc";
        } else {
            order = "create_time";
        }
        PageInfo<GroomingBookingDTO> result = new PageInfo<>();
        List<MoeGroomingAppointment> appointmentList;
        try (Page<MoeGroomingAppointment> page = PageHelper.startPage(pageNum, pageSize)) {
            page.doSelectPage(() -> moeGroomingAppointmentMapper.queryByOnlineBook(
                    bookingQueryVO.getBusinessId(), List.of(AppointmentStatusEnum.UNCONFIRMED), isWaitingList, order));
            appointmentList = page.getResult();
            result.setTotal(page.getTotal());
        }
        List<Integer> groomingIds =
                appointmentList.stream().map(MoeGroomingAppointment::getId).toList();
        if (CollectionUtils.isEmpty(groomingIds)) {
            result.setList(List.of());
            return result;
        }
        List<GroomingBookingDTO> tmpGroomingList =
                moeGroomingAppointmentMapper.queryGroomingBookingAppointmentJoin(groomingIds);
        // 对查询结果按入参排序
        List<GroomingBookingDTO> groomingList = new ArrayList<>();
        appointmentList.forEach(k -> {
            for (GroomingBookingDTO groomingBooking : tmpGroomingList) {
                if (k.getId().equals(groomingBooking.getGroomingId())) {
                    groomingList.add(groomingBooking);
                    break;
                }
            }
        });

        addGroomingBookingExtraInfo(migrated, companyId, bookingQueryVO.getBusinessId(), groomingList);
        List<Integer> serviceIds = groomingList.stream()
                .map(GroomingBookingDTO::getPetList)
                .flatMap(List::stream)
                .map(GroomingCustomerPetdetailDTO::getServiceId)
                .toList();
        Map<Integer, MoeGroomingService> serviceMap =
                groomingServiceService.getServiceMap(bookingQueryVO.getBusinessId(), serviceIds);
        groomingList.forEach(k -> {
            k.getPetList().forEach(pet -> {
                MoeGroomingService service = serviceMap.get(pet.getServiceId());
                if (service != null) {
                    pet.setServiceName(service.getName());
                }
            });
        });
        if (bookingQueryVO.getType().equals(2)) {
            List<MoeWaitList> waitLists = waitListService.batchGetWaitListByAppointment(
                    bookingQueryVO.getBusinessId().longValue(),
                    groomingIds.stream().map(Integer::longValue).toList());
            Map<Long, MoeWaitList> waitListMap =
                    waitLists.stream().collect(Collectors.toMap(MoeWaitList::getAppointmentId, Function.identity()));
            Map<Integer, MoeStaffDto> staffInfo =
                    waitListService.getStaffInfo(bookingQueryVO.getBusinessId().longValue(), waitLists);

            groomingList.forEach(appt -> {
                MoeWaitList waitList = waitListMap.get(appt.getGroomingId().longValue());
                if (waitList != null) {
                    WaitListCompatibleDTO waitListCompatibleDTO = new WaitListCompatibleDTO();
                    waitListCompatibleDTO.setRealWaitListId(waitList.getId());
                    waitListCompatibleDTO.setTimePreference(
                            TimePreferenceMapper.INSTANCE.toDto(waitList.getTimePreference()));
                    waitListCompatibleDTO.setDatePreference(
                            DatePreferenceMapper.INSTANCE.toDto(waitList.getDatePreference()));
                    waitListCompatibleDTO.setStaffPreference(
                            waitListService.staffPreferencePO2DTO(waitList.getStaffPreference(), staffInfo));
                    appt.setWaitListDTO(waitListCompatibleDTO);
                }
            });
        }
        result.setList(groomingList);
        return result;
    }
}
