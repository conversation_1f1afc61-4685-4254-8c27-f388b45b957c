package com.moego.server.grooming.service.utils;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/17
 */
public class ProfileConflictUtils {

    public static boolean conflict(
            CustomerProfileRequestDTO.ClientProfileDTO request, CustomerProfileRequestDTO.ClientProfileDTO existing) {
        if (Objects.isNull(request) || Objects.isNull(existing)) {
            return false;
        }
        return (conflict(request.getEmail(), existing.getEmail())
                        || conflict(request.getFirstName(), existing.getFirstName())
                        || conflict(request.getLastName(), existing.getLastName())
                        || conflict(request.getReferralSourceId(), existing.getReferralSourceId())
                        || conflict(request.getPreferredGroomerId(), existing.getPreferredGroomerId())
                        || conflict(request.getPreferredFrequencyType(), existing.getPreferredFrequencyType())
                        || conflict(request.getPreferredFrequencyDay(), existing.getPreferredFrequencyDay())
                        || conflict(request.getPreferredDay(), existing.getPreferredDay())
                        || conflict(request.getPreferredTime(), existing.getPreferredTime())
                        || conflict(request.getCustomQuestions(), existing.getCustomQuestions()))
                || isBirthdayConflict(request.getBirthday(), existing.getBirthday());
    }

    public static boolean conflict(
            CustomerProfileRequestDTO.PetProfileDTO request, CustomerProfileRequestDTO.PetProfileDTO existing) {
        if (Objects.isNull(request) || Objects.isNull(existing)) {
            return false;
        }
        return (conflict(request.getAvatarPath(), existing.getAvatarPath())
                || conflict(request.getPetName(), existing.getPetName())
                || conflict(request.getGender(), existing.getGender())
                || conflict(request.getBirthday(), existing.getBirthday())
                || conflict(request.getWeight(), existing.getWeight())
                || conflict(request.getFixed(), existing.getFixed())
                || conflict(request.getBehavior(), existing.getBehavior())
                || conflict(request.getHairLength(), existing.getHairLength())
                || conflict(request.getVetName(), existing.getVetName())
                || conflict(request.getVetPhone(), existing.getVetPhone())
                || conflict(request.getVetAddress(), existing.getVetAddress())
                || conflict(request.getEmergencyContactName(), existing.getEmergencyContactName())
                || conflict(request.getEmergencyContactPhone(), existing.getEmergencyContactPhone())
                || conflict(request.getHealthIssues(), existing.getHealthIssues())
                || conflict(request.getVaccineList(), existing.getVaccineList())
                || conflict(request.getCustomQuestions(), existing.getCustomQuestions()));
    }

    public static boolean conflict(String request, String existing) {
        if (Objects.isNull(request)) {
            return false;
        }
        return !Objects.equals(request, existing);
    }

    public static boolean conflict(Byte request, Byte existing) {
        if (Objects.isNull(request)) {
            return false;
        }
        return !Objects.equals(request, existing);
    }

    public static boolean conflict(Integer request, Integer existing) {
        if (Objects.isNull(request)) {
            return false;
        }
        return !Objects.equals(request, existing);
    }

    public static boolean conflict(Integer[] request, Integer[] existing) {
        if (Objects.isNull(request)) {
            return false;
        }
        return !Arrays.equals(request, existing);
    }

    public static boolean isBirthdayConflict(LocalDateTime requestBirthday, LocalDateTime existingBirthday) {
        var request = requestBirthday != null ? requestBirthday.toLocalDate() : null;
        var existing = existingBirthday != null ? existingBirthday.toLocalDate() : null;
        if (Objects.isNull(request)) {
            return false;
        }
        return !Objects.equals(request, existing);
    }

    public static boolean conflict(Map<String, Object> request, Map<String, Object> existing) {
        if (CollectionUtils.isEmpty(request)) {
            return false;
        }
        for (Map.Entry<String, Object> entry : request.entrySet()) {
            Object requestValue = entry.getValue();
            if (Objects.isNull(requestValue)) {
                continue;
            }
            Object existingValue = existing.get(entry.getKey());
            if (Objects.isNull(existingValue)
                    || !Objects.equals(requestValue, existingValue)
                    || !Objects.equals(requestValue.toString(), existingValue.toString())) {
                return true;
            }
        }
        return false;
    }

    public static boolean conflict(
            List<VaccineBindingRecordDto> requestVaccines, List<VaccineBindingRecordDto> existingVaccines) {
        Map<Integer, VaccineBindingRecordDto> existingMap = existingVaccines.stream()
                .collect(Collectors.toMap(VaccineBindingRecordDto::getVaccineBindingId, Function.identity()));
        for (VaccineBindingRecordDto request : requestVaccines) {
            if (request == null) {
                continue;
            }
            // New vaccine 也需要 B 端 review
            if (request.getVaccineBindingId() == null) {
                return true;
            }
            VaccineBindingRecordDto existing = existingMap.get(request.getVaccineBindingId());
            if (Objects.isNull(existing)) {
                continue;
            }
            if (!Objects.equals(request.getExpirationDate(), existing.getExpirationDate())
                    || !Objects.equals(request.getDocumentUrls(), existing.getDocumentUrls())) {
                return true;
            }
        }
        return false;
    }

    public static CustomerProfileRequestDTO replaceConflictProfile(
            CustomerProfileRequestDTO request, CustomerProfileRequestDTO existing) {
        if (Objects.isNull(request)) {
            return existing;
        }
        if (Objects.isNull(existing)) {
            return CustomerProfileRequestDTO.builder().build();
        }
        return CustomerProfileRequestDTO.builder()
                .client(replaceConflictClientProfile(request.getClient(), existing.getClient()))
                .pets(replaceConflictPetProfile(request.getPets(), existing.getPets()))
                .build();
    }

    public static CustomerProfileRequestDTO.ClientProfileDTO replaceConflictClientProfile(
            CustomerProfileRequestDTO.ClientProfileDTO request, CustomerProfileRequestDTO.ClientProfileDTO existing) {
        if (Objects.isNull(request)) {
            return existing;
        }
        if (Objects.isNull(existing)) {
            return null;
        }
        CustomerProfileRequestDTO.ClientProfileDTO result = new CustomerProfileRequestDTO.ClientProfileDTO();
        result.setFirstName(existing.getFirstName());
        result.setLastName(existing.getLastName());
        result.setPhoneNumber(existing.getPhoneNumber());
        result.setEmail(replaceConflict(request.getEmail(), existing.getEmail()));
        result.setReferralSourceId(replaceConflict(request.getReferralSourceId(), existing.getReferralSourceId()));
        result.setPreferredGroomerId(
                replaceConflict(request.getPreferredGroomerId(), existing.getPreferredGroomerId()));
        result.setPreferredFrequencyType(
                replaceConflict(request.getPreferredFrequencyType(), existing.getPreferredFrequencyType()));
        result.setPreferredFrequencyDay(
                replaceConflict(request.getPreferredFrequencyDay(), existing.getPreferredFrequencyDay()));
        result.setPreferredDay(replaceConflict(request.getPreferredDay(), existing.getPreferredDay()));
        result.setPreferredTime(replaceConflict(request.getPreferredTime(), existing.getPreferredTime()));
        result.setCustomQuestions(
                replaceConflictCustomQuestions(request.getCustomQuestions(), existing.getCustomQuestions()));
        result.setBirthday(replaceConflict(request.getBirthday(), existing.getBirthday()));
        return result;
    }

    public static List<CustomerProfileRequestDTO.PetProfileDTO> replaceConflictPetProfile(
            List<CustomerProfileRequestDTO.PetProfileDTO> requestPets,
            List<CustomerProfileRequestDTO.PetProfileDTO> existingPets) {
        if (CollectionUtils.isEmpty(requestPets)) {
            return existingPets;
        }
        Map<Integer, CustomerProfileRequestDTO.PetProfileDTO> requestPetMap = requestPets.stream()
                .collect(Collectors.toMap(CustomerProfileRequestDTO.PetProfileDTO::getPetId, Function.identity()));
        return existingPets.stream()
                .map(existing -> replaceConflictPetProfile(requestPetMap.get(existing.getPetId()), existing))
                .toList();
    }

    public static CustomerProfileRequestDTO.PetProfileDTO replaceConflictPetProfile(
            CustomerProfileRequestDTO.PetProfileDTO request, CustomerProfileRequestDTO.PetProfileDTO existing) {
        if (Objects.isNull(request)) {
            return existing;
        }
        CustomerProfileRequestDTO.PetProfileDTO result = new CustomerProfileRequestDTO.PetProfileDTO();
        result.setPetId(existing.getPetId());
        result.setBreed(existing.getBreed());
        result.setPetTypeId(existing.getPetTypeId());
        result.setAvatarPath(replaceConflict(request.getAvatarPath(), existing.getAvatarPath()));
        result.setPetName(replaceConflict(request.getPetName(), existing.getPetName()));
        result.setBreedMix(replaceConflict(request.getBreedMix(), existing.getBreedMix()));
        result.setGender(replaceConflict(request.getGender(), existing.getGender()));
        result.setBirthday(replaceConflict(request.getBirthday(), existing.getBirthday()));
        result.setWeight(replaceConflict(request.getWeight(), existing.getWeight()));
        result.setFixed(replaceConflict(request.getFixed(), existing.getFixed()));
        result.setBehavior(replaceConflict(request.getBehavior(), existing.getBehavior()));
        result.setHairLength(replaceConflict(request.getHairLength(), existing.getHairLength()));
        result.setVetName(replaceConflict(request.getVetName(), existing.getVetName()));
        result.setVetPhone(replaceConflict(request.getVetPhone(), existing.getVetPhone()));
        result.setVetAddress(replaceConflict(request.getVetAddress(), existing.getVetAddress()));
        result.setEmergencyContactName(
                replaceConflict(request.getEmergencyContactName(), existing.getEmergencyContactName()));
        result.setEmergencyContactPhone(
                replaceConflict(request.getEmergencyContactPhone(), existing.getEmergencyContactPhone()));
        result.setHealthIssues(replaceConflict(request.getHealthIssues(), existing.getHealthIssues()));
        result.setCustomQuestions(
                replaceConflictCustomQuestions(request.getCustomQuestions(), existing.getCustomQuestions()));
        result.setVaccineList(replaceConflictVaccineList(request.getVaccineList(), existing.getVaccineList()));
        return result;
    }

    public static List<VaccineBindingRecordDto> replaceConflictVaccineList(
            List<VaccineBindingRecordDto> requestVaccines, List<VaccineBindingRecordDto> existingVaccines) {
        if (CollectionUtils.isEmpty(requestVaccines)) {
            return existingVaccines;
        }
        if (CollectionUtils.isEmpty(existingVaccines)) {
            return List.of();
        }
        Map<Integer, VaccineBindingRecordDto> requestVaccinMap = requestVaccines.stream()
                .filter(e -> isNormal(e.getVaccineBindingId()))
                .collect(Collectors.toMap(
                        VaccineBindingRecordDto::getVaccineBindingId, Function.identity(), (o, n) -> n));
        Map<Integer, VaccineBindingRecordDto> existingVaccineMap = existingVaccines.stream()
                .filter(e -> isNormal(e.getVaccineBindingId()))
                .collect(Collectors.toMap(
                        VaccineBindingRecordDto::getVaccineBindingId, Function.identity(), (o, n) -> n));
        var existingVaccineList = existingVaccineMap.entrySet().stream()
                .map(existingEntry -> {
                    VaccineBindingRecordDto requestVaccine = requestVaccinMap.get(existingEntry.getKey());
                    VaccineBindingRecordDto existingVaccine = existingEntry.getValue();
                    if (Objects.isNull(requestVaccine)) {
                        return existingEntry.getValue();
                    }
                    return new VaccineBindingRecordDto()
                            .setVaccineBindingId(existingVaccine.getVaccineBindingId())
                            .setVaccineId(existingVaccine.getVaccineId())
                            .setVaccineName(existingVaccine.getVaccineName())
                            .setExpirationDate(replaceConflict(
                                    requestVaccine.getExpirationDate(), existingVaccine.getExpirationDate()))
                            .setDocumentUrls(replaceConflict(
                                    requestVaccine.getDocumentUrls(), existingVaccine.getDocumentUrls()));
                })
                .toList();

        var newVaccineList = requestVaccines.stream()
                .filter(e -> !isNormal(e.getVaccineBindingId()))
                .toList();

        var result = new ArrayList<VaccineBindingRecordDto>();
        result.addAll(existingVaccineList);
        result.addAll(newVaccineList);
        return result;
    }

    public static Map<String, Object> replaceConflictCustomQuestions(
            Map<String, Object> requestCustomQuestions, Map<String, Object> existingCustomQuestions) {
        if (CollectionUtils.isEmpty(requestCustomQuestions)) {
            return existingCustomQuestions;
        }
        if (CollectionUtils.isEmpty(existingCustomQuestions)) {
            return Map.of();
        }
        Map<String, Object> result = new HashMap<>();
        existingCustomQuestions.forEach(
                (key, value) -> result.put(key, replaceConflict(requestCustomQuestions.get(key), value)));
        return result;
    }

    public static Object replaceConflict(Object request, Object existing) {
        return Objects.nonNull(request) && StringUtils.hasText(request.toString()) ? request : existing;
    }

    public static String replaceConflict(String request, String existing) {
        return StringUtils.hasText(request) ? request : existing;
    }

    public static Integer replaceConflict(Integer request, Integer existing) {
        return Objects.nonNull(request) ? request : existing;
    }

    public static Byte replaceConflict(Byte request, Byte existing) {
        return Objects.nonNull(request) ? request : existing;
    }

    public static Integer[] replaceConflict(Integer[] request, Integer[] existing) {
        return !ObjectUtils.isEmpty(request) ? request : existing;
    }

    public static <T> List<T> replaceConflict(List<T> request, List<T> existing) {
        return !CollectionUtils.isEmpty(request) ? request : existing;
    }

    public static LocalDateTime replaceConflict(LocalDateTime request, LocalDateTime existing) {
        return request != null ? request : existing;
    }
}
