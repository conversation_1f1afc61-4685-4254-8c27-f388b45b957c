package com.moego.server.grooming.web.vo.ob;

import com.moego.server.customer.dto.CustomerAddressDto;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;

@Builder(toBuilder = true)
public record OBClientDetailVO(
        Integer businessId,
        Integer customerId,
        String firstName,
        String lastName,
        String phoneNumber,
        String email,
        String avatarPath,
        String clientColor,
        Integer referralSourceId,
        Integer preferredGroomerId,
        Byte preferredFrequencyType,
        Integer preferredFrequencyDay,
        Integer[] preferredDay,
        Integer[] preferredTime,
        List<OBRequestDetailVO.QuestionAnswerVO> questionAnswerList,
        CustomerAddressDto primaryAddress,
        List<CustomerAddressDto> newAddresses,
        LocalDateTime birthday) {}
