package com.moego.server.grooming.web.vo.ob;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmartScheduleV2Request {

    @NotNull
    private String addressLat;

    @NotNull
    private String addressLng;

    private String addressZipcode;

    @Nullable
    @Pattern(message = "Invalid date format, valid example: 2022-02-08", regexp = "^(\\d{4}-\\d{2}-\\d{2})$")
    @Schema(description = "开始查询的日期")
    private String date;

    @Nullable
    @Schema(description = "one of date or dates must be provided, uses dates first")
    private List<LocalDate> dates;

    @NotNull
    @Schema(description = "time in minutes")
    private Integer serviceDuration;

    @Schema(description = "available staff map time in minutes")
    private Map<Long, Integer> staffServiceDuration;

    @NotNull
    private List<Integer> staffIds;

    @NotNull
    @Max(42)
    @Min(1)
    @Schema(description = "需要获取的天数")
    private Integer count;

    @NotNull
    @Max(365)
    @Schema(description = "需要查询的最远天数")
    private Integer farthestDay;

    @Schema(description = "是否关闭ss功能， 默认false")
    private Boolean disableSmartScheduling;

    @Schema(description = "需要排除掉的groomingId")
    private Integer filterGroomingId;

    @Schema(description = "是否校验Certain Area Certain Day， 默认false")
    private boolean checkCACD;

    @Schema(description = "内部使用标志位， set from backend")
    private boolean isFromOB;

    @Schema(description = "需要运用客户偏好的客户ID，过滤出指定的week和time")
    private Integer applyClientPreferenceCustomerId;

    @JsonIgnore
    @Schema(description = "query first available time slot flag", hidden = true)
    private Boolean queryOne;

    @Schema(description = "第一个可用天返回全量 time slot，其余天仅返回每半天的第一个可用 time slot")
    private Boolean queryPerHalfDay;

    @Schema(description = "The query time slots ends at the end of the month of the first available date")
    private Boolean queryEndOfTheMonth;

    @Schema(description = "pet param info, include weight and breedId")
    private List<OBPetDataDTO> petParamList;

    @Schema(description = "ob version")
    private Byte useVersion;

    @Schema(description = "ob time 使用 working hour 的 staff")
    private Set<Integer> syncWithWorkingHourStaff;

    /**
     * After the query limit number of days, return the result directly
     */
    private Integer queryLimitDays;

    /**
     * The limited query time when the query time range exceeds limit days
     */
    private Integer overLimitDaysSeconds;

    private Integer bufferTime;

    @Schema(description = "每个 staff 需要获取的天数")
    private Integer queryCountPerStaff;

    @Schema(description = "service ids")
    private List<Integer> serviceIds;
}
