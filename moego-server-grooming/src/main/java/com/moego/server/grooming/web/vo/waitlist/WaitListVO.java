package com.moego.server.grooming.web.vo.waitlist;

import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.grooming.dto.waitlist.DatePreferenceDTO;
import com.moego.server.grooming.dto.waitlist.StaffPreferenceDTO;
import com.moego.server.grooming.dto.waitlist.TimePreferenceDTO;
import com.moego.server.grooming.web.vo.ob.OBRequestDetailVO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class WaitListVO {
    private Long id;
    private Long appointmentId;
    private Boolean isAvailable;
    private DatePreferenceDTO datePreference;
    private TimePreferenceDTO timePreference;
    private StaffPreferenceDTO staffPreference;
    private LocalDate validFrom;
    private LocalDate validTill;
    private LocalDateTime createTime;
    private String ticketComment;
    private CustomerVO customerInfo;
    private Boolean hasRequestUpdate;
    private List<CertainAreaDTO> certainAreaList;
    private BigDecimal price;
    private Integer duration;
    private List<PetServiceVO> petList;
    private OBRequestDetailVO.OBPrepayDetailVO prepay;
}
