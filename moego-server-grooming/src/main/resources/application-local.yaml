spring:
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:mysql://mysql.t2.moego.dev:40107/moe_grooming?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: moego_developer_240310_eff7a0dc
    password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
  data:
    redis:
      host: redis.t2.moego.dev
      password: iMoReGoTdesstingeCache250310_7fec987d
      port: 40179
      timeout: 60000
      key:
        delimiter: ":"
        prefix: local
      ssl:
        enabled: false
  activemq:
    broker-url: ssl://b-f80a5cc9-31ab-4781-8914-ecb9fe93c94a-1.mq.us-west-2.amazonaws.com:61617
    password: +S+22Y8FlzqazglDiXbwIA
    user: moego-active-mq-dev-v1-master
    enabled: true
    destinationPrefix: refund

s3:
  key: ********************
  secret: C68h5Eb7ZaP/TqD11+OGc2NsTTpMXYoq+7PZiAiR
  region: us-west-2
  public:
    domain: https://moegonew.s3-us-west-2.amazonaws.com/
    bucket: moegonew
    prefix:
      signature: signature/
      export-file: export/
log:
  path: ${LOG_PATH:./logs}

customer:
  invoice:
    url: 'http://**************:8111/payonline/'
  upcoming:
    key: 'AAAA33*2020_123456789AAA'
    url: 'http://**************:8111/appointment/upcoming?id='

exchange:
  rate:
    accessKey: 84d723027299a4317deff6ee99e9e59e
    apiKey: 4uNsYaMK3kBUchING92m8znyH3r7CSO8
    base: USD
    param:
      symbols: AUD,BRL,CAD,EUR,GBP,NZD,ZAR,USD
    url: http://api.exchangeratesapi.io/latest
    newUrl: https://api.apilayer.com/exchangerates_data/latest?symbols=%s&base=%s

google:
  calendar:
    auth:
      client:
        id: 951819712767-b0bnkb8oaombndr23ah22oqcp9jk50b3.apps.googleusercontent.com
        secret: 9fekT7b93NN19hMKBhbucWsw
      redirect:
        uri: https://go.t2.moego.pet/GoogleCalendarSync/callback
  event:
    web:
      hook: https://api.t2.moego.pet/grooming/google/calendar/event/webhook
  analytics:
    property-id: 347480732

quickbooks:
  api:
    host: https://sandbox-quickbooks.api.intuit.com
  client:
    id: ABNEBsMGUKGkX3vQwUowBEqM0CRPsXJekuvzvmwdnPvHgyCRAp
    secret: EVo6ob7bM31RGRO1c93SNfYXvQ8rLmptOVYfN6IK
  environment: SANDBOX
  oAuth:
    redirectUri: https://go.t2.moego.dev/quickbook/activate
  web:
    invoice:
      url: 'https://app.sandbox.qbo.intuit.com/app/invoice?txnId='

moego:
  grpc:
    server:
      debug-enabled: true
  messaging:
    enabled: true
    pulsar:
      service-url: pulsar.t2.moego.pet:40650
      authentication: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      tenant: test2
  event-bus:
    brokers:
      - name: default
        addresses:
          - kafka.kafka.svc.cluster.local:9092
  feature-flag:
    growth-book:
      api-host: https://growthbook.moego.pet/growthbook-api
      client-key: sdk-qygeRRneunZQJxf
logging:
  level:
    com.moego.server.grooming.mapper: DEBUG
    org.springframework.jdbc.core: DEBUG

