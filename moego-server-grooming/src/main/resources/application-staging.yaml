spring:
  config:
    import:
      - "aws-secretsmanager:moego/staging/datasource/grooming?prefix=secret.datasource.grooming."
      - "aws-secretsmanager:moego/staging/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/staging/google?prefix=secret.google."
      - "aws-secretsmanager:moego/staging/growthbook?prefix=secret.growthbook."
      - "aws-secretsmanager:moego/staging/quickbooks?prefix=secret.quickbooks."
      - "aws-secretsmanager:moego/staging/mq?prefix=secret.mq."
      - "aws-secretsmanager:moego/staging/aws?prefix=secret.aws."
      - "aws-secretsmanager:moego/staging/apilayer?prefix=secret.apilayer."
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:mysql://${secret.datasource.grooming.mysql.url}:${secret.datasource.grooming.mysql.port}/moe_grooming?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.grooming.mysql.username}
    password: ${secret.datasource.grooming.mysql.password}
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
customer:
  invoice:
    url: https://client.s1.moego.dev/payonline/
  upcoming:
    key: 'AAAA33*2020_123456789AAA'
    url: 'https://client.s1.moego.dev/appointment/upcoming?id='

exchange:
  rate:
    base: USD
    param:
      symbols: AUD,BRL,CAD,EUR,GBP,NZD,ZAR,USD
    url: http://api.exchangeratesapi.io/latest
    newUrl: https://api.apilayer.com/exchangerates_data/latest?symbols=%s&base=%s

google:
  calendar:
    auth:
      redirect:
        uri: https://go.s1.moego.dev/GoogleCalendarSync/callback
  event:
    web:
      hook: https://api.s1.moego.dev/grooming/google/calendar/event/webhook

quickbooks:
  api:
    host: https://sandbox-quickbooks.api.intuit.com
  environment: SANDBOX
  oAuth:
    redirectUri: https://go.s1.moego.dev/quickbook/activate
  web:
    invoice:
      url: https://app.sandbox.qbo.intuit.com/app/invoice?txnId=

moego:
  messaging:
    pulsar:
      tenant: staging
  google-reserve:
    conversion-tracking-url: https://www.google.com/maps/conversion/collect
  grooming-report:
    share-key: Q3Cbuf6uonTauA1e8mp55PWU
logging:
  level:
    com.moego.lib.messaging: DEBUG
