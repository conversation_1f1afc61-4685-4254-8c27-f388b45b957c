spring:
  config:
    import:
      - "aws-secretsmanager:moego/testing/datasource/grooming?prefix=secret.datasource.grooming."
      - "aws-secretsmanager:moego/testing/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/testing/google?prefix=secret.google."
      - "aws-secretsmanager:moego/testing/growthbook?prefix=secret.growthbook."
      - "aws-secretsmanager:moego/testing/quickbooks?prefix=secret.quickbooks."
      - "aws-secretsmanager:moego/testing/mq?prefix=secret.mq."
      - "aws-secretsmanager:moego/testing/aws?prefix=secret.aws."
      - "aws-secretsmanager:moego/testing/apilayer?prefix=secret.apilayer."
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:mysql://${secret.datasource.grooming.mysql.url}:${secret.datasource.grooming.mysql.port}/moe_grooming?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.grooming.mysql.username}
    password: ${secret.datasource.grooming.mysql.password}
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
moego:
  messaging:
    pulsar:
      tenant: test2
logging:
  level:
    com.moego.lib.messaging: DEBUG
