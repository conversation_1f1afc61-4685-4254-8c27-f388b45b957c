server:
  port: 9206

spring:
  application:
    name: moego-server-grooming
  profiles:
    active: local
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:mysql://${secret.datasource.grooming.mysql.url}:${secret.datasource.grooming.mysql.port}/moe_grooming?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.grooming.mysql.username}
    password: ${secret.datasource.grooming.mysql.password}
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
  data:
    redis:
      host: ${secret.redis.host}
      password: ${secret.redis.password}
      port: ${secret.redis.port}
      timeout: 60000
      key:
        delimiter: ":"
        prefix: "apiv2"
      ssl:
        enabled: ${secret.redis.tls}
  activemq:
    broker-url: ${secret.mq.activemq.url}
    password: ${secret.mq.activemq.password}
    user: ${secret.mq.activemq.user}
    enabled: true
    destinationPrefix: refund
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-west-2}
s3:
  key: ${secret.aws.access_key_id}
  secret: ${secret.aws.secret_access_key}
  region: ${secret.aws.region}
  public:
    domain: 'https://moegonew.s3-us-west-2.amazonaws.com/'
    bucket: 'moegonew'
    prefix:
      signature: 'signature/'
      export-file: 'export/'

customer:
  invoice:
    url: 'http://**************:8111/payonline/'
  upcoming:
    key: 'AAAA33*2020_123456789AAA'
    url: 'http://**************:8111/appointment/upcoming?id='

default:
  max:
    driving:
      speed: '27.78'

exchange:
  rate:
    accessKey: ${secret.apilayer.exchange.rate.accessKey}
    apiKey: ${secret.apilayer.exchange.rate.apiKey}
    base: USD
    param:
      symbols: AUD,BRL,CAD,EUR,GBP,NZD,ZAR,USD
    url: http://api.exchangeratesapi.io/latest
    newUrl: https://api.apilayer.com/exchangerates_data/latest?symbols=%s&base=%s

google:
  calendar:
    auth:
      client:
        id: ${secret.google.calendar.auth.client.id}
        secret: ${secret.google.calendar.auth.client.secret}
      redirect:
        uri: https://go.t2.moego.pet/GoogleCalendarSync/callback
  event:
    web:
      hook: https://api.t2.moego.pet/grooming/google/calendar/event/webhook
  analytics:
    property-id: 347480732

monitoring:
  metrics:
    enabled: true
    url:
      prefix: /grooming

mybatis:
  mapper-locations: classpath*:/com/moego/server/grooming/mapperxml/*.xml
  type-aliases-package: com.moego.server.grooming.mapperbean

pagehelper:
  helperDialect: mysql
  page-size-zero: true
  params: count=countSql
  reasonable: false
  supportMethodsArguments: true

quickbooks:
  api:
    host: https://sandbox-quickbooks.api.intuit.com
  client:
    id: ${secret.quickbooks.client.id}
    secret: ${secret.quickbooks.client.secret}
  environment: SANDBOX
  oAuth:
    redirectUri: https://go.t2.moego.dev/quickbook/activate
  web:
    invoice:
      url: 'https://app.sandbox.qbo.intuit.com/app/invoice?txnId='

sentry:
  dsn: 'https://<EMAIL>/5'

smart-scheduling:
  timeout: 60
  core-pool-size: 200
  maximum-pool-size: 400
  alive-time-seconds: 60
  queue-capacity: 600
  repeat:
    free-user-available-times: 20
    times-limit: 24

client-portal:
  core-pool-size: 10
  maximum-pool-size: 10
  alive-time-seconds: 0
  queue-capacity: 100
  notification-ahead-minutes: 65

develop-script:
  timeout: 60
  core-pool-size: 20
  maximum-pool-size: 40
  alive-time-seconds: 60
  queue-capacity: 1000

moego:
  session:
    company-is-migrate-metadata-key-id: 62
  messaging:
    enabled: true
    pulsar:
      service-url: ${secret.mq.pulsar.service_url}
      authentication: ${secret.mq.pulsar.token}
      tenant: test2
  event-bus:
    brokers:
      - name: default
        addresses:
          - ${secret.mq.kafka.broker_url_0}
          - ${secret.mq.kafka.broker_url_1}
          - ${secret.mq.kafka.broker_url_2}
        security:
          enabled: true
          properties:
            security.protocol: SASL_SSL
            sasl.mechanism: AWS_MSK_IAM
            sasl.jaas.config: software.amazon.msk.auth.iam.IAMLoginModule required;
            sasl.client.callback.handler.class: software.amazon.msk.auth.iam.IAMClientCallbackHandler
    producer:
      # 如果 enabled 为 false (默认值), 则不会初始化 producer, 此时如果代码里依赖了 producer, 则会抛出异常
      enabled: true
      # 发送消息成功时是否打印日志, 默认为 false
      log-success: false
      # 发送消息失败时是否打印日志, 默认为 true
      log-failure: true
  server:
    url:
      business: http://moego-service-business:9203
      customer: http://moego-service-customer:9201
      message: http://moego-service-message:9205
      payment: http://moego-service-payment:9204
      retail: http://moego-service-retail:9207
  grpc:
    client:
      stubs:
        - service: moego.service.map.**
          authority: moego-svc-map:9090
        - service: moego.service.order.v1.**
          authority: moego-svc-order:9090
        - service: moego.service.agreement.**
          authority: moego-svc-agreement:9090
        - service: moego.service.metadata.**
          authority: moego-svc-metadata:9090
        - service: moego.service.activity_log.**
          authority: moego-svc-activity-log:9090
        - service: moego.service.risk_control.**
          authority: moego-svc-risk-control:9090
        - service: moego.service.notification.**
          authority: moego-svc-notification:9090
        - service: moego.service.marketing.**
          authority: moego-svc-marketing:9090
        - service: moego.service.message.**
          authority: moego-svc-message:9090
        - service: moego.service.auto_message.v1.**
          authority: moego-svc-auto-message:9090
        - service: moego.service.business_customer.**
          authority: moego-svc-business-customer:9090
        - service: moego.service.permission.**
          authority: moego-svc-permission:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.offering.**
          authority: moego-svc-offering:9090
        - service: moego.service.appointment.**
          authority: moego-svc-appointment:9090
        - service: moego.service.fulfillment.**
          authority: moego-svc-fulfillment:9090
        - service: moego.service.online_booking.**
          authority: moego-svc-online-booking:9090
        - service: moego.service.account.**
          authority: moego-svc-account:9090
        - service: moego.service.membership.**
          authority: moego-svc-membership:9090
        - service: moego.service.ratelimit.**
          authority: moego-svc-ratelimit:9090
        - service: moego.service.smart_scheduler.**
          authority: moego-svc-smart-scheduler:9090
        - service: moego.service.order.v2.**
          authority: moego-svc-order-v2:9090
        - service: backend.proto.customer.v1.**
          authority: moego-customer:9090
  google-reserve:
    partner-id: ********
    conversion-tracking-url: https://www.google.com/maps/conversion/debug/collect
  grooming-report:
    share-key: f8481cae6c837bef7caea8a8
  driving-info:
    core-pool-size: 200
    maximum-pool-size: 200
    alive-time-seconds: 0
    queue-capacity: 500
  feature-flag:
    growth-book:
      api-host: ${secret.growthbook.host}
      client-key: ${secret.growthbook.client_key}

springdoc:
  packages-to-scan:
    - com.moego.server.grooming.web
order:
  offset:
    retail: 20000000
    new: 100000000
