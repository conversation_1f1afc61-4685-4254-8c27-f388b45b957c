package com.moego.server.grooming.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@Disabled("local test only")
class MoeGroomingAppointmentMapperTest {
    @Autowired
    private MoeGroomingAppointmentMapper moeGroomingAppointmentMapper;

    @Test
    @Transactional
    void updateByPrimaryKeySelective() {
        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setId(1);
        moeGroomingAppointment.setStatus((byte) 2);
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);
        assertNotNull(moeGroomingAppointment.getUpdateTime());
    }

    @Test
    @Transactional
    void deleteAppointments() {
        var existAppt = moeGroomingAppointmentMapper.selectByPrimaryKey(1);
        var bid = 11;
        if (existAppt == null) {
            MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
            moeGroomingAppointment.setId(1);
            moeGroomingAppointment.setBusinessId(bid);
            moeGroomingAppointmentMapper.insert(moeGroomingAppointment);
        } else {
            bid = existAppt.getBusinessId();
        }
        moeGroomingAppointmentMapper.deleteAppointments(List.of(1, 2), bid);

        var newAppt = moeGroomingAppointmentMapper.selectByPrimaryKey(1);
        assertNotNull(newAppt);
        assertEquals(1, newAppt.getIsDeprecate());
    }

    @Test
    @Transactional
    void batchUpdateByPrimaryKeySelective() {
        var appointment1 = new MoeGroomingAppointment();
        appointment1.setId(1);
        var appointment2 = new MoeGroomingAppointment();
        appointment2.setId(2);
        List<MoeGroomingAppointment> records = List.of(appointment1, appointment2);
        moeGroomingAppointmentMapper.batchUpdateByPrimaryKeySelective(records);
    }
}
