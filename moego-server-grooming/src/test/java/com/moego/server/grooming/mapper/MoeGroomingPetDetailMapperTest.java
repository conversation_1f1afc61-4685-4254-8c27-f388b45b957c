package com.moego.server.grooming.mapper;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Disabled("For local test")
public class MoeGroomingPetDetailMapperTest {
    @Autowired
    private MoeGroomingPetDetailMapper moeGroomingPetDetailMapper;

    @Test
    void updateByPrimaryKeySelective() {
        MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
        moeGroomingPetDetail.setId(1);
        moeGroomingPetDetail.setStartDate("2024-01-01");
        moeGroomingPetDetailMapper.updateByPrimaryKeySelective(moeGroomingPetDetail);
        assertNotNull(moeGroomingPetDetail.getUpdateTime());
    }
}
