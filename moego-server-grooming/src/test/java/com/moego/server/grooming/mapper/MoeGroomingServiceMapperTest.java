package com.moego.server.grooming.mapper;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.moego.server.grooming.mapperbean.MoeGroomingService;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@Disabled("For local test")
class MoeGroomingServiceMapperTest {
    @Autowired
    private MoeGroomingServiceMapper moeGroomingServiceMapper;

    @Test
    @Transactional
    void updateByPrimaryKeySelective() {
        MoeGroomingService moeGroomingService = new MoeGroomingService();
        moeGroomingService.setId(1);
        moeGroomingService.setStatus((byte) 2);
        moeGroomingServiceMapper.updateByPrimaryKeySelective(moeGroomingService);
        assertNotNull(moeGroomingService.getUpdateTime());
    }

    @Test
    @Transactional
    void batchUpdateSort() {
        var service1 = new MoeGroomingService();
        service1.setId(1);
        service1.setSort(1);
        var service2 = new MoeGroomingService();
        service2.setId(2);
        service2.setSort(2);
        moeGroomingServiceMapper.batchUpdateSort(List.of(service1, service2));
    }

    @Test
    @Transactional
    void setServiceCategoryDefaultForCid() {
        moeGroomingServiceMapper.setServiceCategoryDefaultForCid(1, 1L);
    }

    @Test
    @Transactional
    void batchUpdateCategoryId() {
        moeGroomingServiceMapper.batchUpdateCategoryId(1, 2, 1L);
    }
}
