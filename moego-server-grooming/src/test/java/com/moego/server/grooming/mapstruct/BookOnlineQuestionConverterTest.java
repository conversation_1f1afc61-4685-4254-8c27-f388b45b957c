package com.moego.server.grooming.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.AcceptPetEntryType;
import com.moego.idl.models.online_booking.v1.ExistingClientAccessMode;
import com.moego.idl.models.online_booking.v1.ExistingPetAccessMode;
import com.moego.idl.models.online_booking.v1.NewClientAccessMode;
import com.moego.idl.models.online_booking.v1.NewPetAccessMode;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.params.BookOnlineQuestionParams;
import org.junit.jupiter.api.Test;

class BookOnlineQuestionConverterTest {

    @Test
    void entityToDTO_shouldConvertCustomerAccessModes() {
        // Both enabled -> BOTH
        MoeBookOnlineQuestion entity1 = new MoeBookOnlineQuestion();
        entity1.setType(GroomingQuestionDTO.Type.PET_OWNER);
        entity1.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity1.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        entity1.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity1.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        assertThat(BookOnlineQuestionConverter.entityToDTO(entity1).getAcceptCustomerType())
                .isEqualTo(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);

        // New enabled, Existing disabled -> NEW
        MoeBookOnlineQuestion entity2 = new MoeBookOnlineQuestion();
        entity2.setType(GroomingQuestionDTO.Type.PET_OWNER);
        entity2.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity2.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_DISABLED);
        entity2.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity2.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        assertThat(BookOnlineQuestionConverter.entityToDTO(entity2).getAcceptCustomerType())
                .isEqualTo(AcceptCustomerType.NEW_CUSTOMER_VALUE);

        // New disabled, Existing enabled -> EXISTING
        MoeBookOnlineQuestion entity3 = new MoeBookOnlineQuestion();
        entity3.setType(GroomingQuestionDTO.Type.PET_OWNER);
        entity3.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_DISABLED);
        entity3.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW);
        entity3.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity3.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        assertThat(BookOnlineQuestionConverter.entityToDTO(entity3).getAcceptCustomerType())
                .isEqualTo(AcceptCustomerType.EXISTING_CUSTOMER_VALUE);
    }

    @Test
    void entityToDTO_shouldConvertPetAccessModes() {
        // Both enabled -> NEW_AND_EXISTING
        MoeBookOnlineQuestion entity1 = new MoeBookOnlineQuestion();
        entity1.setType(GroomingQuestionDTO.Type.PET);
        entity1.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity1.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);
        entity1.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity1.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        assertThat(BookOnlineQuestionConverter.entityToDTO(entity1).getAcceptPetEntryType())
                .isEqualTo(AcceptPetEntryType.NEW_AND_EXISTING_VALUE);

        // New enabled, Existing disabled -> NEW
        MoeBookOnlineQuestion entity2 = new MoeBookOnlineQuestion();
        entity2.setType(GroomingQuestionDTO.Type.PET);
        entity2.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        entity2.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_DISABLED);
        entity2.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity2.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        assertThat(BookOnlineQuestionConverter.entityToDTO(entity2).getAcceptPetEntryType())
                .isEqualTo(AcceptPetEntryType.NEW_VALUE);

        // New disabled, Existing enabled -> EXISTING
        MoeBookOnlineQuestion entity3 = new MoeBookOnlineQuestion();
        entity3.setType(GroomingQuestionDTO.Type.PET);
        entity3.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_DISABLED);
        entity3.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW);
        entity3.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        entity3.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);
        assertThat(BookOnlineQuestionConverter.entityToDTO(entity3).getAcceptPetEntryType())
                .isEqualTo(AcceptPetEntryType.EXISTING_VALUE);
    }

    @Test
    void paramToEntity_shouldConvertCustomerAccessModes() {
        // BOTH -> new enabled, existing view_and_edit
        BookOnlineQuestionParams param1 = new BookOnlineQuestionParams();
        param1.setAcceptCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        param1.setQuestion("Custom Question");
        MoeBookOnlineQuestion entity1 = BookOnlineQuestionConverter.paramToEntity(param1);
        assertThat(entity1.getNewClientAccessMode()).isEqualTo(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        assertThat(entity1.getExistingClientAccessMode())
                .isEqualTo(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);

        // BOTH with "Phone number" -> new enabled, existing view only
        BookOnlineQuestionParams param2 = new BookOnlineQuestionParams();
        param2.setAcceptCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        param2.setQuestion("Phone number");
        MoeBookOnlineQuestion entity2 = BookOnlineQuestionConverter.paramToEntity(param2);
        assertThat(entity2.getNewClientAccessMode()).isEqualTo(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        assertThat(entity2.getExistingClientAccessMode())
                .isEqualTo(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW);

        // NEW -> new enabled, existing disabled
        BookOnlineQuestionParams param3 = new BookOnlineQuestionParams();
        param3.setAcceptCustomerType(AcceptCustomerType.NEW_CUSTOMER_VALUE);
        MoeBookOnlineQuestion entity3 = BookOnlineQuestionConverter.paramToEntity(param3);
        assertThat(entity3.getNewClientAccessMode()).isEqualTo(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        assertThat(entity3.getExistingClientAccessMode())
                .isEqualTo(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_DISABLED);

        // EXISTING -> new disabled, existing view_and_edit
        BookOnlineQuestionParams param4 = new BookOnlineQuestionParams();
        param4.setAcceptCustomerType(AcceptCustomerType.EXISTING_CUSTOMER_VALUE);
        param4.setQuestion("Custom Question");
        MoeBookOnlineQuestion entity4 = BookOnlineQuestionConverter.paramToEntity(param4);
        assertThat(entity4.getNewClientAccessMode()).isEqualTo(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_DISABLED);
        assertThat(entity4.getExistingClientAccessMode())
                .isEqualTo(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);

        // BOTH with "Referral source" -> new enabled, existing view only
        BookOnlineQuestionParams param5 = new BookOnlineQuestionParams();
        param5.setAcceptCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        param5.setQuestion("Referral source");
        MoeBookOnlineQuestion entity5 = BookOnlineQuestionConverter.paramToEntity(param5);
        assertThat(entity5.getNewClientAccessMode()).isEqualTo(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        assertThat(entity5.getExistingClientAccessMode())
                .isEqualTo(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW);
    }

    @Test
    void paramToEntity_shouldConvertPetAccessModes() {
        // NEW_AND_EXISTING with regular question -> new enabled, existing view_and_edit
        BookOnlineQuestionParams param1 = new BookOnlineQuestionParams();
        param1.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING_VALUE);
        param1.setQuestion("Pet name");
        MoeBookOnlineQuestion entity1 = BookOnlineQuestionConverter.paramToEntity(param1);
        assertThat(entity1.getNewPetAccessMode()).isEqualTo(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        assertThat(entity1.getExistingPetAccessMode())
                .isEqualTo(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);

        // NEW_AND_EXISTING with "Pet type" -> new enabled, existing view only
        BookOnlineQuestionParams param1a = new BookOnlineQuestionParams();
        param1a.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING_VALUE);
        param1a.setQuestion("Pet type");
        MoeBookOnlineQuestion entity1a = BookOnlineQuestionConverter.paramToEntity(param1a);
        assertThat(entity1a.getNewPetAccessMode()).isEqualTo(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        assertThat(entity1a.getExistingPetAccessMode()).isEqualTo(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW);

        // NEW_AND_EXISTING with "Pet breed" -> new enabled, existing view only
        BookOnlineQuestionParams param1b = new BookOnlineQuestionParams();
        param1b.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING_VALUE);
        param1b.setQuestion("Pet breed");
        MoeBookOnlineQuestion entity1b = BookOnlineQuestionConverter.paramToEntity(param1b);
        assertThat(entity1b.getNewPetAccessMode()).isEqualTo(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        assertThat(entity1b.getExistingPetAccessMode()).isEqualTo(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW);

        // NEW -> new enabled, existing disabled
        BookOnlineQuestionParams param2 = new BookOnlineQuestionParams();
        param2.setAcceptPetEntryType(AcceptPetEntryType.NEW_VALUE);
        MoeBookOnlineQuestion entity2 = BookOnlineQuestionConverter.paramToEntity(param2);
        assertThat(entity2.getNewPetAccessMode()).isEqualTo(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        assertThat(entity2.getExistingPetAccessMode())
                .isEqualTo(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_DISABLED);

        // EXISTING with regular question -> new disabled, existing view_and_edit
        BookOnlineQuestionParams param3 = new BookOnlineQuestionParams();
        param3.setAcceptPetEntryType(AcceptPetEntryType.EXISTING_VALUE);
        param3.setQuestion("Pet name");
        MoeBookOnlineQuestion entity3 = BookOnlineQuestionConverter.paramToEntity(param3);
        assertThat(entity3.getNewPetAccessMode()).isEqualTo(NewPetAccessMode.NEW_PET_ACCESS_MODE_DISABLED);
        assertThat(entity3.getExistingPetAccessMode())
                .isEqualTo(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT);

        // EXISTING with "Pet type" -> new disabled, existing view only
        BookOnlineQuestionParams param3a = new BookOnlineQuestionParams();
        param3a.setAcceptPetEntryType(AcceptPetEntryType.EXISTING_VALUE);
        param3a.setQuestion("Pet type");
        MoeBookOnlineQuestion entity3a = BookOnlineQuestionConverter.paramToEntity(param3a);
        assertThat(entity3a.getNewPetAccessMode()).isEqualTo(NewPetAccessMode.NEW_PET_ACCESS_MODE_DISABLED);
        assertThat(entity3a.getExistingPetAccessMode()).isEqualTo(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW);

        // EXISTING with "Pet breed" -> new disabled, existing view only
        BookOnlineQuestionParams param3b = new BookOnlineQuestionParams();
        param3b.setAcceptPetEntryType(AcceptPetEntryType.EXISTING_VALUE);
        param3b.setQuestion("Pet breed");
        MoeBookOnlineQuestion entity3b = BookOnlineQuestionConverter.paramToEntity(param3b);
        assertThat(entity3b.getNewPetAccessMode()).isEqualTo(NewPetAccessMode.NEW_PET_ACCESS_MODE_DISABLED);
        assertThat(entity3b.getExistingPetAccessMode()).isEqualTo(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW);
    }

    @Test
    void paramToEntity_shouldConvertAllFields() {
        BookOnlineQuestionParams params = new BookOnlineQuestionParams();
        params.setId(1);
        params.setBusinessId(2);
        params.setQuestion("Test Question");
        params.setPlaceholder("Test Placeholder");
        params.setIsShow((byte) 1);
        params.setIsRequired((byte) 1);
        params.setType((byte) 1);
        params.setIsAllowDelete((byte) 1);
        params.setIsAllowChange((byte) 1);
        params.setIsAllowEdit((byte) 1);
        params.setSort(1);
        params.setStatus((byte) 1);
        params.setCreateTime(1234567890L);
        params.setUpdateTime(1234567890L);
        params.setQuestionType((byte) 1);
        params.setCompanyId(3L);
        params.setExtraJson("{\"key\":\"value\"}");
        params.setAcceptCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
        params.setAcceptPetEntryType(AcceptPetEntryType.NEW_AND_EXISTING_VALUE);
        params.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED.getNumber());
        params.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW.getNumber());
        params.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED.getNumber());
        params.setExistingClientAccessMode(
                ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT.getNumber());

        var expect = new MoeBookOnlineQuestion();
        expect.setId(1);
        expect.setBusinessId(2);
        expect.setQuestion("Test Question");
        expect.setPlaceholder("Test Placeholder");
        expect.setIsShow((byte) 1);
        expect.setIsRequired((byte) 1);
        expect.setType((byte) 1);
        expect.setIsAllowDelete((byte) 1);
        expect.setIsAllowChange((byte) 1);
        expect.setIsAllowEdit((byte) 1);
        expect.setSort(1);
        expect.setStatus((byte) 1);
        expect.setCreateTime(1234567890L);
        expect.setUpdateTime(1234567890L);
        expect.setQuestionType((byte) 1);
        expect.setCompanyId(3L);
        expect.setExtraJson("{\"key\":\"value\"}");
        expect.setNewPetAccessMode(NewPetAccessMode.NEW_PET_ACCESS_MODE_ENABLED);
        expect.setExistingPetAccessMode(ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW);
        expect.setNewClientAccessMode(NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_ENABLED);
        expect.setExistingClientAccessMode(ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT);

        var actual = BookOnlineQuestionConverter.paramToEntity(params);

        assertThat(actual).usingRecursiveComparison().isEqualTo(expect);
    }
}
