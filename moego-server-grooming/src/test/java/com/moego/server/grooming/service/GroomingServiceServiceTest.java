/*
 * @since 2023-02-11 11:59:12
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.idl.models.marketing.v1.DiscountCodeModel;
import com.moego.idl.models.marketing.v1.DiscountCodeType;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GroomingServiceServiceTest {

    @InjectMocks
    private GroomingServiceService groomingServiceService;

    @Test
    public void testStringListMatchCI() {
        assertThat(groomingServiceService.stringListContainsCI(
                        "Affenpinscher,Afghan Hound,Airedale Terrier,Pointer,German longhaired pointer",
                        "Affenpinscher"))
                .isTrue();
        assertThat(groomingServiceService.stringListContainsCI(
                        "Affenpinscher,Afghan Hound,Airedale Terrier,Pointer,German longhaired pointer", "Pointer"))
                .isTrue();
        assertThat(groomingServiceService.stringListContainsCI(
                        "Affenpinscher,Afghan Hound,Airedale Terrier,Pointer,German longhaired pointer",
                        "German longhaired pointer"))
                .isTrue();
        assertThat(groomingServiceService.stringListContainsCI(
                        "Affenpinscher,Afghan Hound,Airedale Terrier,Pointer,German longhaired pointer",
                        "longhaired pointer"))
                .isFalse();
        assertThat(groomingServiceService.stringListContainsCI("Affenpinscher", "Affenpinscher"))
                .isTrue();
    }

    @Test
    void getStaffIdsForAllService() {
        List<MoeGroomingService> serviceList = List.of(
                new MoeGroomingService() {
                    {
                        setId(100);
                        setType(ServiceEnum.TYPE_ADD_ONS);
                        setIsAllStaff(CustomerContactEnum.IS_ALL_STAFF);
                    }
                },
                new MoeGroomingService() {
                    {
                        setId(200);
                        setType(ServiceEnum.TYPE_SERVICE);
                        setIsAllStaff(CustomerContactEnum.IS_ALL_STAFF);
                    }
                },
                new MoeGroomingService() {
                    {
                        setId(300);
                        setType(ServiceEnum.TYPE_SERVICE);
                    }
                },
                new MoeGroomingService() {
                    {
                        setId(400);
                        setType(ServiceEnum.TYPE_SERVICE);
                    }
                });
        Map<Integer, List<Integer>> serviceStaffIds = Map.of(300, List.of(1, 2, 3), 400, List.of(3, 5, 7));
        List<Integer> result =
                groomingServiceService.getStaffIdsForAllService(List.of(2, 3, 5), serviceList, serviceStaffIds);
        assertThat(result).containsExactly(3);
    }

    @Test
    void testCalculateDiscountAmount_PercentageType_ShouldReturnOriginalAmount() {
        // Given - 百分比类型的折扣码
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE)
                .setAmount(15.0) // 15%
                .build();
        BigDecimal totalPriceWithCharges = new BigDecimal("100.00");

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(new BigDecimal("15.0"), result);
    }

    @Test
    void testCalculateDiscountAmount_FixedAmountType_LessThanTotal_ShouldReturnDiscountAmount() {
        // Given - 固定金额类型，折扣金额小于总价
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_FIXED_AMOUNT)
                .setAmount(20.0) // $20
                .build();
        BigDecimal totalPriceWithCharges = new BigDecimal("100.00");

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(new BigDecimal("20.0"), result);
    }

    @Test
    void testCalculateDiscountAmount_FixedAmountType_GreaterThanTotal_ShouldReturnTotalPrice() {
        // Given - 固定金额类型，折扣金额大于总价
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_FIXED_AMOUNT)
                .setAmount(150.0) // $150
                .build();
        BigDecimal totalPriceWithCharges = new BigDecimal("100.00");

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(new BigDecimal("100.00"), result);
    }

    @Test
    void testCalculateDiscountAmount_CreditType_LessThanTotal_ShouldReturnDiscountAmount() {
        // Given - CREDIT 类型，折扣金额小于总价
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_CREDIT)
                .setAmount(30.0) // $30
                .build();
        BigDecimal totalPriceWithCharges = new BigDecimal("100.00");

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(new BigDecimal("30.0"), result);
    }

    @Test
    void testCalculateDiscountAmount_ZeroTotalPrice_ShouldReturnZero() {
        // Given - 总价为0的边界情况
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_FIXED_AMOUNT)
                .setAmount(50.0) // $50
                .build();
        BigDecimal totalPriceWithCharges = BigDecimal.ZERO;

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void testCalculateDiscountAmount_PercentageType_SmallPercentage_ShouldReturnExactValue() {
        // Given - 百分比类型，小数点后多位的百分比
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE)
                .setAmount(12.5) // 12.5%
                .build();
        BigDecimal totalPriceWithCharges = new BigDecimal("200.00");

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(new BigDecimal("12.5"), result);
    }

    @Test
    void testCalculateDiscountAmount_PercentageType_NegativePercentage_ShouldReturnZero() {
        // Given - 百分比类型，负数百分比应该被限制为 0%
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE)
                .setAmount(-10.0) // -10%
                .build();
        BigDecimal totalPriceWithCharges = new BigDecimal("100.00");

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void testCalculateDiscountAmount_PercentageType_OverHundredPercent_ShouldReturnHundred() {
        // Given - 百分比类型，超过 100% 的百分比应该被限制为 100%
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE)
                .setAmount(150.0) // 150%
                .build();
        BigDecimal totalPriceWithCharges = new BigDecimal("100.00");

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(new BigDecimal("100"), result);
    }

    @Test
    void testCalculateDiscountAmount_PercentageType_ExactlyHundredPercent_ShouldReturnHundred() {
        // Given - 百分比类型，正好 100% 的百分比
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE)
                .setAmount(100.0) // 100%
                .build();
        BigDecimal totalPriceWithCharges = new BigDecimal("100.00");

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(new BigDecimal("100.0"), result);
    }

    @Test
    void testCalculateDiscountAmount_PercentageType_ExactlyZeroPercent_ShouldReturnZero() {
        // Given - 百分比类型，正好 0% 的百分比
        DiscountCodeModel discountCodeModel = DiscountCodeModel.newBuilder()
                .setType(DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE)
                .setAmount(0.0) // 0%
                .build();
        BigDecimal totalPriceWithCharges = new BigDecimal("100.00");

        // When
        BigDecimal result = GroomingServiceService.calculateDiscountAmount(discountCodeModel, totalPriceWithCharges);

        // Then
        assertEquals(new BigDecimal("0.0"), result);
    }
}
