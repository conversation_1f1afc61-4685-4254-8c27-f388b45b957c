package com.moego.server.grooming.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.idl.models.smart_scheduler.v1.SmartScheduleSettingModel;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.smart_scheduler.v1.GetSmartScheduleSettingResponse;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessClosedDateClient;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.client.IBusinessSmartSchedulingClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.ServiceAreaSettingDTO;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.ss.SmartScheduleResultDto;
import com.moego.server.grooming.dto.ss.SmartScheduleStaffMap;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.params.ss.SmartScheduleRequest;
import com.moego.server.grooming.service.dto.SmartScheduleDTO;
import com.moego.server.grooming.service.remote.SmartSchedulerService;
import com.moego.server.grooming.web.vo.SmartScheduleParam;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @since 2022/8/5
 */
@ExtendWith(MockitoExtension.class)
public class SmartSchedule2ServiceTest {

    @InjectMocks
    private SmartScheduleService smartScheduleService;

    @Mock
    private IBusinessClosedDateClient iBusinessClosedDateClient;

    @Mock
    private IBusinessSmartSchedulingClient iBusinessSmartSchedulingClient;

    @Mock
    private IBusinessServiceAreaClient iBusinessServiceAreaClient;

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    @Mock
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Mock
    private PetDetailMapperProxy petDetailMapper;

    @Mock
    private GoogleMapService googleMapService;

    @Mock
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    private SmartSchedulerService smartSchedulerService;

    @Mock
    private PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceStub;

    @Mock
    private CompanyHelper companyHelper;

    @Mock
    private StaffTimeSyncService staffTimeSyncService;

    private final SmartScheduleRequest smartScheduleRequest = new SmartScheduleRequest();
    private final Integer companyId = 123;
    private final Integer businessId = 123;
    private final Integer staffId = 10000;
    private final Integer customerId = 10000;
    private final String startDate = "2022-08-05";
    private final Integer farthestDays = 365;
    private final List<Integer> staffIdList = new ArrayList<>();

    private final int BUFFER_TIME = 20;

    @BeforeEach
    public void initializeRequest() {
        smartScheduleRequest.setFarthestDay(365);
        smartScheduleRequest.setCount(42);
        smartScheduleRequest.setDate("2022-08-05");
        smartScheduleRequest.setFilterGroomingId(null);
        smartScheduleRequest.setAddressLat("22.53925");
        smartScheduleRequest.setAddressLng("113.9431");
        smartScheduleRequest.setServiceDuration(60);
        smartScheduleRequest.setStaffIds(Collections.singletonList(staffId));
        smartScheduleRequest.setDisableSmartScheduling(false);
        smartScheduleRequest.setBufferTime(20);
        staffIdList.add(staffId);
    }

    @Test
    public void smartSchedule2() {
        ServiceAreaSettingDTO serviceAreaSettingDTO = new ServiceAreaSettingDTO();
        serviceAreaSettingDTO.setBusinessId(businessId);
        serviceAreaSettingDTO.setServiceAreaEnable(CommonConstant.DISABLE);

        Map<Integer, Map<Integer, TimeRangeDto>> weekStaffWorkingHours = new HashMap<>();
        Map<Integer, TimeRangeDto> timeRangeDtoMap = new HashMap<>();
        TimeRangeDto timeRangeDto = new TimeRangeDto();
        timeRangeDto.setStartTime(540);
        timeRangeDto.setEndTime(1140);
        timeRangeDtoMap.put(staffId, timeRangeDto);
        weekStaffWorkingHours.put(1, timeRangeDtoMap);
        // doReturn(weekStaffWorkingHours).when(iBusinessStaffClient).getBusinessStaffWorkingHour(businessId,
        // staffIdList);
        // Map<Integer, Map<LocalDate, List<TimeRangeDto>>> dateStaffWorkingHours = new HashMap<>();
        // Map<LocalDate, List<TimeRangeDto>> dateListMap = new HashMap<>();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = start.plusDays(365);
        long days = ChronoUnit.DAYS.between(start, end);
        // for (int i = 0; i <= days; i++) {
        //     LocalDate localDate = start.plusDays(i);
        //     dateListMap.put(localDate, Collections.singletonList(timeRangeDto));
        // }
        // dateStaffWorkingHours.put(staffId, dateListMap);
        // doReturn(dateStaffWorkingHours)
        //         .when(iBusinessStaffClient)
        //         .getStaffWorkingHourWithOverrideDate(businessId, staffIdList, startDate, end.toString());

        MoeBusinessCustomerDTO customerDTO = new MoeBusinessCustomerDTO();
        customerDTO.setBusinessId(businessId);
        customerDTO.setPreferredDay(Arrays.asList(1, 2, 3).toArray(new Integer[0]));
        customerDTO.setPreferredTime(Arrays.asList(840, 960).toArray(new Integer[0]));
        doReturn(customerDTO).when(iCustomerCustomerClient).getCustomerWithDeleted(customerId);

        BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
        businessPreference.setTimezoneName("Asia/Shanghai");
        doReturn(businessPreference).when(iBusinessBusinessClient).getBusinessPreference(businessId);

        doReturn(companyId.longValue()).when(companyHelper).mustGetCompanyId(businessId);

        doReturn(GetSmartScheduleSettingResponse.newBuilder()
                        .setSmartScheduleSetting(SmartScheduleSettingModel.newBuilder()
                                .setServiceAreaEnable(false)
                                .build())
                        .build())
                .when(smartSchedulerService)
                .getSmartSchedulerSetting(companyId);

        Map<String, Map<Integer, StaffTime>> dateStaffTime = new HashMap<>();
        Map<Integer, StaffTime> dateLisTimetMap = new HashMap<>();
        for (int i = 0; i <= days; i++) {
            LocalDate localDate = start.plusDays(i);
            var staffTime = new StaffTime();
            staffTime.setIsSelected(true);
            staffTime.setTimeRange(Collections.singletonList(timeRangeDto));
            dateLisTimetMap.put(staffId, staffTime);
            dateStaffTime.put(localDate.toString(), dateLisTimetMap);
        }
        doReturn(dateStaffTime)
                .when(staffTimeSyncService)
                .getStaffTimeMap(anySet(), anyString(), anyString(), any(), anyInt(), anyInt(), anyLong());

        smartScheduleRequest.setApplyClientPreferenceCustomerId(customerId);
        SmartScheduleResultDto smartScheduleResultDto =
                smartScheduleService.smartSchedule2(businessId, smartScheduleRequest);
        Assertions.assertThat(smartScheduleResultDto.isAvailable())
                .as("available must be true")
                .isTrue();
        Map<String, SmartScheduleStaffMap> dayMap = smartScheduleResultDto.getDayMap();
        Assertions.assertThat(dayMap.size()).as("available day size must be 19").isEqualTo(19);
    }

    @Test
    public void smartSchedule2RemainCount() {
        doReturn(Map.of()).when(iBusinessSmartSchedulingClient).getStaffSmartScheduleSettingMap(Mockito.any());

        Map<Integer, Map<Integer, TimeRangeDto>> weekStaffWorkingHours = new HashMap<>();
        Map<Integer, TimeRangeDto> timeRangeDtoMap = new HashMap<>();
        TimeRangeDto timeRangeDto = new TimeRangeDto();
        timeRangeDto.setStartTime(540);
        timeRangeDto.setEndTime(1140);
        timeRangeDtoMap.put(staffId, timeRangeDto);
        weekStaffWorkingHours.put(0, timeRangeDtoMap);
        weekStaffWorkingHours.put(1, timeRangeDtoMap);
        weekStaffWorkingHours.put(2, timeRangeDtoMap);
        weekStaffWorkingHours.put(3, timeRangeDtoMap);
        weekStaffWorkingHours.put(4, timeRangeDtoMap);
        weekStaffWorkingHours.put(5, timeRangeDtoMap);
        weekStaffWorkingHours.put(6, timeRangeDtoMap);
        // doReturn(weekStaffWorkingHours).when(iBusinessStaffClient).getBusinessStaffWorkingHour(businessId,
        // staffIdList);
        // Map<Integer, Map<LocalDate, List<TimeRangeDto>>> dateStaffWorkingHours = new HashMap<>();
        // Map<LocalDate, List<TimeRangeDto>> dateListMap = new HashMap<>();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = start.plusDays(365);
        long days = ChronoUnit.DAYS.between(start, end);
        // for (int i = 0; i <= days; i++) {
        //     LocalDate localDate = start.plusDays(i);
        //     dateListMap.put(localDate, Collections.singletonList(timeRangeDto));
        // }
        // dateStaffWorkingHours.put(staffId, dateListMap);
        // doReturn(dateStaffWorkingHours)
        //         .when(iBusinessStaffClient)
        //         .getStaffWorkingHourWithOverrideDate(businessId, staffIdList, startDate, end.toString());

        doReturn(companyId.longValue()).when(companyHelper).mustGetCompanyId(businessId);

        BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
        businessPreference.setTimezoneName("Asia/Shanghai");
        doReturn(businessPreference).when(iBusinessBusinessClient).getBusinessPreference(businessId);

        // doReturn(new
        // CompanyIdDTO(companyId.longValue())).when(iBusinessBusinessClient).getCompanyIdByBusinessId(businessId);

        Map<String, Map<Integer, StaffTime>> dateStaffTime = new HashMap<>();
        Map<Integer, StaffTime> dateLisTimetMap = new HashMap<>();
        for (int i = 0; i <= days; i++) {
            LocalDate localDate = start.plusDays(i);
            var staffTime = new StaffTime();
            staffTime.setIsSelected(true);
            staffTime.setTimeRange(Collections.singletonList(timeRangeDto));
            dateLisTimetMap.put(staffId, staffTime);
            dateStaffTime.put(localDate.toString(), dateLisTimetMap);
        }
        doReturn(dateStaffTime)
                .when(staffTimeSyncService)
                .getStaffTimeMap(anySet(), anyString(), anyString(), any(), anyInt(), anyInt(), anyLong());

        doReturn(GetSmartScheduleSettingResponse.newBuilder()
                        .setSmartScheduleSetting(SmartScheduleSettingModel.newBuilder()
                                .setServiceAreaEnable(false)
                                .build())
                        .build())
                .when(smartSchedulerService)
                .getSmartSchedulerSetting(companyId);

        smartScheduleService.smartSchedule2(businessId, smartScheduleRequest);
        verify(googleMapService, times(smartScheduleRequest.getCount()))
                .calculateAvailableTime(
                        anyList(),
                        anyString(),
                        anyString(),
                        eq(smartScheduleRequest.getServiceDuration()),
                        eq(BUFFER_TIME));
    }

    @Test
    public void smartSchedulingOneDayQueryOnePerDay() {
        List<TimeSlot> freeTimeSlots = new ArrayList<>();
        TimeSlot slot1 = new TimeSlot();
        slot1.setStart(690);
        slot1.setEnd(900);
        TimeSlot slot2 = new TimeSlot();
        slot2.setStart(930);
        slot2.setEnd(1140);
        freeTimeSlots.add(slot1);
        freeTimeSlots.add(slot2);
        SmartScheduleParam request = new SmartScheduleParam();
        BeanUtils.copyProperties(smartScheduleRequest, request);
        request.setStaffSsSettingMap(Map.of());
        request.setQueryOne(true);
        request.getAmTimeSlotMap().put(smartScheduleRequest.getDate(), false);
        request.getPmTimeSlotMap().put(smartScheduleRequest.getDate(), false);

        List<TimeSlot> result = smartScheduleService.smartSchedulingOneDay(
                staffId, freeTimeSlots, request, smartScheduleRequest.getDate());
        Assertions.assertThat(result)
                .as("ss query one per day size not equals 1")
                .hasSize(1);
    }

    @Test
    public void smartSchedulingOneDayQueryPerHalfDay() {
        List<TimeSlot> freeTimeSlots = new ArrayList<>();
        TimeSlot slot1 = new TimeSlot();
        slot1.setStart(690);
        slot1.setEnd(900);
        TimeSlot slot2 = new TimeSlot();
        slot2.setStart(930);
        slot2.setEnd(1010);
        TimeSlot slot3 = new TimeSlot();
        slot3.setStart(1040);
        slot3.setEnd(1140);
        freeTimeSlots.add(slot1);
        freeTimeSlots.add(slot2);
        freeTimeSlots.add(slot3);
        SmartScheduleParam request = new SmartScheduleParam();
        BeanUtils.copyProperties(smartScheduleRequest, request);
        request.setStaffSsSettingMap(Map.of());
        request.setQueryPerHalfDay(true);
        request.getAmTimeSlotMap().put(smartScheduleRequest.getDate(), false);
        request.getPmTimeSlotMap().put(smartScheduleRequest.getDate(), false);

        List<TimeSlot> result = smartScheduleService.smartSchedulingOneDay(
                staffId, freeTimeSlots, request, smartScheduleRequest.getDate());
        Assertions.assertThat(result)
                .as("ss query one per half day size not equals 2")
                .hasSize(2);
        Assertions.assertThat(request.getAmTimeSlotMap().get(smartScheduleRequest.getDate()))
                .as("ss query per half day am time slot flag must be true")
                .isTrue();
        Assertions.assertThat(request.getPmTimeSlotMap().get(smartScheduleRequest.getDate()))
                .as("ss query per half day pm time slot flag must be true")
                .isTrue();
    }

    @Test
    public void smartSchedulingOneDayNotAvailable() {
        List<TimeSlot> freeTimeSlots = new ArrayList<>();
        TimeSlot slot1 = new TimeSlot();
        slot1.setStart(700);
        slot1.setEnd(775);
        TimeSlot slot2 = new TimeSlot();
        slot2.setStart(900);
        slot2.setEnd(975);
        freeTimeSlots.add(slot1);
        freeTimeSlots.add(slot2);
        SmartScheduleParam request = new SmartScheduleParam();
        BeanUtils.copyProperties(smartScheduleRequest, request);
        request.setStaffSsSettingMap(Map.of());
        request.setQueryPerHalfDay(true);
        request.getAmTimeSlotMap().put(smartScheduleRequest.getDate(), false);
        request.getPmTimeSlotMap().put(smartScheduleRequest.getDate(), false);

        List<TimeSlot> result = smartScheduleService.smartSchedulingOneDay(
                staffId, freeTimeSlots, request, smartScheduleRequest.getDate());
        Assertions.assertThat(result)
                .as("ss query one per half day result not is empty")
                .isEmpty();
    }

    @Test
    public void smartSchedulingOneDay() {
        List<TimeSlot> freeTimeSlots = new ArrayList<>();
        TimeSlot slot1 = new TimeSlot();
        slot1.setStart(690);
        slot1.setEnd(900);
        TimeSlot slot2 = new TimeSlot();
        slot2.setStart(930);
        slot2.setEnd(1010);
        TimeSlot slot3 = new TimeSlot();
        slot3.setStart(1040);
        slot3.setEnd(1140);
        freeTimeSlots.add(slot1);
        freeTimeSlots.add(slot2);
        freeTimeSlots.add(slot3);
        SmartScheduleParam request = new SmartScheduleParam();
        BeanUtils.copyProperties(smartScheduleRequest, request);
        request.setStaffSsSettingMap(Map.of());

        List<TimeSlot> result = smartScheduleService.smartSchedulingOneDay(
                staffId, freeTimeSlots, request, smartScheduleRequest.getDate());
        Assertions.assertThat(result).as("ss query one day size not equals 3").hasSize(freeTimeSlots.size());
    }

    @Test
    public void smartSchedulingOneDayHaveTs() {
        List<TimeSlot> freeTimeSlots = new ArrayList<>();
        TimeSlot slot1 = new TimeSlot();
        slot1.setStart(690);
        slot1.setEnd(900);
        TimeSlot slot2 = new TimeSlot();
        slot2.setStart(930);
        slot2.setEnd(1010);
        freeTimeSlots.add(slot1);
        freeTimeSlots.add(slot2);
        SmartScheduleParam request = new SmartScheduleParam();
        BeanUtils.copyProperties(smartScheduleRequest, request);
        request.setStaffSsSettingMap(Map.of());
        request.setQueryPerHalfDay(true);
        request.getAmTimeSlotMap().put(smartScheduleRequest.getDate(), true);
        request.getPmTimeSlotMap().put(smartScheduleRequest.getDate(), true);

        List<TimeSlot> result = smartScheduleService.smartSchedulingOneDay(
                staffId, freeTimeSlots, request, smartScheduleRequest.getDate());
        Assertions.assertThat(result).as("ss query one day size not is empty").isEmpty();
    }

    @Test
    public void smartScheduleFullyDayIndexNegative() {
        SmartScheduleParam requestParam = new SmartScheduleParam();
        requestParam.setBusinessId(businessId);
        BeanUtils.copyProperties(smartScheduleRequest, requestParam);
        requestParam.setQueryPerHalfDay(true);
        SmartScheduleDTO smartScheduleDTO = new SmartScheduleDTO();
        smartScheduleDTO.setRequest(requestParam);
        Map<String, SmartScheduleStaffMap> resultDayMap = new HashMap<>();

        smartScheduleService.smartScheduleFullyDay(smartScheduleDTO, -1, resultDayMap);
    }

    @Test
    public void smartScheduleFullyDayIndexOutOfBounds() {
        SmartScheduleParam requestParam = new SmartScheduleParam();
        requestParam.setBusinessId(businessId);
        BeanUtils.copyProperties(smartScheduleRequest, requestParam);
        requestParam.setQueryPerHalfDay(true);
        SmartScheduleDTO smartScheduleDTO = new SmartScheduleDTO();
        smartScheduleDTO.setRequest(requestParam);
        smartScheduleDTO.setResultArray(new SmartScheduleStaffMap[requestParam.getFarthestDay()]);
        Map<String, SmartScheduleStaffMap> resultDayMap = new HashMap<>();

        smartScheduleService.smartScheduleFullyDay(smartScheduleDTO, 365, resultDayMap);
    }
}
