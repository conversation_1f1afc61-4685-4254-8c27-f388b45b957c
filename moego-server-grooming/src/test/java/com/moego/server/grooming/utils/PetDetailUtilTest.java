package com.moego.server.grooming.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class PetDetailUtilTest {

    @Test
    void getStaffIds_withValidPetDetailsAndEvaluations_returnsDistinctStaffIds() {
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setStaffId(1);
        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setStaffId(2);
        var evaluation1 = new EvaluationServiceDetail();
        evaluation1.setStaffId(3L);
        var evaluation2 = new EvaluationServiceDetail();
        evaluation2.setStaffId(1L);

        List<Integer> result =
                PetDetailUtil.getStaffIds(List.of(petDetail1, petDetail2), List.of(evaluation1, evaluation2));

        assertThat(result).containsExactlyInAnyOrder(1, 2, 3);
    }

    @Test
    void getStaffIds_withEmptyPetDetailsAndEvaluations_returnsEmptyList() {
        List<Integer> result = PetDetailUtil.getStaffIds(List.of(), List.of());

        assertThat(result).isEmpty();
    }

    @Test
    void getStaffIds_withNullStaffIds_returnsEmptyList() {
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setStaffId(null);
        var evaluation = new EvaluationServiceDetail();
        evaluation.setStaffId(null);

        List<Integer> result = PetDetailUtil.getStaffIds(List.of(petDetail), List.of(evaluation));

        assertThat(result).isEmpty();
    }

    @Test
    void getPetIds_withValidPetDetailsAndEvaluations_returnsDistinctPetIds() {
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setPetId(1);
        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setPetId(2);
        var evaluation1 = new EvaluationServiceDetail();
        evaluation1.setPetId(3L);
        var evaluation2 = new EvaluationServiceDetail();
        evaluation2.setPetId(1L);

        List<Integer> result =
                PetDetailUtil.getPetIds(List.of(petDetail1, petDetail2), List.of(evaluation1, evaluation2));

        assertThat(result).containsExactlyInAnyOrder(1, 2, 3);
    }

    @Test
    void getPetIds_withEmptyPetDetailsAndEvaluations_returnsEmptyList() {
        List<Integer> result = PetDetailUtil.getPetIds(List.of(), List.of());

        assertThat(result).isEmpty();
    }

    @Test
    void getPetIds_withNullPetIds_returnsEmptyList() {
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setPetId(null);
        var evaluation = new EvaluationServiceDetail();
        evaluation.setPetId(null);

        List<Integer> result = PetDetailUtil.getPetIds(List.of(petDetail), List.of(evaluation));

        assertThat(result).isEmpty();
    }

    @Test
    void getServiceIds_withValidPetDetails_returnsDistinctServiceIds() {
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setServiceId(1);
        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setServiceId(2);
        var petDetail3 = new MoeGroomingPetDetail();
        petDetail3.setServiceId(1);

        List<Integer> result = PetDetailUtil.getServiceIds(List.of(petDetail1, petDetail2, petDetail3));

        assertThat(result).containsExactlyInAnyOrder(1, 2);
    }

    @Test
    void getServiceIds_withEmptyPetDetails_returnsEmptyList() {
        List<Integer> result = PetDetailUtil.getServiceIds(List.of());

        assertThat(result).isEmpty();
    }

    @Test
    void getServiceIds_withNullServiceIds_returnsEmptyList() {
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setServiceId(null);

        List<Integer> result = PetDetailUtil.getServiceIds(List.of(petDetail));

        assertThat(result).isEmpty();
    }

    @Test
    void getLodgingIds_withValidPetDetails_returnsDistinctLodgingIds() {
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setLodgingId(1L);
        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setLodgingId(2L);
        var petDetail3 = new MoeGroomingPetDetail();
        petDetail3.setLodgingId(1L);

        List<Long> result = PetDetailUtil.getLodgingIds(List.of(petDetail1, petDetail2, petDetail3));

        assertThat(result).containsExactlyInAnyOrder(1L, 2L);
    }

    @Test
    void getLodgingIds_withEmptyPetDetails_returnsEmptyList() {
        List<Long> result = PetDetailUtil.getLodgingIds(List.of());

        assertThat(result).isEmpty();
    }

    @Test
    void getLodgingIds_withNullLodgingIds_returnsEmptyList() {
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setLodgingId(null);

        List<Long> result = PetDetailUtil.getLodgingIds(List.of(petDetail));

        assertThat(result).isEmpty();
    }

    @Test
    void getDays_withBoardingService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding));

        // Act
        var result = PetDetailUtil.getDays(boarding, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getDays_withDaycareService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(1);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartDate("2024-10-15");
        daycare.setStartTime(600L);
        daycare.setEndDate("2024-10-15");
        daycare.setEndTime(600L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, daycare));

        // Act
        var result = PetDetailUtil.getDays(daycare, petServiceMap);
        var expected = List.of("2024-10-15");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getDays_withBoardingDaycareService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(2);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartDate("2024-10-15");
        daycare.setStartTime(720L);
        daycare.setEndDate("2024-10-15");
        daycare.setEndTime(720L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding, 2, daycare));

        // Act
        var result = PetDetailUtil.getDays(daycare, petServiceMap);
        var expected = List.of("2024-10-15");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getDays_withBoardingMultiDaycareService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(2);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setSpecificDates("[\"2024-10-15\", \"2024-10-17\"]");
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding, 2, daycare));

        // Act
        var result = PetDetailUtil.getDays(daycare, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getDays_withBoardingEverydayDaycareService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(2);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding, 2, daycare));

        // Act
        var result = PetDetailUtil.getDays(daycare, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-16", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getDays_withBoardingAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        MoeGroomingPetDetail addOn = new MoeGroomingPetDetail();
        addOn.setPetId(1);
        addOn.setServiceType(ServiceType.ADDON_VALUE);
        addOn.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        addOn.setStartDate("2024-10-15");
        addOn.setStartTime(720L);
        addOn.setEndDate("2024-10-15");
        addOn.setEndTime(720L);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding));

        // Act
        var result = PetDetailUtil.getDays(addOn, petServiceMap);
        var expected = List.of("2024-10-15");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getDays_withBoardingMultiAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        MoeGroomingPetDetail addon = new MoeGroomingPetDetail();
        addon.setPetId(1);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        addon.setSpecificDates("[\"2024-10-15\", \"2024-10-17\"]");
        addon.setQuantityPerDay(1);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding));

        // Act
        var result = PetDetailUtil.getDays(addon, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getDays_withBoardingEverydayAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setId(1);
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        MoeGroomingPetDetail addon = new MoeGroomingPetDetail();
        addon.setId(2);
        addon.setPetId(1);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setQuantityPerDay(1);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding));

        // Act
        var result = PetDetailUtil.getDays(addon, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-16", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getDays_withDaycareEverydayAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setId(1);
        daycare.setPetId(1);
        daycare.setServiceId(1);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartDate("2024-10-15");
        daycare.setStartTime(600L);
        daycare.setEndDate("2024-10-15");
        daycare.setEndTime(600L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);

        MoeGroomingPetDetail addon = new MoeGroomingPetDetail();
        addon.setId(2);
        addon.setPetId(1);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setQuantityPerDay(1);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, daycare));

        // Act
        var result = PetDetailUtil.getDays(addon, petServiceMap);
        var expected = List.of("2024-10-15");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getDays_withBoardingMultiDayDaycareEverydayAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(2);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setSpecificDates("[\"2024-10-15\", \"2024-10-17\"]");
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);

        MoeGroomingPetDetail addon = new MoeGroomingPetDetail();
        addon.setPetId(1);
        addon.setAssociatedServiceId(2L);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        addon.setQuantityPerDay(1);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding, 2, daycare));

        // Act
        var result = PetDetailUtil.getDays(addon, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withBoardingService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding));

        // Act
        var result = PetDetailUtil.getServiceDates(boarding, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-16", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withPerDayBoardingService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding));

        // Act
        var result = PetDetailUtil.getServiceDates(boarding, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withDaycareService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(1);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartDate("2024-10-15");
        daycare.setStartTime(600L);
        daycare.setEndDate("2024-10-15");
        daycare.setEndTime(600L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, daycare));

        // Act
        var result = PetDetailUtil.getServiceDates(daycare, petServiceMap);
        var expected = List.of("2024-10-15");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withBoardingDaycareService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(2);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartDate("2024-10-15");
        daycare.setStartTime(720L);
        daycare.setEndDate("2024-10-15");
        daycare.setEndTime(720L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding, 2, daycare));

        // Act
        var result = PetDetailUtil.getServiceDates(daycare, petServiceMap);
        var expected = List.of("2024-10-15");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withBoardingMultiDaycareService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);

        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(2);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setSpecificDates("[\"2024-10-15\", \"2024-10-17\"]");
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding, 2, daycare));

        // Act
        var result = PetDetailUtil.getServiceDates(daycare, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withBoardingEverydayDaycareService_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(2);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding, 2, daycare));

        // Act
        var result = PetDetailUtil.getServiceDates(daycare, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-16", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withBoardingAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        MoeGroomingPetDetail addOn = new MoeGroomingPetDetail();
        addOn.setPetId(1);
        addOn.setServiceType(ServiceType.ADDON_VALUE);
        addOn.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        addOn.setStartDate("2024-10-15");
        addOn.setStartTime(720L);
        addOn.setEndDate("2024-10-15");
        addOn.setEndTime(720L);
        addOn.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding));

        // Act
        var result = PetDetailUtil.getServiceDates(addOn, petServiceMap);
        var expected = List.of("2024-10-15");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withBoardingMultiAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        MoeGroomingPetDetail addon = new MoeGroomingPetDetail();
        addon.setPetId(1);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        addon.setSpecificDates("[\"2024-10-15\", \"2024-10-17\"]");
        addon.setQuantityPerDay(1);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding));

        // Act
        var result = PetDetailUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withBoardingEverydayAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setId(1);
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        MoeGroomingPetDetail addon = new MoeGroomingPetDetail();
        addon.setId(2);
        addon.setPetId(1);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding));

        // Act
        var result = PetDetailUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-16", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withDaycareEverydayAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setId(1);
        daycare.setPetId(1);
        daycare.setServiceId(1);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setStartDate("2024-10-15");
        daycare.setStartTime(600L);
        daycare.setEndDate("2024-10-15");
        daycare.setEndTime(600L);
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        MoeGroomingPetDetail addon = new MoeGroomingPetDetail();
        addon.setId(2);
        addon.setPetId(1);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, daycare));

        // Act
        var result = PetDetailUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-10-15");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceDates_withBoardingMultiDayDaycareEverydayAddOn_returnsCorrectDays() {
        // Arrange
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setPetId(1);
        boarding.setServiceId(1);
        boarding.setServiceType(ServiceType.SERVICE_VALUE);
        boarding.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        boarding.setStartDate("2024-10-15");
        boarding.setStartTime(600L);
        boarding.setEndDate("2024-10-18");
        boarding.setEndTime(600L);
        boarding.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boarding.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setPetId(1);
        daycare.setServiceId(2);
        daycare.setServiceType(ServiceType.SERVICE_VALUE);
        daycare.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        daycare.setSpecificDates("[\"2024-10-15\", \"2024-10-17\"]");
        daycare.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        daycare.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);

        MoeGroomingPetDetail addon = new MoeGroomingPetDetail();
        addon.setPetId(1);
        addon.setAssociatedServiceId(2L);
        addon.setServiceType(ServiceType.ADDON_VALUE);
        addon.setServiceItemType(ServiceItemType.DAYCARE_VALUE);
        addon.setQuantityPerDay(1);
        addon.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(1, Map.of(1, boarding, 2, daycare));

        // Act
        var result = PetDetailUtil.getServiceDates(addon, petServiceMap);
        var expected = List.of("2024-10-15", "2024-10-17");

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getServiceActualDates_withAssociatedService_returnsDatesFromAssociatedService() {
        // 创建主服务（boarding）
        var mainService = new MoeGroomingPetDetail();
        mainService.setServiceId(1);
        mainService.setServiceType(ServiceType.SERVICE_VALUE);
        mainService.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        mainService.setStartDate("2024-01-01");
        mainService.setEndDate("2024-01-03");

        // 创建附加服务（add-on）
        var addOnService = new MoeGroomingPetDetail();
        addOnService.setPetId(100);
        addOnService.setServiceId(2);
        addOnService.setServiceType(ServiceType.ADDON_VALUE);
        addOnService.setAssociatedServiceId(1L);
        addOnService.setStartDate("2024-01-02");
        addOnService.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        // 创建petServiceMap
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(100, Map.of(1, mainService));

        // 调用方法
        List<String> result = PetDetailUtil.getServiceActualDates(addOnService, petServiceMap);

        // 验证结果 - 应该返回add-on的开始日期
        assertThat(result).containsExactly("2024-01-02");
    }

    @Test
    void getServiceActualDates_withoutAssociatedService_returnsStartDate() {
        // 创建没有关联服务的petDetail
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setPetId(100);
        petDetail.setServiceId(1);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail.setServiceItemType(ServiceItemType.GROOMING_VALUE);
        petDetail.setStartDate("2024-01-01");
        petDetail.setEndDate("2024-01-01");
        petDetail.setStartTime(600L); // 设置开始时间，使其成为datePoint类型

        // 创建包含该petId但没有关联服务的petServiceMap
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(100, Map.of());

        // 调用方法
        List<String> result = PetDetailUtil.getServiceActualDates(petDetail, petServiceMap);

        // 验证结果 - 应该返回petDetail的开始日期
        assertThat(result).containsExactly("2024-01-01");
    }

    @Test
    void getServiceActualDates_withAssociatedServiceEverydayDateType_returnsDateRange() {
        // 创建主服务（boarding）
        var mainService = new MoeGroomingPetDetail();
        mainService.setServiceId(1);
        mainService.setServiceType(ServiceType.SERVICE_VALUE);
        mainService.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        mainService.setStartDate("2024-01-01");
        mainService.setEndDate("2024-01-03");

        // 创建附加服务，使用EVERYDAY日期类型
        var addOnService = new MoeGroomingPetDetail();
        addOnService.setPetId(100);
        addOnService.setServiceId(2);
        addOnService.setServiceType(ServiceType.ADDON_VALUE);
        addOnService.setAssociatedServiceId(1L);
        addOnService.setStartDate("2024-01-01");
        addOnService.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        // 创建petServiceMap
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(100, Map.of(1, mainService));

        // 调用方法
        List<String> result = PetDetailUtil.getServiceActualDates(addOnService, petServiceMap);

        // 验证结果 - 应该返回从开始到结束（不包括checkout day）的所有日期
        assertThat(result).containsExactly("2024-01-01", "2024-01-02");
    }

    @Test
    void getServiceActualDates_withAssociatedServiceSpecificDates_returnsSpecificDates() {
        // 创建主服务（boarding）
        var mainService = new MoeGroomingPetDetail();
        mainService.setServiceId(1);
        mainService.setServiceType(ServiceType.SERVICE_VALUE);
        mainService.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        mainService.setStartDate("2024-01-01");
        mainService.setEndDate("2024-01-05");

        // 创建附加服务，使用SPECIFIC_DATE日期类型
        var addOnService = new MoeGroomingPetDetail();
        addOnService.setPetId(100);
        addOnService.setServiceId(2);
        addOnService.setServiceType(ServiceType.ADDON_VALUE);
        addOnService.setAssociatedServiceId(1L);
        addOnService.setStartDate("2024-01-01");
        addOnService.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);
        addOnService.setSpecificDates("[\"2024-01-02\", \"2024-01-04\"]");

        // 创建petServiceMap
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(100, Map.of(1, mainService));

        // 调用方法
        List<String> result = PetDetailUtil.getServiceActualDates(addOnService, petServiceMap);

        // 验证结果 - 应该返回指定的具体日期
        assertThat(result).containsExactly("2024-01-02", "2024-01-04");
    }

    @Test
    void getServiceActualDates_withEmptyPetServiceMap_returnsStartDate() {
        // 创建petDetail
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setPetId(100);
        petDetail.setServiceId(1);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail.setServiceItemType(ServiceItemType.GROOMING_VALUE);
        petDetail.setStartDate("2024-01-01");
        petDetail.setEndDate("2024-01-01");
        petDetail.setStartTime(600L); // 设置开始时间，使其成为datePoint类型

        // 创建包含petId但为空的服务map
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(100, Map.of());

        // 调用方法
        List<String> result = PetDetailUtil.getServiceActualDates(petDetail, petServiceMap);

        // 验证结果 - 应该返回petDetail的开始日期
        assertThat(result).containsExactly("2024-01-01");
    }

    @Test
    void getServiceActualDates_withAssociatedServiceEverydayIncludeCheckoutDay_returnsFullDateRange() {
        // 创建主服务（boarding）
        var mainService = new MoeGroomingPetDetail();
        mainService.setServiceId(1);
        mainService.setServiceType(ServiceType.SERVICE_VALUE);
        mainService.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        mainService.setStartDate("2024-01-01");
        mainService.setEndDate("2024-01-03");

        // 创建附加服务，使用EVERYDAY_INCLUDE_CHECKOUT_DAY日期类型
        var addOnService = new MoeGroomingPetDetail();
        addOnService.setPetId(100);
        addOnService.setServiceId(2);
        addOnService.setServiceType(ServiceType.ADDON_VALUE);
        addOnService.setAssociatedServiceId(1L);
        addOnService.setStartDate("2024-01-01");
        addOnService.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE);

        // 创建petServiceMap
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(100, Map.of(1, mainService));

        // 调用方法
        List<String> result = PetDetailUtil.getServiceActualDates(addOnService, petServiceMap);

        // 验证结果 - 应该返回包括checkout day的所有日期
        assertThat(result).containsExactly("2024-01-01", "2024-01-02", "2024-01-03");
    }

    @Test
    void getServiceActualDates_withAssociatedServiceEverydayExceptCheckinDay_returnsDateRangeExceptFirst() {
        // 创建主服务（boarding）
        var mainService = new MoeGroomingPetDetail();
        mainService.setServiceId(1);
        mainService.setServiceType(ServiceType.SERVICE_VALUE);
        mainService.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        mainService.setStartDate("2024-01-01");
        mainService.setEndDate("2024-01-04");

        // 创建附加服务，使用EVERYDAY_EXCEPT_CHECKIN_DAY日期类型
        var addOnService = new MoeGroomingPetDetail();
        addOnService.setPetId(100);
        addOnService.setServiceId(2);
        addOnService.setServiceType(ServiceType.ADDON_VALUE);
        addOnService.setAssociatedServiceId(1L);
        addOnService.setStartDate("2024-01-01");
        addOnService.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY_VALUE);

        // 创建petServiceMap
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(100, Map.of(1, mainService));

        // 调用方法
        List<String> result = PetDetailUtil.getServiceActualDates(addOnService, petServiceMap);

        // 验证结果 - 应该返回除了checkin day之外的所有日期（从第二天到最后一天）
        assertThat(result).containsExactly("2024-01-02", "2024-01-03", "2024-01-04");
    }

    @Test
    void getServiceActualDates_withNullAssociatedServiceId_returnsStartDate() {
        // 创建主服务
        var mainService = new MoeGroomingPetDetail();
        mainService.setServiceId(1);
        mainService.setServiceType(ServiceType.SERVICE_VALUE);
        mainService.setServiceItemType(ServiceItemType.BOARDING_VALUE);
        mainService.setStartDate("2024-01-01");
        mainService.setEndDate("2024-01-03");

        // 创建附加服务，但没有设置associatedServiceId
        var addOnService = new MoeGroomingPetDetail();
        addOnService.setPetId(100);
        addOnService.setServiceId(2);
        addOnService.setServiceType(ServiceType.ADDON_VALUE);
        addOnService.setAssociatedServiceId(null); // 明确设置为null
        addOnService.setStartDate("2024-01-02");
        addOnService.setStartTime(600L); // 设置开始时间，使其成为datePoint类型
        addOnService.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        // 创建petServiceMap
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = Map.of(100, Map.of(1, mainService));

        // 调用方法
        List<String> result = PetDetailUtil.getServiceActualDates(addOnService, petServiceMap);

        // 验证结果 - 由于associatedServiceId为null，但有主服务，应该返回add-on与主服务的关联日期
        assertThat(result).containsExactly("2024-01-02");
    }
}
