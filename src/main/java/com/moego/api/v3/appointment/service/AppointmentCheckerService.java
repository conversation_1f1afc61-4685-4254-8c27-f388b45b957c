package com.moego.api.v3.appointment.service;

import com.moego.api.v3.appointment.utils.LodgingUtil;
import com.moego.api.v3.appointment.utils.PetDetailUtil;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.organization.v1.CloseDateDef;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.GetTimeOverlapAppointmentListRequest;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentCheckerService {

    private final LodgingUtil lodgingUtil;
    private final PetDetailUtil petDetailUtil;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentService;
    private final com.moego.idl.service.organization.v1.BusinessServiceGrpc.BusinessServiceBlockingStub businessService;
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailService;
    private final LodgingUsingService lodgingUsingService;

    public Map<Long, LodgingOccupiedStatus> getLodgingOccupiedStatusMap(
            Long companyId,
            Long businessId,
            String startDateStr,
            String endDateStr,
            List<LodgingTypeModel> lodgingTypeList,
            List<LodgingUnitModel> lodgingUnitList) {
        List<LodgingAssignInfo> assignInfoList =
                lodgingUtil.getLodgingAssignInfo(companyId, businessId, startDateStr, endDateStr);
        var excludeBoardingLastDay = lodgingUsingService.excludeBoardingLastDayForScheduling(companyId);
        var petCntPerDayPerLodging =
                LodgingUtil.calPetCntPerDayPerLodging(startDateStr, endDateStr, assignInfoList, excludeBoardingLastDay);
        return LodgingUtil.calLodgingStatusForScheduling(lodgingTypeList, lodgingUnitList, petCntPerDayPerLodging);
    }

    public List<LodgingUnitModel> getLodgingUnitList(Long companyId, Long businessId, List<Long> lodgingUnitIds) {
        return lodgingUtil.getLodgingUnitByUnitIds(companyId, businessId, lodgingUnitIds);
    }

    public List<LodgingTypeModel> getLodgingTypeList(List<LodgingUnitModel> lodgingUnitList) {
        return lodgingUtil.getLodgingTypeByIds(lodgingUnitList.stream()
                .map(LodgingUnitModel::getLodgingTypeId)
                .distinct()
                .toList());
    }

    public Map<Long, List<AppointmentModel>> getTimeOverlapAppointmentListMap(
            Long companyId,
            List<Long> customerIds,
            GetTimeOverlapAppointmentListRequest.Filter filter,
            List<Long> petIds,
            Long appointmentId) {
        if (ObjectUtils.isEmpty(companyId) || CollectionUtils.isEmpty(customerIds)) {
            return Map.of();
        }
        var response =
                appointmentService.getTimeOverlapAppointmentList(GetTimeOverlapAppointmentListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllCustomerId(customerIds)
                        .setFilter(filter)
                        .addAllPetIds(petIds)
                        .build());

        return response.getAppointmentListMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getAppointmentsList().stream()
                        .filter(appointment -> !Objects.equals(appointment.getId(), appointmentId))
                        .toList()));
    }

    public List<CloseDateDef> getClosedDateList(Long tokenCompanyId, long businessId) {
        var response = businessService.getClosedDateList(
                com.moego.idl.service.organization.v1.GetClosedDateListRequest.newBuilder()
                        .setTokenCompanyId(tokenCompanyId)
                        .setBusinessId(businessId)
                        .build());
        return response.getClosedDateList();
    }

    public GetPetDetailListResponse getPetDetailList(long companyId, Collection<Long> appointmentIdList) {
        return petDetailService.getPetDetailList(GetPetDetailListRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllAppointmentIds(appointmentIdList)
                .build());
    }

    public Map<Long, BusinessCustomerPetModel> getPetMap(
            long companyId, GetPetDetailListResponse petDetailListResponse) {
        return petDetailUtil.getPetMap(
                petDetailListResponse.getPetDetailsList(), petDetailListResponse.getPetEvaluationsList(), companyId);
    }

    public Map<Long, ServiceBriefView> getServiceMap(long companyId, GetPetDetailListResponse petDetailListResponse) {
        return petDetailUtil.getServiceMap(petDetailListResponse.getPetDetailsList(), companyId);
    }

    @Data
    public static class DateRange {
        LocalDate startDate;
        LocalDate endDate;

        public DateRange(LocalDate startDate, LocalDate endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }
    }

    public DateRange getNonOverlapRanges(
            LocalDate startDate, LocalDate endDate, LocalDate appointmentStart, LocalDate appointmentEnd) {

        // 如果待比较区间完全在预约前或后，直接返回整个区间
        if (endDate.isBefore(appointmentStart) || startDate.isAfter(appointmentEnd)) {
            return new DateRange(startDate, endDate);
        }

        // 前段非交集（如果存在）
        if (startDate.isBefore(appointmentStart)) {
            return new DateRange(startDate, appointmentStart.minusDays(1));
        }

        // 后段非交集（如果存在）
        if (endDate.isAfter(appointmentEnd)) {
            return new DateRange(appointmentEnd.plusDays(1), endDate);
        }

        return new DateRange(startDate, endDate);
    }

    public List<LodgingUnitModel> filterOverCapacityUnits(
            List<LodgingUnitModel> units,
            Map<Long, LodgingOccupiedStatus> statusMap,
            Map<Long, LodgingTypeModel> typeMap) {

        return units.stream()
                .filter(unit -> {
                    LodgingOccupiedStatus status = statusMap.get(unit.getId());
                    if (status == null) return false;

                    LodgingTypeModel type = typeMap.get(unit.getLodgingTypeId());
                    if (type == null) return false;

                    return status == LodgingOccupiedStatus.FULLY_OCCUPIED
                            || (type.getLodgingUnitType() == LodgingUnitType.ROOM
                                    && status == LodgingOccupiedStatus.PARTIALLY_OCCUPIED);
                })
                .collect(Collectors.toList());
    }
}
