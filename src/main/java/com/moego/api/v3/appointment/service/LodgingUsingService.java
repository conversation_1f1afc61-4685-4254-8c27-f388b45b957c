package com.moego.api.v3.appointment.service;

import static com.moego.lib.featureflag.features.FeatureFlags.ENABLE_NEW_LODGING_OCCUPANCY;

import com.moego.api.v3.shared.helper.CompanyHelper;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.service.appointment.v1.LodgingInUseCheckRequest;
import com.moego.idl.service.appointment.v1.LodgingServiceGrpc;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class LodgingUsingService {

    private final LodgingServiceGrpc.LodgingServiceBlockingStub lodgingClient;

    private final CompanyHelper companyHelper;

    private final FeatureFlagApi featureFlagApi;

    /**
     * 支持查询 company 下多个 business 的 lodging 使用情况
     */
    public Map<Long, List<Long>> getUpcomingAppointments(
            Long companyId, @Nullable Long businessId, List<Long> lodgingIds) {
        if (CollectionUtils.isEmpty(lodgingIds)) {
            return Map.of();
        }
        LodgingInUseCheckRequest.Builder request = LodgingInUseCheckRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllLodgingIds(lodgingIds.stream().distinct().toList());
        if (businessId != null) {
            request.setBusinessId(businessId);
        }
        return lodgingClient.lodgingInUseCheck(request.build()).getLodgingUpcomingAppointmentsMap().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, entry -> entry.getValue().getValuesList()));
    }

    public Integer getUpcomingAppointmentCnt(Long companyId, Long businessId, Long lodgingId) {
        return getUpcomingAppointments(companyId, businessId, List.of(lodgingId))
                .getOrDefault(lodgingId, List.of())
                .size();
    }

    // 用于展示方面的 excludeBoardingLastDay（lodging calendar OR print card）
    public boolean excludeBoardingLastDayForShow() {
        // 在展示上都 exclude last day
        return true;
    }

    // 用于 scheduling 方面的 excludeBoardingLastDay (get lodging availability OR lodging assign ...)
    public boolean excludeBoardingLastDayForScheduling(Long companyId) {
        return enableNewLodgingOccupancy(companyId);
    }

    // 根据 feature flag 判断是否启用新的 lodging occupancy 逻辑
    public boolean enableNewLodgingOccupancy(Long companyId) {
        FeatureFlagContext.FeatureFlagContextBuilder builder = FeatureFlagContext.builder();
        builder.company(companyId);
        var company = companyHelper.mustGetCompany(companyId);
        if (CommonUtil.isNormal(company.getEnterpriseId())) {
            builder.enterprise(company.getEnterpriseId());
        }
        return featureFlagApi.isOn(ENABLE_NEW_LODGING_OCCUPANCY, builder.build());
    }
}
