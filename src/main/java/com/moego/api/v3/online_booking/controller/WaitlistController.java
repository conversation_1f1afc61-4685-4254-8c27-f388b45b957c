package com.moego.api.v3.online_booking.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.api.v3.membership.service.MembershipService;
import com.moego.api.v3.online_booking.converter.WaitlistConverter;
import com.moego.api.v3.online_booking.service.BusinessCustomerService;
import com.moego.api.v3.organization.service.StaffService;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.api.online_booking.v1.BookWaitlistParams;
import com.moego.idl.api.online_booking.v1.BookWaitlistResult;
import com.moego.idl.api.online_booking.v1.CreateWaitlistParams;
import com.moego.idl.api.online_booking.v1.CreateWaitlistResult;
import com.moego.idl.api.online_booking.v1.DeleteWaitlistParams;
import com.moego.idl.api.online_booking.v1.DeleteWaitlistResult;
import com.moego.idl.api.online_booking.v1.GetWaitlistParams;
import com.moego.idl.api.online_booking.v1.GetWaitlistResult;
import com.moego.idl.api.online_booking.v1.ListWaitlistParams;
import com.moego.idl.api.online_booking.v1.ListWaitlistResult;
import com.moego.idl.api.online_booking.v1.UpdateWaitlistParams;
import com.moego.idl.api.online_booking.v1.UpdateWaitlistResult;
import com.moego.idl.api.online_booking.v1.WaitlistServiceGrpc;
import com.moego.idl.api.online_booking.v1.WaitlistView;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipSubscriptionListModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.CreateAppointmentRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.ListWaitlistsRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestStatusRequest;
import com.moego.idl.service.online_booking.v1.WaitlistExtra;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.params.SearchCustomerIdsParam;
import com.moego.server.grooming.api.IBookOnlineDepositService;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class WaitlistController extends WaitlistServiceGrpc.WaitlistServiceImplBase {

    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestService;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceBlockingStub;
    private final ICustomerCustomerClient customerClient;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub customerServiceBlockingStub;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;
    private final StaffService staffService;
    private final BusinessCustomerService businessCustomerService;
    private final MembershipService membershipService;
    private final IBookOnlineDepositService iBookOnlineDepositService;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    @Override
    @Auth(AuthType.COMPANY)
    public void createWaitlist(CreateWaitlistParams request, StreamObserver<CreateWaitlistResult> responseObserver) {
        var serviceRequest =
                WaitlistConverter.convertToRequest(AuthContext.get().companyId(), request);
        var waitlistId = bookingRequestService.createBookingRequest(serviceRequest.toBuilder()
                .setAttr(BookingRequestModel.Attr.newBuilder()
                        .setCreatedByStaffId(AuthContext.get().staffId())
                        .build())
                .build());
        responseObserver.onNext(CreateWaitlistResult.newBuilder()
                .setWaitlistId(waitlistId.getValue())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateWaitlist(UpdateWaitlistParams request, StreamObserver<UpdateWaitlistResult> responseObserver) {
        var existWaitlist =
                checkWaitlistValid(request.getWaitlistId(), AuthContext.get().companyId());
        bookingRequestService.replaceBookingRequest(WaitlistConverter.convertToRequest(request, existWaitlist));
        responseObserver.onNext(UpdateWaitlistResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteWaitlist(DeleteWaitlistParams request, StreamObserver<DeleteWaitlistResult> responseObserver) {
        checkWaitlistValid(request.getWaitlistId(), AuthContext.get().companyId());
        bookingRequestService.updateBookingRequestStatus(UpdateBookingRequestStatusRequest.newBuilder()
                .setStatus(BookingRequestStatus.DELETED)
                .setId(request.getWaitlistId())
                .build());
        responseObserver.onNext(DeleteWaitlistResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private BookingRequestModel checkWaitlistValid(long waitlistId, long companyId) {
        var bookingRequestResponse = bookingRequestService.getBookingRequest(
                GetBookingRequestRequest.newBuilder().setId(waitlistId).build());
        if (!bookingRequestResponse.hasBookingRequest()
                || bookingRequestResponse.getBookingRequest().getCompanyId() != companyId) {
            throw bizException(Code.CODE_PARAMS_ERROR, "waitlist error");
        }
        return bookingRequestResponse.getBookingRequest();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getWaitlist(GetWaitlistParams request, StreamObserver<GetWaitlistResult> responseObserver) {
        var bookingRequestResponse = bookingRequestService.getBookingRequest(GetBookingRequestRequest.newBuilder()
                .setId(request.getWaitlistId())
                .addAssociatedModels(BookingRequestAssociatedModel.SERVICE)
                .addAssociatedModels(BookingRequestAssociatedModel.ADD_ON)
                .addAssociatedModels(BookingRequestAssociatedModel.FEEDING)
                .addAssociatedModels(BookingRequestAssociatedModel.MEDICATION)
                .build());
        if (!bookingRequestResponse.hasBookingRequest()
                || bookingRequestResponse.getBookingRequest().getCompanyId()
                        != AuthContext.get().companyId()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "waitlist error");
        }

        var singleList = fetchViewData(
                AuthContext.get().companyId(),
                Collections.singletonList(bookingRequestResponse.getBookingRequest()),
                Collections.singletonList(bookingRequestResponse.getWaitlistExtras()));

        responseObserver.onNext(
                GetWaitlistResult.newBuilder().setWaitlist(singleList.get(0)).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listWaitlist(ListWaitlistParams request, StreamObserver<ListWaitlistResult> responseObserver) {
        var companyId = AuthContext.get().companyId();

        var serviceRequest = buildServiceFilter(request);
        // 关键词搜索
        if (request.hasFilters()
                && request.getFilters().hasKeyword()
                && StringUtils.hasText(request.getFilters().getKeyword())) {
            var customerIds = getCustomerIdsByKeyword(
                    request.getBusinessId(), request.getFilters().getKeyword());
            if (!CollectionUtils.isEmpty(customerIds)) {
                serviceRequest =
                        serviceRequest.toBuilder().addAllCustomerId(customerIds).build();
            }
        }
        if (request.hasOrderPriceDesc() && request.getOrderPriceDesc()) {
            serviceRequest = serviceRequest.toBuilder().setOrderPriceDesc(true).build();
        }

        var response = bookingRequestService.listWaitlists(serviceRequest.toBuilder()
                .addAssociatedModels(BookingRequestAssociatedModel.SERVICE)
                .addAssociatedModels(BookingRequestAssociatedModel.ADD_ON)
                .addAssociatedModels(BookingRequestAssociatedModel.FEEDING)
                .addAssociatedModels(BookingRequestAssociatedModel.MEDICATION)
                .setCompanyId(companyId)
                .setPagination(request.getPagination())
                .addAllOrderBys(request.getOrderBysList())
                .build());
        responseObserver.onNext(ListWaitlistResult.newBuilder()
                .addAllWaitlists(
                        fetchViewData(companyId, response.getBookingRequestsList(), response.getWaitlistExtrasList()))
                .setPagination(response.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    private Set<Long> filterNormalLong(Set<Long> idList) {
        return idList.stream().filter(CommonUtil::isNormal).collect(Collectors.toSet());
    }

    private List<WaitlistView> fetchViewData(
            long companyId, List<BookingRequestModel> bookingRequests, List<WaitlistExtra> extraList) {
        if (CollectionUtils.isEmpty(bookingRequests)) {
            return List.of();
        }
        long businessId = bookingRequests.get(0).getBusinessId();
        Set<Long> obClientIds = new HashSet<>();
        Set<Long> clientIds = new HashSet<>();
        Set<Long> petIds = new HashSet<>();
        Set<Long> serviceIds = new HashSet<>();
        Set<Long> createByStaffs = new HashSet<>();

        // key bookingRequestId
        // value      key petId, value serviceId
        Map<Long, Map<Long, Set<Long>>> petIdServiceIdListMapByBookingRequestId = new HashMap<>();
        for (BookingRequestModel bookingRequest : bookingRequests) {
            // client
            clientIds.add(bookingRequest.getCustomerId());
            if (bookingRequest.getSource() == BookingRequestModel.Source.OB) {
                obClientIds.add(bookingRequest.getCustomerId());
            }
            // pet service
            for (BookingRequestModel.Service service : bookingRequest.getServicesList()) {
                Long petId = null;
                Long serviceId = null;
                if (service.hasDaycare()) {
                    petId = service.getDaycare().getService().getPetId();
                    serviceId = service.getDaycare().getService().getServiceId();
                }
                if (service.hasBoarding()) {
                    petId = service.getBoarding().getService().getPetId();
                    serviceId = service.getBoarding().getService().getServiceId();
                }
                if (petId != null) {
                    petIds.add(petId);
                    serviceIds.add(serviceId);
                    petIdServiceIdListMapByBookingRequestId
                            .computeIfAbsent(bookingRequest.getId(), k -> new HashMap<>())
                            .computeIfAbsent(petId, k -> new HashSet<>())
                            .add(serviceId);
                }
            }
            // staff
            if (bookingRequest.hasAttr() && bookingRequest.getAttr().hasCreatedByStaffId()) {
                createByStaffs.add(bookingRequest.getAttr().getCreatedByStaffId());
            }
        }

        clientIds = filterNormalLong(clientIds);
        petIds = filterNormalLong(petIds);
        serviceIds = filterNormalLong(serviceIds);
        createByStaffs = filterNormalLong(createByStaffs);
        if (CollectionUtils.isEmpty(clientIds)
                || CollectionUtils.isEmpty(petIds)
                || CollectionUtils.isEmpty(serviceIds)) {
            return List.of();
        }

        // query client
        var clientsResponse = customerServiceBlockingStub.batchGetCustomerInfo(BatchGetCustomerInfoRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .addAllIds(clientIds)
                .build());

        // query has request update
        var clientListMap =
                businessCustomerService.listCustomerHasRequestUpdate(Math.toIntExact(businessId), obClientIds);

        // query pet
        var petsResponse = businessCustomerPetService.batchGetPetInfo(
                BatchGetPetInfoRequest.newBuilder().addAllIds(petIds).build());

        // query service
        var serviceResponse = serviceManagementService.getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllServiceIds(serviceIds)
                .build());
        // query order

        var membershipMap =
                membershipService.getAllSubscriptions(clientIds.stream().toList());

        Map<Long, StaffModel> staffMap = Map.of();
        if (!CollectionUtils.isEmpty(createByStaffs)) {
            staffMap = staffService.getStaffMap(
                    createByStaffs.stream().filter(staffId -> staffId != 0).toList());
        }

        Map<Long, BusinessCustomerInfoModel> customerInfoModelMap = clientsResponse.getCustomersList().stream()
                .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, customerInfoModel -> customerInfoModel));
        Map<Long, BusinessCustomerPetInfoModel> petInfoModelMap = petsResponse.getPetsList().stream()
                .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, petInfoModel -> petInfoModel));
        Map<Long, ServiceBriefView> serviceInfoMap = serviceResponse.getServicesList().stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, serviceInfo -> serviceInfo));
        var extraMap = extraList.stream().collect(Collectors.toMap(WaitlistExtra::getId, Function.identity()));
        List<WaitlistView> waitlistViewList = new ArrayList<>();

        for (BookingRequestModel bookingRequest : bookingRequests) {
            var builder = WaitlistView.newBuilder();
            builder.setWaitlist(bookingRequest);

            if (extraMap.containsKey(bookingRequest.getId())) {
                builder.setIsAvailable(extraMap.get(bookingRequest.getId()).getIsAvailable());
                builder.setIsExpired(extraMap.get(bookingRequest.getId()).getIsExpired());
            }

            // 客户信息转换
            BusinessCustomerInfoModel customerInfoModel = customerInfoModelMap.get(bookingRequest.getCustomerId());
            if (customerInfoModel != null) {
                builder.setClient(WaitlistConverter.INSTANCE.convertCustomerInfo(customerInfoModel));
            }

            // has update
            var clientDto = clientListMap.get(bookingRequest.getCustomerId());
            builder.setHasRequestUpdate(clientDto != null && clientDto.hasRequestUpdate());

            // 获取 petId -> serviceId 映射
            Map<Long, Set<Long>> petServicesMap = petIdServiceIdListMapByBookingRequestId.get(bookingRequest.getId());
            if (petServicesMap != null) {
                for (Map.Entry<Long, Set<Long>> entry : petServicesMap.entrySet()) {
                    Long petId = entry.getKey();
                    Set<Long> petServiceIds = entry.getValue();

                    BusinessCustomerPetInfoModel petModel = petInfoModelMap.get(petId);
                    if (petModel == null) continue;
                    // 转换服务列表
                    List<WaitlistView.ServiceInfo> serviceList = petServiceIds.stream()
                            .map(serviceInfoMap::get)
                            .filter(Objects::nonNull)
                            .map(WaitlistConverter.INSTANCE::convertServiceInfo)
                            .toList();

                    // 转换宠物信息并添加服务
                    WaitlistView.PetInfo.Builder petBuilder =
                            WaitlistConverter.INSTANCE.convertPetInfo(petModel).toBuilder();
                    petBuilder.addAllServices(serviceList);
                    builder.addPets(petBuilder.build());
                }
                // createBy
                if (bookingRequest.hasAttr() && bookingRequest.getAttr().hasCreatedByStaffId()) {
                    StaffModel staffModel =
                            staffMap.get(bookingRequest.getAttr().getCreatedByStaffId());
                    if (staffModel != null) {
                        builder.setCreateBy(WaitlistConverter.INSTANCE.convertStaffInfo(staffModel));
                    }
                }
                builder.setTotalPrice(WaitlistConverter.getWaitlistTotalPrice(bookingRequest.getServicesList()));
            }
            // membership
            builder.setMembershipSubscriptions(membershipMap.getOrDefault(
                    bookingRequest.getCustomerId(), MembershipSubscriptionListModel.getDefaultInstance()));

            waitlistViewList.add(builder.build());
        }
        return waitlistViewList;
    }

    private Map<Long, OrderModel> getOrderList(List<Long> bookingRequestIds) {
        var response = orderClient.getOrderList(GetOrderListRequest.newBuilder()
                .setSourceType(OrderSourceType.BOOKING_REQUEST.name().toLowerCase())
                .addAllSourceIds(bookingRequestIds)
                .build());
        Map<Long, OrderModel> returnMap = new HashMap<>();
        response.getOrderListList()
                .forEach(orderDetailModel -> returnMap.computeIfAbsent(
                        orderDetailModel.getOrder().getSourceId(), k -> orderDetailModel.getOrder()));
        return returnMap;
    }

    private Double getPrepayRate(BookOnlineDepositDTO obDeposit, OrderModel order) {
        BigDecimal totalAmount = BigDecimal.valueOf(order.getTotalAmount());

        if (obDeposit == null) return 0d;
        if (totalAmount.compareTo(BigDecimal.ZERO) <= 0) return 1d;

        BigDecimal amountExcludeTipAndFee =
                obDeposit.getAmount().subtract(obDeposit.getConvenienceFee()).subtract(obDeposit.getTipsAmount());

        return amountExcludeTipAndFee
                .divide(totalAmount, 2, RoundingMode.HALF_UP)
                .doubleValue();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CREATE_APPOINTMENT})
    public void bookWaitlist(BookWaitlistParams request, StreamObserver<BookWaitlistResult> responseObserver) {
        long companyId = AuthContext.get().companyId();

        // 检查宠物详情
        checkPetDetail(request.getPetDetailsList());

        // 创建预约
        CreateAppointmentRequest createAppointmentRequest = CreateAppointmentRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(request.getBusinessId())
                .setStaffId(AuthContext.get().staffId())
                .setAppointment(request.getAppointment())
                .addAllPetDetails(request.getPetDetailsList())
                .addAllNotes(request.getNotesList())
                .build();

        var apptId = appointmentServiceBlockingStub
                .createAppointment(createAppointmentRequest)
                .getAppointmentId();

        //  关联并删除 waitlist
        bookingRequestService.updateBookingRequest(UpdateBookingRequestRequest.newBuilder()
                .setStatus(BookingRequestStatus.SCHEDULED)
                .setAppointmentId(apptId)
                .setId(request.getWaitlistId())
                .build());

        responseObserver.onNext(
                BookWaitlistResult.newBuilder().setAppointmentId(apptId).build());
        responseObserver.onCompleted();
    }

    /**
     * 构建服务层过滤条件
     */
    private ListWaitlistsRequest buildServiceFilter(ListWaitlistParams request) {
        var builder = ListWaitlistsRequest.newBuilder();
        builder.addBusinessIds(request.getBusinessId());
        builder.setPagination(request.getPagination());
        builder.addServiceItems(request.getServiceType());
        builder.addSources(BookingRequestModel.Source.BUSINESS);
        builder.addSources(BookingRequestModel.Source.MEMBERSHIP);
        builder.addSources(BookingRequestModel.Source.OB);
        if (request.hasFilters()) {
            var filter = request.getFilters();
            if (filter.hasStartDate()) {
                builder.setStartDate(request.getFilters().getStartDate());
            }
            if (filter.hasEndDate()) {
                builder.setLatestEndDate(request.getFilters().getEndDate());
            }
            if (filter.hasIsFromOb() && filter.getIsFromOb()) {
                builder.clearSources();
                builder.addSources(BookingRequestModel.Source.OB);
            }
            if (filter.hasIsExpired() && filter.getIsExpired()) {
                builder.setIsWaitlistExpired(true);
            }
            if (filter.hasIsAvailable() && filter.getIsAvailable()) {
                builder.setIsWaitlistAvailable(true);
            }
        }
        return builder.build();
    }

    private Set<Long> getCustomerIdsByKeyword(long businessId, String filter) {
        SearchCustomerIdsParam param = new SearchCustomerIdsParam()
                .setBusinessId(Math.toIntExact(businessId))
                .setKeyword(filter);
        return customerClient.searchCustomerIds(param).stream()
                .map(Integer::longValue)
                .collect(Collectors.toSet());
    }

    private void checkPetDetail(List<PetDetailDef> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Pet details cannot be empty");
        }
        for (PetDetailDef petDetail : petDetails) {
            if (petDetail.getPetId() <= 0) {
                throw bizException(Code.CODE_PARAMS_ERROR, "Pet id must be greater than 0");
            }
            if (CollectionUtils.isEmpty(petDetail.getServicesList())) {
                throw bizException(Code.CODE_PARAMS_ERROR, "Services cannot be empty");
            }
            for (SelectedServiceDef service : petDetail.getServicesList()) {
                if (service.getServiceId() <= 0) {
                    throw bizException(Code.CODE_PARAMS_ERROR, "Service id must be greater than 0");
                }
            }
        }
    }
}
