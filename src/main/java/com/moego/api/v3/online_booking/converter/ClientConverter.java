package com.moego.api.v3.online_booking.converter;

import com.google.protobuf.Timestamp;
import com.google.type.LatLng;
import com.moego.idl.api.online_booking.v1.AddressDetail;
import com.moego.idl.api.online_booking.v1.CustomerDetail;
import com.moego.idl.api.online_booking.v1.OBClientPaymentTypeResponse;
import com.moego.idl.api.online_booking.v1.UpdateOBClientRequest;
import com.moego.idl.api.online_booking.v1.UpdateOBClientResponse;
import com.moego.idl.api.online_booking.v1.UpsertAddressParams;
import com.moego.idl.api.online_booking.v1.UpsertAddressResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressUpdateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModel;
import com.moego.idl.models.customer.v1.CustomerDef;
import com.moego.idl.models.customer.v1.CustomerModelOnlineBookingView;
import com.moego.idl.utils.v2.Int32List;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.ProfileRequestAddressDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentGroupSettingDTO;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/11
 */
@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ClientConverter {
    ClientConverter INSTANCE = Mappers.getMapper(ClientConverter.class);

    @Mapping(target = "customQuestions", ignore = true)
    @Mapping(target = "preferredDay", source = "preferredDays")
    @Mapping(target = "preferredTime", source = "preferredTimes")
    CustomerProfileRequestDTO.ClientProfileDTO def2ClientProfileDTO(CustomerDef customer);

    @Mappings({@Mapping(target = "preferredDay", ignore = true), @Mapping(target = "preferredTime", ignore = true)})
    CustomerModelOnlineBookingView dto2view(CustomerProfileRequestDTO.ClientProfileDTO dto);

    @Mappings({
        @Mapping(target = "preAuthPolicy", source = "preAuthPolicy", defaultValue = ""),
        @Mapping(target = "cancellationPolicy", source = "cancellationPolicy", defaultValue = ""),
        @Mapping(target = "prepayPolicy", source = "prepayPolicy", defaultValue = ""),
        @Mapping(
                target = "depositAmount",
                expression =
                        "java(settingDTO.getDepositAmount() == null ? 0 : settingDTO.getDepositAmount().doubleValue())"),
        @Mapping(
                target = "paymentType",
                expression =
                        "java(com.moego.idl.models.online_booking.v1.PaymentType.forNumber(settingDTO.getPaymentType()))")
    })
    OBClientPaymentTypeResponse toPaymentSettingResponse(BookOnlinePaymentGroupSettingDTO settingDTO);

    CustomerDetail toCustomerDetail(BusinessCustomerModel model, boolean hasPetParentAppAccount);

    @Mappings({
        // @Mapping(target = "avatarPath", ignore = true),
        // @Mapping(target = "clientColor", ignore = true),
        @Mapping(target = "preferredDay", ignore = true),
        @Mapping(target = "preferredTime", ignore = true),
        @Mapping(target = "questionAnswers", ignore = true),
    })
    CustomerDetail toCustomerDetail(
            CustomerProfileRequestDTO.ClientProfileDTO clientProfile, boolean hasPetParentAppAccount);

    @AfterMapping
    default void afterMapping(
            CustomerProfileRequestDTO.ClientProfileDTO clientProfile,
            @MappingTarget CustomerDetail.Builder customerDetail) {
        if (customerDetail.getPreferredDayList() != null) {
            List<Integer> list = integerArrayToIntegerList(clientProfile.getPreferredDay());
            if (list != null) {
                customerDetail.addAllPreferredDay(list);
            }
        }
        if (customerDetail.getPreferredTimeList() != null) {
            List<Integer> list1 = integerArrayToIntegerList(clientProfile.getPreferredTime());
            if (list1 != null) {
                customerDetail.addAllPreferredTime(list1);
            }
        }
    }

    private List<Integer> integerArrayToIntegerList(Integer[] integerArray) {
        if (integerArray == null) {
            return null;
        }

        List<Integer> list = new ArrayList<Integer>(integerArray.length);
        for (Integer integer : integerArray) {
            list.add(integer);
        }

        return list;
    }

    @Mapping(target = "phoneNumber", ignore = true)
    @Mapping(target = "customQuestions", ignore = true)
    CustomerProfileRequestDTO.ClientProfileDTO dto2ClientProfileDTO(MoeBusinessCustomerDTO dto);

    @Mapping(target = "addressId", source = "customerAddressId")
    @Mapping(
            target = "isPrimary",
            expression =
                    "java(customerAddressDto.getIsPrimary() != null ? customerAddressDto.getIsPrimary()  == (byte) 1 : false)")
    AddressDetail toAddressDetail(CustomerAddressDto customerAddressDto);

    @Mapping(target = "addressId", source = "id")
    @Mapping(target = "lat", source = "coordinate.latitude")
    @Mapping(target = "lng", source = "coordinate.longitude")
    AddressDetail toAddressDetail(BusinessCustomerAddressModel addressModel);

    @Mapping(
            target = "addressId",
            expression = "java(dto.getCustomerAddressId() == null ? dto.getId() : dto.getCustomerAddressId())")
    @Mapping(target = "isProfileRequestAddress", expression = "java(dto.getCustomerAddressId() == null)")
    AddressDetail toAddressDetail(ProfileRequestAddressDTO dto);

    @Mapping(
            target = "coordinate",
            source = "request",
            conditionExpression = "java(hasCoordinate(request))",
            qualifiedByName = "toCoordinate")
    BusinessCustomerAddressCreateDef toAddressCreateDef(UpdateOBClientRequest.UpdateOBClientRequestAddress request);

    @Mapping(
            target = "coordinate",
            source = "request",
            conditionExpression = "java(hasCoordinate(request))",
            qualifiedByName = "toCoordinate")
    BusinessCustomerAddressCreateDef toAddressCreateDef(UpsertAddressParams request);

    @Mapping(
            target = "coordinate",
            source = "request",
            conditionExpression = "java(hasCoordinate(request))",
            qualifiedByName = "toCoordinate")
    BusinessCustomerAddressUpdateDef toAddressUpdateDef(UpdateOBClientRequest.UpdateOBClientRequestAddress request);

    @Mapping(
            target = "coordinate",
            source = "request",
            conditionExpression = "java(hasCoordinate(request))",
            qualifiedByName = "toCoordinate")
    BusinessCustomerAddressUpdateDef toAddressUpdateDef(UpsertAddressParams request);

    @Mapping(target = "lat", source = "coordinate.latitude")
    @Mapping(target = "lng", source = "coordinate.longitude")
    UpdateOBClientResponse.UpdateOBClientAddressResponse toUpdateOBClientAddressResponse(
            BusinessCustomerAddressModel addressModel);

    @Mapping(target = "lat", source = "coordinate.latitude")
    @Mapping(target = "lng", source = "coordinate.longitude")
    UpsertAddressResult toUpsertAddressResult(BusinessCustomerAddressModel addressModel);

    default boolean hasCoordinate(UpdateOBClientRequest.UpdateOBClientRequestAddress request) {
        return StringUtils.hasText(request.getLat()) && StringUtils.hasText(request.getLng());
    }

    default boolean hasCoordinate(UpsertAddressParams request) {
        return StringUtils.hasText(request.getLat()) && StringUtils.hasText(request.getLng());
    }

    @Named("toCoordinate")
    default LatLng toCoordinate(UpdateOBClientRequest.UpdateOBClientRequestAddress request) {
        return LatLng.newBuilder()
                .setLatitude(Double.parseDouble(request.getLat()))
                .setLongitude(Double.parseDouble(request.getLng()))
                .build();
    }

    @Named("toCoordinate")
    default LatLng toCoordinate(UpsertAddressParams request) {
        return LatLng.newBuilder()
                .setLatitude(Double.parseDouble(request.getLat()))
                .setLongitude(Double.parseDouble(request.getLng()))
                .build();
    }

    default LocalDateTime timestampToLocalDateTime(Timestamp timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos(), ZoneOffset.UTC);
    }

    default Timestamp localDateTimeToTimestamp(LocalDateTime localDateTime) {
        var instant = localDateTime.toInstant(ZoneOffset.UTC);
        return Timestamp.newBuilder()
                .setSeconds(instant.getEpochSecond())
                .setNanos(instant.getNano())
                .build();
    }

    default boolean isPrimary(int value) {
        return value == 1;
    }

    default int isPrimary(boolean value) {
        return value ? 1 : 0;
    }

    default Integer[] int32ListToIntegerArray(Int32List int32List) {
        if (int32List == null) {
            return null;
        }
        return int32List.getValuesList().toArray(Integer[]::new);
    }
}
