package com.moego.api.v3.organization.controller;

import com.google.type.DayOfWeek;
import com.moego.api.v3.organization.consts.ClockInOutPermissionScopeEnum;
import com.moego.api.v3.organization.convert.StaffConvert;
import com.moego.api.v3.organization.service.StaffLoginTimeService;
import com.moego.api.v3.organization.service.StaffService;
import com.moego.api.v3.permission.service.RoleService;
import com.moego.common.enums.BooleanEnum;
import com.moego.idl.api.organization.v1.CalenderStaff;
import com.moego.idl.api.organization.v1.CreateStaffParams;
import com.moego.idl.api.organization.v1.CreateStaffResult;
import com.moego.idl.api.organization.v1.DeleteStaffAvailabilityOverrideParams;
import com.moego.idl.api.organization.v1.DeleteStaffAvailabilityOverrideResult;
import com.moego.idl.api.organization.v1.DeleteStaffParams;
import com.moego.idl.api.organization.v1.DeleteStaffResult;
import com.moego.idl.api.organization.v1.GetBusinessStaffAvailabilityTypeParams;
import com.moego.idl.api.organization.v1.GetBusinessStaffAvailabilityTypeResult;
import com.moego.idl.api.organization.v1.GetClockInOutStaffsParams;
import com.moego.idl.api.organization.v1.GetClockInOutStaffsResult;
import com.moego.idl.api.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsParams;
import com.moego.idl.api.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsResult;
import com.moego.idl.api.organization.v1.GetRecommendedStaffLoginTimeParams;
import com.moego.idl.api.organization.v1.GetRecommendedStaffLoginTimeResult;
import com.moego.idl.api.organization.v1.GetStaffAvailabilityOverrideParams;
import com.moego.idl.api.organization.v1.GetStaffAvailabilityOverrideResult;
import com.moego.idl.api.organization.v1.GetStaffAvailabilityParams;
import com.moego.idl.api.organization.v1.GetStaffAvailabilityResult;
import com.moego.idl.api.organization.v1.GetStaffCalenderViewParams;
import com.moego.idl.api.organization.v1.GetStaffCalenderViewResult;
import com.moego.idl.api.organization.v1.GetStaffFullDetailParams;
import com.moego.idl.api.organization.v1.GetStaffFullDetailResult;
import com.moego.idl.api.organization.v1.GetStaffLoginTimeParams;
import com.moego.idl.api.organization.v1.GetStaffLoginTimeResult;
import com.moego.idl.api.organization.v1.GetStaffsByWorkingLocationIdsParams;
import com.moego.idl.api.organization.v1.GetStaffsByWorkingLocationIdsResult;
import com.moego.idl.api.organization.v1.InitStaffAvailabilityParams;
import com.moego.idl.api.organization.v1.InitStaffAvailabilityResult;
import com.moego.idl.api.organization.v1.ListStaffGroupByRoleParams;
import com.moego.idl.api.organization.v1.ListStaffGroupByRoleResult;
import com.moego.idl.api.organization.v1.QueryStaffListByPaginationParams;
import com.moego.idl.api.organization.v1.QueryStaffListByPaginationResult;
import com.moego.idl.api.organization.v1.SlotAvailabilityDayOngoingHistoryList;
import com.moego.idl.api.organization.v1.StaffAvailabilityInfo;
import com.moego.idl.api.organization.v1.StaffServiceGrpc;
import com.moego.idl.api.organization.v1.TimeAvailabilityDayOngoingHistoryList;
import com.moego.idl.api.organization.v1.UpdateBusinessStaffAvailabilityTypeParams;
import com.moego.idl.api.organization.v1.UpdateBusinessStaffAvailabilityTypeResult;
import com.moego.idl.api.organization.v1.UpdateStaffAvailabilityOverrideParams;
import com.moego.idl.api.organization.v1.UpdateStaffAvailabilityParams;
import com.moego.idl.api.organization.v1.UpdateStaffAvailabilityResult;
import com.moego.idl.api.organization.v1.UpdateStaffLoginTimeParams;
import com.moego.idl.api.organization.v1.UpdateStaffLoginTimeResult;
import com.moego.idl.api.organization.v1.UpdateStaffParams;
import com.moego.idl.api.organization.v1.UpdateStaffResult;
import com.moego.idl.api.organization.v1.WeekAvailability;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.LimitationGroup;
import com.moego.idl.models.organization.v1.LimitationGroupDef;
import com.moego.idl.models.organization.v1.LocationStaffsDef;
import com.moego.idl.models.organization.v1.PetBreedLimitation;
import com.moego.idl.models.organization.v1.PetBreedLimitationDef;
import com.moego.idl.models.organization.v1.PetSizeLimitation;
import com.moego.idl.models.organization.v1.PetSizeLimitationDef;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SendInviteLinkParamsDef;
import com.moego.idl.models.organization.v1.ServiceLimitation;
import com.moego.idl.models.organization.v1.ServiceLimitationDef;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.SlotAvailabilityDayDef;
import com.moego.idl.models.organization.v1.SlotDailySettingDef;
import com.moego.idl.models.organization.v1.SlotHourSetting;
import com.moego.idl.models.organization.v1.SlotHourSettingDef;
import com.moego.idl.models.organization.v1.StaffAccessControlDef;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.StaffNotificationDef;
import com.moego.idl.models.organization.v1.StaffPayrollSettingDef;
import com.moego.idl.models.organization.v1.StaffWorkingLocationDef;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.idl.models.organization.v1.UpdateStaffDef;
import com.moego.idl.models.permission.v1.RoleModel;
import com.moego.idl.service.offering.v1.RemoveServiceFilterRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.DeleteStaffAvailabilityOverrideRequest;
import com.moego.idl.service.organization.v1.GetClockInOutStaffsRequest;
import com.moego.idl.service.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsRequest;
import com.moego.idl.service.organization.v1.GetStaffAvailabilityOverrideRequest;
import com.moego.idl.service.organization.v1.GetStaffAvailabilityRequest;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.GetStaffLoginTimeRequest;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationIdsRequest;
import com.moego.idl.service.organization.v1.InitStaffAvailabilityRequest;
import com.moego.idl.service.organization.v1.UpdateStaffRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.params.BatchUpdateOBSettingParam;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
public class StaffServer extends StaffServiceGrpc.StaffServiceImplBase {

    private final com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessService;
    private final PermissionHelper permissionHelper;
    private final StaffLoginTimeService staffLoginTimeService;
    private final StaffService staffHelper;
    private final FeatureFlagApi featureFlagApi;

    private final StaffConvert converter = StaffConvert.INSTANCE;

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub
            serviceManagementServiceBlockingStub;
    private final IGroomingOnlineBookingService onlineBookingApi;
    private final RoleService roleService;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_STAFF_MEMBERS, PermissionEnums.ADD_NEW_STAFF})
    public void createStaff(CreateStaffParams request, StreamObserver<CreateStaffResult> responseObserver) {
        var response = staffService.createStaff(converter.toCreateStaffRequest(request).toBuilder()
                .setTokenStaffId(AuthContext.get().staffId())
                .setTokenCompanyId(AuthContext.get().companyId())
                .setTokenBusinessId(AuthContext.get().businessId())
                .build());

        responseObserver.onNext(converter.toCreateStaffResult(response));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getStaffFullDetail(
            GetStaffFullDetailParams request, StreamObserver<GetStaffFullDetailResult> responseObserver) {
        var response = staffService.getStaffFullDetail(converter.toGetStaffFullDetailRequest(request).toBuilder()
                .setTokenStaffId(AuthContext.get().staffId())
                .setTokenCompanyId(AuthContext.get().companyId())
                .build());

        responseObserver.onNext(converter.toGetStaffFullDetailResult(response));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_STAFF_MEMBERS})
    public void updateStaff(UpdateStaffParams request, StreamObserver<UpdateStaffResult> responseObserver) {
        Long tokenCompanyId = AuthContext.get().companyId();
        Long tokenStaffId = AuthContext.get().staffId();
        Long tokenBusinessId = AuthContext.get().businessId();

        var staff = staffService.getStaffDetail(GetStaffDetailRequest.newBuilder()
                .setId(request.getId())
                .setCompanyId(AuthContext.get().companyId())
                .build());
        if (!staff.hasStaff()) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }

        var updateStaffRequest = converter.toUpdateStaffRequest(request).toBuilder()
                .setTokenStaffId(tokenStaffId)
                .setTokenCompanyId(tokenCompanyId)
                .setTokenBusinessId(tokenBusinessId);
        // Check permission
        Map<PermissionEnums, Long> permissionMap =
                permissionHelper.getPermissionMapByStaffId(tokenCompanyId, tokenStaffId);
        if (permissionMap.get(PermissionEnums.EDIT_STAFF_PROFILE) == null) {
            updateStaffRequest.setStaffProfile(UpdateStaffDef.getDefaultInstance());
            updateStaffRequest.setAccessControl(StaffAccessControlDef.getDefaultInstance());
            updateStaffRequest.setWorkingLocation(StaffWorkingLocationDef.getDefaultInstance());
            updateStaffRequest.setInviteLink(SendInviteLinkParamsDef.getDefaultInstance());
            updateStaffRequest.clearLoginTime();
        }
        if (permissionMap.get(PermissionEnums.EDIT_STAFF_NOTIFICATION) == null) {
            updateStaffRequest.setNotificationSetting(StaffNotificationDef.getDefaultInstance());
        }
        if (permissionMap.get(PermissionEnums.EDIT_STAFF_PAY_RATE) == null) {
            updateStaffRequest.setPayrollSetting(StaffPayrollSettingDef.getDefaultInstance());
        }
        var response = staffService.updateStaff(updateStaffRequest.build());

        responseObserver.onNext(converter.toUpdateStaffResult(response));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_STAFF_MEMBERS, PermissionEnums.EDIT_STAFF_PROFILE})
    public void deleteStaff(DeleteStaffParams request, StreamObserver<DeleteStaffResult> responseObserver) {
        var staff = staffService.getStaffDetail(GetStaffDetailRequest.newBuilder()
                .setId(request.getId())
                .setCompanyId(AuthContext.get().companyId())
                .build());
        if (!staff.hasStaff()) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }
        var response = staffService.deleteStaff(converter.toDeleteStaffRequest(request).toBuilder()
                .setTokenStaffId(AuthContext.get().staffId())
                .setTokenCompanyId(AuthContext.get().companyId())
                .build());

        if (response.getSuccess()) {
            // 删除 staff 时，删除 service by staff相关数据
            serviceManagementServiceBlockingStub.removeServiceFilter(RemoveServiceFilterRequest.newBuilder()
                    .setCompanyId(AuthContext.get().companyId())
                    .setStaffId(request.getId())
                    .build());
        }

        responseObserver.onNext(converter.toDeleteStaffResult(response));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_STAFF_MEMBERS})
    public void queryStaffListByPagination(
            QueryStaffListByPaginationParams request,
            StreamObserver<QueryStaffListByPaginationResult> responseObserver) {
        var result = staffService.queryStaffListByPagination(
                converter.toQueryStaffListByPaginationRequest(request).toBuilder()
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .build());

        responseObserver.onNext(converter.toQueryStaffListByPaginationResult(result));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getStaffsByWorkingLocationIds(
            GetStaffsByWorkingLocationIdsParams request,
            StreamObserver<GetStaffsByWorkingLocationIdsResult> responseObserver) {
        var response = staffService.getStaffsByWorkingLocationIds(GetStaffsByWorkingLocationIdsRequest.newBuilder()
                .addAllBusinessIds(request.getBusinessIdsList())
                .setTokenCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .build());
        responseObserver.onNext(converter.toGetStaffsByWorkingLocationIdsResult(response));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getClockInOutStaffs(
            GetClockInOutStaffsParams request, StreamObserver<GetClockInOutStaffsResult> responseObserver) {
        Long tokenCompanyId = AuthContext.get().companyId();
        Long tokenStaffId = AuthContext.get().staffId();
        Map<PermissionEnums, Long> staffPermissionMap =
                permissionHelper.getPermissionMapByStaffId(tokenCompanyId, tokenStaffId);
        // 无权限时前端会隐藏入口，这个接口返回空列表即可
        if (staffPermissionMap.get(PermissionEnums.CLOCK_IN_OR_OUT) == null) {
            responseObserver.onNext(GetClockInOutStaffsResult.newBuilder().build());
            responseObserver.onCompleted();
        }
        boolean isForThemselves = staffPermissionMap.get(PermissionEnums.CLOCK_IN_OR_OUT)
                == ClockInOutPermissionScopeEnum.FOR_THEMSELVES_ONLY.getValue();

        var response = staffService.getClockInOutStaffs(GetClockInOutStaffsRequest.newBuilder()
                .setTokenStaffId(tokenStaffId)
                .setTokenCompanyId(tokenCompanyId)
                .setDate(request.getDate())
                .addAllStaffIds(request.getStaffIdsList())
                .setIsForThemselves(isForThemselves)
                .build());
        responseObserver.onNext(GetClockInOutStaffsResult.newBuilder()
                .addAllClockInOutStaffs(response.getClockInOutStaffsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getEnterpriseStaffsByWorkingLocationIds(
            GetEnterpriseStaffsByWorkingLocationIdsParams request,
            StreamObserver<GetEnterpriseStaffsByWorkingLocationIdsResult> responseObserver) {
        var response = staffService.getEnterpriseStaffsByWorkingLocationIds(
                GetEnterpriseStaffsByWorkingLocationIdsRequest.newBuilder()
                        .addAllBusinessIds(request.getBusinessIdsList())
                        .setCompanyId(AuthContext.get().companyId())
                        .build());
        GetEnterpriseStaffsByWorkingLocationIdsResult result =
                GetEnterpriseStaffsByWorkingLocationIdsResult.newBuilder()
                        .addAllLocationStaffs(response.getLocationStaffsList())
                        .build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getStaffLoginTime(
            GetStaffLoginTimeParams request, StreamObserver<GetStaffLoginTimeResult> responseObserver) {
        var response = staffService.getStaffLoginTime(GetStaffLoginTimeRequest.newBuilder()
                .setStaffId(AuthContext.get().staffId())
                .setCompanyId(AuthContext.get().companyId())
                .build());
        responseObserver.onNext(converter.toGetStaffLoginTimeResult(response));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateStaffLoginTime(
            UpdateStaffLoginTimeParams request, StreamObserver<UpdateStaffLoginTimeResult> responseObserver) {
        var staff = staffService.getStaffDetail(GetStaffDetailRequest.newBuilder()
                .setId(request.getStaffId())
                .setCompanyId(AuthContext.get().companyId())
                .build());
        if (!staff.hasStaff()) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }
        var response = staffService.updateStaff(UpdateStaffRequest.newBuilder()
                .setId(request.getStaffId())
                .setLoginTime(request.getLoginTime())
                .build());
        responseObserver.onNext(UpdateStaffLoginTimeResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getRecommendedStaffLoginTime(
            GetRecommendedStaffLoginTimeParams request,
            StreamObserver<GetRecommendedStaffLoginTimeResult> responseObserver) {
        responseObserver.onNext(GetRecommendedStaffLoginTimeResult.newBuilder()
                .setLoginTime(staffLoginTimeService.getRecommendedStaffLoginTime(
                        AuthContext.get().companyId()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listStaffGroupByRole(
            ListStaffGroupByRoleParams request, StreamObserver<ListStaffGroupByRoleResult> responseObserver) {
        var staffs = staffHelper.listStaffForBusiness(AuthContext.get().companyId(), request.getBusinessId(), null);
        var roleIds = staffs.stream().map(StaffBasicView::getRoleId).distinct().toList();
        var roleIdToInfoMap =
                roleService.listRolesInfo(roleIds, List.of(AuthContext.get().companyId()));

        var staffGroups = staffs.stream().collect(Collectors.groupingBy(StaffBasicView::getRoleId)).entrySet().stream()
                .map(entry -> {
                    Long roleId = entry.getKey();
                    var role = roleIdToInfoMap.get(roleId);
                    return ListStaffGroupByRoleResult.RoleStaffGroup.newBuilder()
                            .setRoleName(Optional.ofNullable(role)
                                    .map(RoleModel::getName)
                                    .orElse(""))
                            .addAllStaffs(entry.getValue())
                            .build();
                })
                .toList();

        responseObserver.onNext(ListStaffGroupByRoleResult.newBuilder()
                .addAllRoleStaffGroups(staffGroups)
                .addAllWorkingStaffIds(
                        staffs.stream().map(StaffBasicView::getId).toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getBusinessStaffAvailabilityType(
            GetBusinessStaffAvailabilityTypeParams request,
            StreamObserver<GetBusinessStaffAvailabilityTypeResult> responseObserver) {
        var availabilityType = staffService
                .getBusinessStaffAvailabilityType(
                        com.moego.idl.service.organization.v1.GetBusinessStaffAvailabilityTypeRequest.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(request.getBusinessId())
                                .build())
                .getAvailabilityType();

        responseObserver.onNext(GetBusinessStaffAvailabilityTypeResult.newBuilder()
                .setAvailabilityType(AvailabilityType.forNumber(availabilityType.getNumber()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateBusinessStaffAvailabilityType(
            UpdateBusinessStaffAvailabilityTypeParams request,
            StreamObserver<UpdateBusinessStaffAvailabilityTypeResult> responseObserver) {
        var businessId = request.getBusinessId();
        var availabilityType = request.getAvailabilityType();
        staffService.updateBusinessStaffAvailabilityType(
                com.moego.idl.service.organization.v1.UpdateBusinessStaffAvailabilityTypeRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setAvailabilityType(availabilityType)
                        .build());

        // 只有白名单中的用户才需要同步 OB 的 available_time_type
        if (isMultiPetNewFlow(businessId)) {
            syncObAvailableTimeType(businessId, availabilityType);
        }

        responseObserver.onNext(
                UpdateBusinessStaffAvailabilityTypeResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    private boolean isMultiPetNewFlow(long businessId) {
        return featureFlagApi.isOn(
                FeatureFlags.ENABLE_MULTI_PET_BY_SLOT,
                FeatureFlagContext.builder().business(businessId).build());
    }

    private void syncObAvailableTimeType(final long businessId, final AvailabilityType availabilityType) {
        // if sync, update ob available_time_type
        var obSetting = onlineBookingApi.getOBSetting(Math.toIntExact(businessId));
        if (BooleanEnum.VALUE_TRUE.equals(obSetting.getAvailableTimeSync())) {
            // set ob available_time_type
            var availabilityTimeType =
                    switch (availabilityType) {
                        case BY_TIME -> (byte) 0;
                        case BY_SLOT -> (byte) 1;
                        default -> throw ExceptionUtil.bizException(
                                Code.CODE_PARAMS_ERROR, "invalid availability type");
                    };

            var newObSetting = new BookOnlineDTO()
                    .setBusinessId(Math.toIntExact(businessId))
                    .setAvailableTimeType(availabilityTimeType);
            onlineBookingApi.batchUpdateOBSetting(new BatchUpdateOBSettingParam()
                    .setBusinessIds(List.of(Math.toIntExact(businessId)))
                    .setUpdateValue(newObSetting));
        }
    }

    private static final String WEEK_FIRST = "firstWeek";
    private static final String WEEK_SECOND = "secondWeek";
    private static final String WEEK_THIRD = "thirdWeek";
    private static final String WEEK_FORTH = "forthWeek";
    private static final String WEEK_UNDEFINED = "undefined";

    private static final int MONDAY = 1;
    private static final int TUESDAY = 2;
    private static final int WEDNESDAY = 3;
    private static final int THURSDAY = 4;
    private static final int FRIDAY = 5;
    private static final int SATURDAY = 6;
    private static final int SUNDAY = 7;

    private static String serviceScheduleType2Week(
            com.moego.idl.models.organization.v1.ScheduleType serviceScheduleType) {
        return switch (serviceScheduleType) {
            case ONE_WEEK -> WEEK_FIRST;
            case TWO_WEEK -> WEEK_SECOND;
            case THREE_WEEK -> WEEK_THIRD;
            case FOUR_WEEK -> WEEK_FORTH;
            default -> WEEK_UNDEFINED;
        };
    }

    private static String serviceDayOfWeek2String(com.google.type.DayOfWeek dayOfWeek) {
        return switch (dayOfWeek) {
            case MONDAY -> "monday";
            case TUESDAY -> "tuesday";
            case WEDNESDAY -> "wednesday";
            case THURSDAY -> "thursday";
            case FRIDAY -> "friday";
            case SATURDAY -> "saturday";
            case SUNDAY -> "sunday";
            default -> throw new IllegalArgumentException("invalid day of week:" + dayOfWeek);
        };
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getStaffCalenderView(
            GetStaffCalenderViewParams request, StreamObserver<GetStaffCalenderViewResult> responseObserver) {
        var resp = staffService.getStaffCalenderView(
                com.moego.idl.service.organization.v1.GetStaffCalenderViewRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .setStartDate(request.getStartDate())
                        .build());

        responseObserver.onNext(GetStaffCalenderViewResult.newBuilder()
                .addAllStaffAvailabilityList(resp.getStaffListList().stream()
                        .map(serviceStaffAvailability -> {
                            var builder = CalenderStaff.newBuilder()
                                    .setStaffId(serviceStaffAvailability.getStaffId())
                                    .setIsAvailable(serviceStaffAvailability.getIsAvailable());
                            if (!request.hasAvailabilityType()
                                    || request.getAvailabilityType() == AvailabilityType.BY_SLOT) {
                                builder.setScheduleType(serviceStaffAvailability.getScheduleType())
                                        .putAllSlotAvailabilityDayMap(
                                                serviceStaffAvailability.getSlotAvailabilityDayMapMap())
                                        .setSlotStartSunday(serviceStaffAvailability.getSlotStartSunday());
                            }
                            if (!request.hasAvailabilityType()
                                    || request.getAvailabilityType() == AvailabilityType.BY_TIME) {
                                builder.setTimeScheduleType(serviceStaffAvailability.getTimeScheduleType())
                                        .putAllTimeAvailabilityDayMap(
                                                serviceStaffAvailability.getTimeAvailabilityDayMapMap())
                                        .setTimeStartSunday(serviceStaffAvailability.getTimeStartSunday());
                            }
                            return builder.build();
                        })
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getStaffAvailability(
            GetStaffAvailabilityParams request, StreamObserver<GetStaffAvailabilityResult> responseObserver) {
        var builder = GetStaffAvailabilityRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .addAllStaffIdList(request.getStaffIdListList());
        if (request.hasAvailabilityType()) {
            builder.setAvailabilityType(request.getAvailabilityType());
        }
        var serviceResp = staffService.getStaffAvailability(builder.build());
        List<StaffAvailabilityInfo> staffList = serviceResp.getStaffAvailabilityListList().stream()
                .map(serviceStaffAvailability -> {
                    Map<String, WeekAvailability> weeks = new HashMap<>();
                    weeks.put(WEEK_FIRST, extractWeekDays(WEEK_FIRST, serviceStaffAvailability));
                    weeks.put(WEEK_SECOND, extractWeekDays(WEEK_SECOND, serviceStaffAvailability));
                    weeks.put(WEEK_THIRD, extractWeekDays(WEEK_THIRD, serviceStaffAvailability));
                    weeks.put(WEEK_FORTH, extractWeekDays(WEEK_FORTH, serviceStaffAvailability));
                    return StaffAvailabilityInfo.newBuilder()
                            .setStaffId(serviceStaffAvailability.getStaffId())
                            .setStartDate(serviceStaffAvailability.getSlotStartSunday())
                            .setIsAvailable(serviceStaffAvailability.getIsAvailable())
                            .setScheduleType(serviceStaffAvailability.getScheduleType())
                            .putAllWeeks(weeks)
                            .build();
                })
                .toList();
        responseObserver.onNext(GetStaffAvailabilityResult.newBuilder()
                .addAllStaffList(staffList)
                .addAllStaffAvailabilityList(serviceResp.getStaffAvailabilityListList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void initStaffAvailability(
            final InitStaffAvailabilityParams request,
            final StreamObserver<InitStaffAvailabilityResult> responseObserver) {
        var currentCompanyId = AuthContext.get().companyId();
        if (!Objects.equals(100811L, currentCompanyId) && !Objects.equals(105809L, currentCompanyId)) {
            responseObserver.onNext(InitStaffAvailabilityResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var builder = InitStaffAvailabilityRequest.newBuilder();
        if (request.hasCompanyId()) {
            builder.setCompanyId(request.getCompanyId());
        }
        if (request.hasBusinessId()) {
            builder.setBusinessId(request.getBusinessId());
        }
        if (request.hasStartBusinessId()) {
            builder.setStartBusinessId(request.getStartBusinessId());
        }
        if (request.hasEndBusinessId()) {
            builder.setEndBusinessId(request.getEndBusinessId());
        }

        staffService.initStaffAvailability(
                builder.addAllStaffIds(request.getStaffIdsList()).build());

        responseObserver.onNext(InitStaffAvailabilityResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private WeekAvailability extractWeekDays(String weekName, StaffAvailability serviceStaffAvailability) {
        var weekDays = serviceStaffAvailability.getSlotAvailabilityDayListList().stream()
                .filter(slotAvailabilityDay -> {
                    var weekKey = serviceScheduleType2Week(slotAvailabilityDay.getScheduleType());
                    return weekKey.equals(weekName);
                })
                .collect(Collectors.toMap(
                        slotAvailabilityDay -> serviceDayOfWeek2String(slotAvailabilityDay.getDayOfWeek()),
                        slotAvailabilityDay -> slotAvailabilityDay));
        return WeekAvailability.newBuilder().putAllDays(weekDays).build();
    }

    private static SlotHourSettingDef slotHours2Def(SlotHourSetting limit) {
        return SlotHourSettingDef.newBuilder()
                .setStartTime(limit.getStartTime())
                .setEndTime(limit.getEndTime())
                .setCapacity(limit.getCapacity())
                .addAllLimitationGroups(bookingLimitation2Def(limit.getLimitationGroupsList()))
                .build();
    }

    private static int weekday2int(String weekName) {
        return switch (weekName) {
            case "monday" -> MONDAY;
            case "tuesday" -> TUESDAY;
            case "wednesday" -> WEDNESDAY;
            case "thursday" -> THURSDAY;
            case "friday" -> FRIDAY;
            case "saturday" -> SATURDAY;
            case "sunday" -> SUNDAY;
            default -> throw new IllegalArgumentException("invalid week name:" + weekName);
        };
    }

    private static List<LimitationGroupDef> bookingLimitation2Def(List<LimitationGroup> limitGroups) {
        if (CollectionUtils.isEmpty(limitGroups)) {
            return List.of();
        }
        return limitGroups.stream().map(StaffServer::bookingLimitation2Def).toList();
    }

    private static LimitationGroupDef bookingLimitation2Def(LimitationGroup limitGroup) {
        return LimitationGroupDef.newBuilder()
                .addAllServiceLimits(limitGroup.getServiceLimitsList().stream()
                        .map(StaffServer::getServiceLimitation)
                        .toList())
                .addAllPetBreedLimits(limitGroup.getPetBreedLimitsList().stream()
                        .map(StaffServer::getPetBreedLimitation)
                        .toList())
                .addAllPetSizeLimits(limitGroup.getPetSizeLimitsList().stream()
                        .map(StaffServer::getPetSizeLimitation)
                        .toList())
                .setOnlyAcceptSelected(limitGroup.getOnlyAcceptSelected())
                .build();
    }

    private static PetSizeLimitationDef getPetSizeLimitation(final PetSizeLimitation petSizeLimit) {
        return PetSizeLimitationDef.newBuilder()
                .addAllPetSizeIds(petSizeLimit.getPetSizeIdsList())
                .setIsAllSize(petSizeLimit.getIsAllSize())
                .setCapacity(petSizeLimit.getCapacity())
                .build();
    }

    private static PetBreedLimitationDef getPetBreedLimitation(final PetBreedLimitation petBreedLimit) {
        return PetBreedLimitationDef.newBuilder()
                .setPetTypeId(petBreedLimit.getPetTypeId())
                .setIsAllBreed(petBreedLimit.getIsAllBreed())
                .addAllBreedIds(petBreedLimit.getBreedIdsList())
                .setCapacity(petBreedLimit.getCapacity())
                .build();
    }

    private static ServiceLimitationDef getServiceLimitation(final ServiceLimitation serviceLimit) {
        return ServiceLimitationDef.newBuilder()
                .addAllServiceIds(serviceLimit.getServiceIdsList())
                .setCapacity(serviceLimit.getCapacity())
                .setIsAllService(serviceLimit.getIsAllService())
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateStaffAvailability(
            UpdateStaffAvailabilityParams request, StreamObserver<UpdateStaffAvailabilityResult> responseObserver) {
        List<StaffAvailabilityDef> staffList = request.getStaffListList().stream()
                .map(staffAvailabilityInfo -> {
                    // 遍历单个staff的所有week days，打平slotDayList
                    List<SlotAvailabilityDayDef> slotDayList = staffAvailabilityInfo.getWeeksMap().values().stream()
                            .flatMap(week -> week.getDaysMap().entrySet().stream()
                                    .map(day -> {
                                        var dayValue = day.getValue();
                                        return SlotAvailabilityDayDef.newBuilder()
                                                .setDayOfWeek(DayOfWeek.forNumber(weekday2int(day.getKey())))
                                                .setIsAvailable(dayValue.getIsAvailable())
                                                .setScheduleType(dayValue.getScheduleType())
                                                .setSlotDailySetting(SlotDailySettingDef.newBuilder()
                                                        .setStartTime(dayValue.getSlotDailySetting()
                                                                .getStartTime())
                                                        .setEndTime(dayValue.getSlotDailySetting()
                                                                .getEndTime())
                                                        .setCapacity(dayValue.getSlotDailySetting()
                                                                .getCapacity())
                                                        // .setLimit(bookingLimitation2Def(dayValue.getSlotDailySetting()
                                                        //         .getLimit()))
                                                        .addAllLimitationGroups(
                                                                bookingLimitation2Def(dayValue.getSlotDailySetting()
                                                                        .getLimitationGroupsList()))
                                                        .build())
                                                .addAllSlotHourSettingList(
                                                        dayValue.getSlotHourSettingListList().stream()
                                                                .map(StaffServer::slotHours2Def)
                                                                .toList())
                                                .build();
                                    }))
                            .toList();
                    return StaffAvailabilityDef.newBuilder()
                            .setStaffId(staffAvailabilityInfo.getStaffId())
                            .setIsAvailable(staffAvailabilityInfo.getIsAvailable())
                            .setScheduleType(staffAvailabilityInfo.getScheduleType())
                            .addAllSlotAvailabilityDayList(slotDayList)
                            .build();
                })
                .toList();

        if (!CollectionUtils.isEmpty(request.getStaffAvailabilityListList())) {
            staffList = request.getStaffAvailabilityListList();
        }

        staffService.updateStaffAvailability(
                com.moego.idl.service.organization.v1.UpdateStaffAvailabilityRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .addAllStaffAvailabilityList(staffList)
                        .build());
        responseObserver.onNext(UpdateStaffAvailabilityResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteStaffAvailabilityOverride(
            DeleteStaffAvailabilityOverrideParams request,
            StreamObserver<DeleteStaffAvailabilityOverrideResult> responseObserver) {
        var builder = DeleteStaffAvailabilityOverrideRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setStaffId(request.getStaffId())
                .addAllOverrideDays(request.getOverrideDaysList());
        if (request.hasAvailabilityType()) {
            builder.setAvailabilityType(request.getAvailabilityType());
        }
        staffService.deleteStaffAvailabilityOverride(builder.build());

        responseObserver.onNext(
                DeleteStaffAvailabilityOverrideResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateStaffAvailabilityOverride(
            UpdateStaffAvailabilityOverrideParams request,
            StreamObserver<UpdateStaffAvailabilityResult> responseObserver) {
        var slotDays = request.getOverrideDaysList().stream()
                .map(overrideDay -> SlotAvailabilityDayDef.newBuilder()
                        .setDayOfWeek(DayOfWeek.MONDAY) // override没有意义，默认为1d
                        .setIsAvailable(overrideDay.getIsAvailable())
                        .setScheduleType(ScheduleType.forNumber(4)) // override没有意义，默认为4周
                        .setSlotDailySetting(SlotDailySettingDef.newBuilder()
                                .setStartTime(overrideDay.getSlotDailySetting().getStartTime())
                                .setEndTime(overrideDay.getSlotDailySetting().getEndTime())
                                .setCapacity(overrideDay.getSlotDailySetting().getCapacity())
                                .addAllLimitationGroups(bookingLimitation2Def(
                                        overrideDay.getSlotDailySetting().getLimitationGroupsList())))
                        .addAllSlotHourSettingList(overrideDay.getSlotHourSettingListList().stream()
                                .map(StaffServer::slotHours2Def)
                                .toList())
                        .setOverrideDate(overrideDay.getOverrideDate())
                        .build())
                .toList();

        if (request.getSlotOverrideDaysCount() > 0) {
            slotDays = request.getSlotOverrideDaysList();
        }

        staffService.updateStaffAvailabilityOverride(
                com.moego.idl.service.organization.v1.UpdateStaffAvailabilityOverrideRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .setStaffId(request.getStaffId())
                        .addAllOverrideDays(slotDays)
                        .addAllTimeOverrideDays(request.getTimeOverrideDaysList())
                        .build());
        responseObserver.onNext(UpdateStaffAvailabilityResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getStaffAvailabilityOverride(
            GetStaffAvailabilityOverrideParams request,
            StreamObserver<GetStaffAvailabilityOverrideResult> responseObserver) {
        var builder = GetStaffAvailabilityOverrideRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId());
        if (request.hasAvailabilityType()) {
            builder.setAvailabilityType(request.getAvailabilityType());
        }

        var staffIdList = request.getStaffIdListList();
        if (CollectionUtils.isEmpty(staffIdList)) {
            staffIdList = staffService
                    .getStaffsByWorkingLocationIds(GetStaffsByWorkingLocationIdsRequest.newBuilder()
                            .addBusinessIds(request.getBusinessId())
                            .setTokenCompanyId(AuthContext.get().companyId())
                            .setTokenStaffId(AuthContext.get().staffId())
                            .build())
                    .getLocationStaffsList()
                    .stream()
                    .filter(ls -> Objects.equals(ls.getBusinessId(), request.getBusinessId()))
                    .map(LocationStaffsDef::getStaffsList)
                    .flatMap(List::stream)
                    .map(StaffModel::getId)
                    .toList();
        }
        builder.addAllStaffIds(staffIdList);

        var response = staffService.getStaffAvailabilityOverride(builder.build());

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        var respBuilder = GetStaffAvailabilityOverrideResult.newBuilder();
        response.getOverrideDaysMap().forEach((staffId, days) -> {
            // 将 staffIdDaysMap 分为 ongoing 和 history 两组
            List<SlotAvailabilityDay> ongoingDays = new ArrayList<>();
            List<SlotAvailabilityDay> historyDays = new ArrayList<>();
            for (var day : days.getSlotsList()) {
                LocalDate overrideDate = LocalDate.parse(day.getOverrideDate()); // 假设 overrideDate 是字符串格式如 "YYYY-MM-DD"
                if (!overrideDate.isBefore(currentDate)) {
                    ongoingDays.add(day); // 进行中的日程（包括今天和未来的日期）
                } else {
                    historyDays.add(day); // 历史记录（过去的日期）
                }
            }
            respBuilder.putStaffMap(
                    staffId,
                    SlotAvailabilityDayOngoingHistoryList.newBuilder()
                            .addAllOngoing(ongoingDays)
                            .addAllHistory(historyDays)
                            .build());
        });
        response.getTimeOverrideDaysMap().forEach((staffId, days) -> {
            // 将 staffIdDaysMap 分为 ongoing 和 history 两组
            List<TimeAvailabilityDay> ongoingDays = new ArrayList<>();
            List<TimeAvailabilityDay> historyDays = new ArrayList<>();
            for (var day : days.getSlotsList()) {
                LocalDate overrideDate = LocalDate.parse(day.getOverrideDate()); // 假设 overrideDate 是字符串格式如 "YYYY-MM-DD"
                if (!overrideDate.isBefore(currentDate)) {
                    ongoingDays.add(day); // 进行中的日程（包括今天和未来的日期）
                } else {
                    historyDays.add(day); // 历史记录（过去的日期）
                }
            }
            respBuilder.putTimeStaffMap(
                    staffId,
                    TimeAvailabilityDayOngoingHistoryList.newBuilder()
                            .addAllOngoing(ongoingDays)
                            .addAllHistory(historyDays)
                            .build());
        });

        // 填充空白数据
        if (!request.hasAvailabilityType() || request.getAvailabilityType() == AvailabilityType.BY_SLOT) {
            staffIdList.forEach(staffId -> {
                if (!respBuilder.getStaffMapMap().containsKey(staffId)) {
                    respBuilder.putStaffMap(
                            staffId,
                            SlotAvailabilityDayOngoingHistoryList.newBuilder()
                                    .addAllHistory(List.of())
                                    .addAllOngoing(List.of())
                                    .build());
                }
            });
        }
        if (!request.hasAvailabilityType() || request.getAvailabilityType() == AvailabilityType.BY_TIME) {
            staffIdList.forEach(staffId -> {
                if (!respBuilder.getTimeStaffMapMap().containsKey(staffId)) {
                    respBuilder.putTimeStaffMap(
                            staffId,
                            TimeAvailabilityDayOngoingHistoryList.newBuilder()
                                    .addAllHistory(List.of())
                                    .addAllOngoing(List.of())
                                    .build());
                }
            });
        }

        responseObserver.onNext(respBuilder.build());
        responseObserver.onCompleted();
    }
}
