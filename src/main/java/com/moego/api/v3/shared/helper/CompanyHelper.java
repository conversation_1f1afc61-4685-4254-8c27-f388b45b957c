package com.moego.api.v3.shared.helper;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.CompanyModel;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.ListCompanyRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/3/12
 */
@Component
@RequiredArgsConstructor
public class CompanyHelper {

    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyStub;

    /**
     * Get {@link LocalDateTime} in company's timezone, throw exception if company not found.
     *
     * @param companyId company id
     * @return LocalDateTime in company's timezone
     */
    public LocalDateTime mustGetCompanyDateTime(long companyId) {
        var resp = companyStub.getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                .setCompanyId(companyId)
                .build());
        if (!resp.hasPreferenceSetting()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Company preference setting not found: " + companyId);
        }
        var timezone = resp.getPreferenceSetting().getTimeZone().getName();
        return getLocalDateTime(Instant.now(), timezone);
    }

    /*private*/ static LocalDateTime getLocalDateTime(Instant now, String timezone) {
        return now.atZone(ZoneId.of(timezone)).toLocalDateTime();
    }

    /**
     * Must get company by id, throw exception if not found
     *
     * @param companyId company id
     * @return company
     */
    public CompanyModel mustGetCompany(long companyId) {
        var companies = companyStub
                .listCompany(ListCompanyRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1)
                                .build())
                        .build())
                .getCompaniesList();
        if (companies.isEmpty()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company not found: " + companyId);
        }
        return companies.get(0);
    }
}
