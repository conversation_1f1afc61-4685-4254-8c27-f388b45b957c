package com.moego.svc.appointment.mapper.pg;

import static com.moego.svc.appointment.mapper.pg.CheckInOutAlertSettingsDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.CheckInOutAlertSettings;
import com.moego.svc.appointment.mapper.typehandler.CheckInAlertSettingsHandler;
import com.moego.svc.appointment.mapper.typehandler.CheckOutAlertSettingsHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface CheckInOutAlertSettingsMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<CheckInOutAlertSettingsMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, checkIn, checkOut, createdAt, updatedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<CheckInOutAlertSettings> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<CheckInOutAlertSettings> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="CheckInOutAlertSettingsResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="check_in", property="checkIn", typeHandler=CheckInAlertSettingsHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="check_out", property="checkOut", typeHandler=CheckOutAlertSettingsHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<CheckInOutAlertSettings> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("CheckInOutAlertSettingsResult")
    Optional<CheckInOutAlertSettings> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, checkInOutAlertSettings, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, checkInOutAlertSettings, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default int insertMultiple(Collection<CheckInOutAlertSettings> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, checkInOutAlertSettings, c ->
            c.map(companyId).toProperty("companyId")
            .map(checkIn).toProperty("checkIn")
            .map(checkOut).toProperty("checkOut")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default int insertSelective(CheckInOutAlertSettings row) {
        return MyBatis3Utils.insert(this::insert, row, checkInOutAlertSettings, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(checkIn).toPropertyWhenPresent("checkIn", row::getCheckIn)
            .map(checkOut).toPropertyWhenPresent("checkOut", row::getCheckOut)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default Optional<CheckInOutAlertSettings> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, checkInOutAlertSettings, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default List<CheckInOutAlertSettings> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, checkInOutAlertSettings, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default List<CheckInOutAlertSettings> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, checkInOutAlertSettings, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default Optional<CheckInOutAlertSettings> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, checkInOutAlertSettings, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    static UpdateDSL<UpdateModel> updateAllColumns(CheckInOutAlertSettings row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(checkIn).equalTo(row::getCheckIn)
                .set(checkOut).equalTo(row::getCheckOut)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(CheckInOutAlertSettings row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(checkIn).equalToWhenPresent(row::getCheckIn)
                .set(checkOut).equalToWhenPresent(row::getCheckOut)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    default int updateByPrimaryKeySelective(CheckInOutAlertSettings row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(checkIn).equalToWhenPresent(row::getCheckIn)
            .set(checkOut).equalToWhenPresent(row::getCheckOut)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}