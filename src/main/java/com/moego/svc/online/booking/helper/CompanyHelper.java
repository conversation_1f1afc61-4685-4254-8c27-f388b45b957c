package com.moego.svc.online.booking.helper;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.CompanyModel;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc.CompanyServiceBlockingStub;
import com.moego.idl.service.organization.v1.ListCompanyRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyHelper {

    private final CompanyServiceBlockingStub companyService;

    /**
     * Must get company by id, throw exception if not found
     *
     * @param companyId company id
     * @return company
     */
    public CompanyModel mustGetCompany(long companyId) {
        var companies = companyService
                .listCompany(ListCompanyRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1)
                                .build())
                        .build())
                .getCompaniesList();
        if (companies.isEmpty()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company not found: " + companyId);
        }
        return companies.get(0);
    }
}
