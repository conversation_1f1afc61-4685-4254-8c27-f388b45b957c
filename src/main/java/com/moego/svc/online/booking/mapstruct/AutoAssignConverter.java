package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AutoAssignConverter {

    AutoAssignConverter INSTANCE = Mappers.getMapper(AutoAssignConverter.class);

    default GetAutoAssignResponse.AssignRequire buildAssignRequire(BoardingServiceDetailModel model) {
        if (Objects.isNull(model)) {
            return null;
        }

        return GetAutoAssignResponse.AssignRequire.newBuilder()
                .setPetId(model.getPetId())
                .setServiceId(model.getServiceId())
                .setStartDate(model.getStartDate())
                .setEndDate(model.getEndDate())
                .setServiceItemType(ServiceItemType.BOARDING)
                .build();
    }

    default GetAutoAssignResponse.AssignRequire buildAssignRequire(DaycareServiceDetailModel model) {
        if (Objects.isNull(model)) {
            return null;
        }

        return GetAutoAssignResponse.AssignRequire.newBuilder()
                .setPetId(model.getPetId())
                .setServiceId(model.getServiceId())
                .addAllSpecificDates(model.getSpecificDatesList())
                .setServiceItemType(ServiceItemType.DAYCARE)
                .build();
    }

    default List<GetAutoAssignResponse.AssignRequire> buildAssignRequire(
            List<BoardingServiceDetailModel> boardingModel, List<DaycareServiceDetailModel> daycareModel) {
        List<GetAutoAssignResponse.AssignRequire> assignRequires = new ArrayList<>();
        if (!CollectionUtils.isEmpty(boardingModel)) {
            assignRequires.addAll(
                    boardingModel.stream().map(this::buildAssignRequire).toList());
        }

        if (!CollectionUtils.isEmpty(daycareModel)) {
            assignRequires.addAll(
                    daycareModel.stream().map(this::buildAssignRequire).toList());
        }
        return assignRequires;
    }

    @Nullable
    default GetAutoAssignResponse.AssignRequire evaluationToAssignRequire(
            EvaluationTestDetailModel model, Map<Long, EvaluationBriefView> evaluations) {
        var evaluation = evaluations.get(model.getEvaluationId());
        if (evaluation == null || !evaluation.getAllowStaffAutoAssign()) {
            return null;
        }
        return GetAutoAssignResponse.AssignRequire.newBuilder()
                .setPetId(model.getPetId())
                .setServiceId(model.getEvaluationId())
                .build();
    }

    default List<GetAutoAssignResponse.AssignRequire> evaluationToAssignRequire(
            List<EvaluationTestDetailModel> model, Map<Long, EvaluationBriefView> evaluations) {
        if (CollectionUtils.isEmpty(model)) {
            return List.of();
        }
        return model.stream()
                .map(k -> evaluationToAssignRequire(k, evaluations))
                .filter(Objects::nonNull)
                .toList();
    }

    default GetAutoAssignResponse.LodgingDetail buildAutoAssignLodgingDetail(
            Long lodgingUnitId,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        LodgingUnitModel lodgingUnit = lodgingUnitMap.get(lodgingUnitId);
        LodgingTypeModel lodgingType = lodgingUnit != null ? lodgingTypeMap.get(lodgingUnit.getLodgingTypeId()) : null;
        return GetAutoAssignResponse.LodgingDetail.newBuilder()
                .setLodgingId(lodgingUnitId)
                .setLodgingUnitName(lodgingUnit != null ? lodgingUnit.getName() : "")
                .setLodgingTypeName(lodgingType != null ? lodgingType.getName() : "")
                .build();
    }

    default List<GetAutoAssignResponse.LodgingDetail> buildAutoAssignLodgingDetail(
            List<Long> lodgingUnitIds,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        if (CollectionUtils.isEmpty(lodgingUnitIds)) {
            return List.of();
        }
        return lodgingUnitIds.stream()
                .map(id -> buildAutoAssignLodgingDetail(id, lodgingUnitMap, lodgingTypeMap))
                .toList();
    }
}
