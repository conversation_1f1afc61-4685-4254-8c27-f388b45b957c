package com.moego.svc.online.booking.service;

import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.PetToLodgingDef;
import com.moego.idl.models.online_booking.v1.PetToStaffDef;
import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.online.booking.mapstruct.AutoAssignConverter;
import com.moego.svc.online.booking.utils.LodgingUtils;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AutoAssignService {
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub businessPetSizeServiceClient;

    public Map<Long, BusinessCustomerPetInfoModel> getPetMap(long companyId, List<Long> petIdList) {
        if (CollectionUtils.isEmpty(petIdList)) {
            return Map.of();
        }

        return businessCustomerPetService
                .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                        .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                        .addAllIds(petIdList.stream().distinct().toList())
                        .build())
                .getPetsList()
                .stream()
                .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity()));
    }

    public List<BusinessPetSizeModel> getPetSizeList(long companyId) {
        return businessPetSizeServiceClient
                .listPetSize(com.moego.idl.service.business_customer.v1.ListPetSizeRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getSizesList();
    }

    /**
     * This method is used to automatically assign pets to lodgings.
     *
     * @param bookingRequest  The booking request model containing the details of the booking.
     * @param lodgingTypeList A list of all available lodging types.
     * @param lodgingUnitList A list of all available lodging units.
     * @param serviceMap      A map of service brief views, with service ID as the key.
     * @param petSizeList     A list of all available pet sizes.
     * @param petMap          A map of business customer pet info models, with pet ID as the key.
     * @param assignInfoList  A list of lodging assign info.
     * @return A list of PetToLodging objects, each representing a pet assigned to a lodging.
     */
    public static List<PetToLodgingDef> autoAssign(
            BookingRequestModel bookingRequest,
            List<LodgingTypeModel> lodgingTypeList,
            List<LodgingUnitModel> lodgingUnitList,
            Map<Long, ServiceBriefView> serviceMap,
            List<BusinessPetSizeModel> petSizeList,
            Map<Long, BusinessCustomerPetInfoModel> petMap,
            List<LodgingAssignInfo> assignInfoList,
            boolean excludeBoardingLastDay) {

        var petCntPerLodgingPerDay = LodgingService.calPetCntPerLodgingPerDay(
                bookingRequest.getStartDate(), bookingRequest.getEndDate(), assignInfoList, excludeBoardingLastDay);
        List<GetAutoAssignResponse.AssignRequire> assignRequires = AutoAssignConverter.INSTANCE.buildAssignRequire(
                PetDetailService.getBoardingServiceDetails(bookingRequest),
                PetDetailService.getDaycareServiceDetails(bookingRequest));

        Optional<Long> lodgingIdForAllPets = getOneAvailableLodgingId(
                assignRequires,
                petMap,
                serviceMap,
                petSizeList,
                lodgingTypeList,
                lodgingUnitList,
                petCntPerLodgingPerDay,
                Map.of(),
                excludeBoardingLastDay);
        return lodgingIdForAllPets
                .map(lodgingUnitId -> assignRequires.stream()
                        .map(GetAutoAssignResponse.AssignRequire::getPetId)
                        .distinct()
                        .map(petId -> PetToLodgingDef.newBuilder()
                                .setPetId(petId)
                                .setLodgingUnitId(lodgingUnitId)
                                .build())
                        .toList())
                .orElseGet(() -> assignToDifferentLodging(
                        bookingRequest,
                        petMap,
                        serviceMap,
                        petSizeList,
                        lodgingTypeList,
                        lodgingUnitList,
                        petCntPerLodgingPerDay,
                        excludeBoardingLastDay));
    }

    /**
     * 将宠物分配到不同的 lodging
     */
    private static List<PetToLodgingDef> assignToDifferentLodging(
            BookingRequestModel bookingRequest,
            Map<Long, BusinessCustomerPetInfoModel> petMap,
            Map<Long, ServiceBriefView> serviceMap,
            List<BusinessPetSizeModel> petSizeList,
            List<LodgingTypeModel> lodgingTypeList,
            List<LodgingUnitModel> lodgingUnitList,
            Map<Long, Map<String, Integer>> petCntPerLodgingPerDay,
            boolean excludeBoardingLastDay) {

        List<PetToLodgingDef> result = new ArrayList<>();
        Map<Long, Map<String, Integer>> petTryAllocatePerLodgingPerDay = new HashMap<>();

        petMap.forEach((petId, pet) -> {
            BoardingServiceDetailModel boardingDetail =
                    PetDetailService.getBoardingServiceDetails(bookingRequest).stream()
                            .filter(service -> service.getPetId() == petId)
                            .findFirst()
                            .orElse(null);
            var assignRequire = AutoAssignConverter.INSTANCE.buildAssignRequire(boardingDetail);

            // boarding service 没找到 pet，继续在 daycare service 中寻找
            if (Objects.isNull(boardingDetail)) {
                DaycareServiceDetailModel daycareDetail =
                        PetDetailService.getDaycareServiceDetails(bookingRequest).stream()
                                .filter(service -> service.getPetId() == petId)
                                .findFirst()
                                .orElse(null);
                assignRequire = AutoAssignConverter.INSTANCE.buildAssignRequire(daycareDetail);
            }

            if (Objects.isNull(assignRequire)) {
                return;
            }

            Optional<Long> lodgingIdForOnePet = getOneAvailableLodgingId(
                    List.of(assignRequire),
                    petMap,
                    serviceMap,
                    petSizeList,
                    lodgingTypeList,
                    lodgingUnitList,
                    petCntPerLodgingPerDay,
                    petTryAllocatePerLodgingPerDay,
                    excludeBoardingLastDay);
            // 该 pet service detail 无法分配 lodging
            if (lodgingIdForOnePet.isEmpty()) {
                return;
            }

            // 记录此次的分配信息
            result.add(PetToLodgingDef.newBuilder()
                    .setPetId(petId)
                    .setLodgingUnitId(lodgingIdForOnePet.get())
                    .build());
            Map<String, Integer> petTryAllocatePerDay =
                    petTryAllocatePerLodgingPerDay.computeIfAbsent(lodgingIdForOnePet.get(), k -> new HashMap<>());
            for (String date : getAssignDates(assignRequire)) {
                petTryAllocatePerDay.merge(date, 1, Integer::sum);
            }
        });
        return result;
    }

    /**
     * 获取一个能同时分配给 assignRequires 的 lodging unit
     *
     * @param assignRequires                 待分配到相同 lodging 的 require 信息
     * @param petCntPerLodgingPerDay         当前已分配的 lodging 信息
     * @param petTryAllocatePerLodgingPerDay 此次已尝试分配的 lodging 信息
     * @return 分配一个合适的 lodging id.
     */
    private static Optional<Long> getOneAvailableLodgingId(
            List<GetAutoAssignResponse.AssignRequire> assignRequires,
            Map<Long, BusinessCustomerPetInfoModel> petMap,
            Map<Long, ServiceBriefView> serviceMap,
            List<BusinessPetSizeModel> petSizeList,
            List<LodgingTypeModel> lodgingTypeList,
            List<LodgingUnitModel> lodgingUnitList,
            Map<Long, Map<String, Integer>> petCntPerLodgingPerDay,
            Map<Long, Map<String, Integer>> petTryAllocatePerLodgingPerDay,
            boolean excludeBoardingLastDay) {

        Map<String, Integer> petCntNeedPerDay =
                LodgingService.calPetCntNeedPerDay(assignRequires, excludeBoardingLastDay);
        if (petCntNeedPerDay == null) {
            return Optional.empty();
        }

        var availableLodgingTypeMap =
                filterLodgingType(lodgingTypeList, assignRequires, petMap, petSizeList, serviceMap).stream()
                        .collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity(), (k1, k2) -> k1));

        var lodgingStatusMap = LodgingUtils.calLodgingStatus(lodgingTypeList, lodgingUnitList, petCntPerLodgingPerDay);
        lodgingUnitList = lodgingUnitList.stream()
                .filter(lodgingUnit -> availableLodgingTypeMap.containsKey(lodgingUnit.getLodgingTypeId()))
                .sorted(Comparator.comparing((LodgingUnitModel lodgingUnit) ->
                                LodgingUtils.LODGING_STATUS_ORDER_MAP.get(lodgingStatusMap.get(lodgingUnit.getId())))
                        .thenComparing(LodgingUnitModel::getId))
                .toList();

        // Get an available lodging
        for (LodgingUnitModel lodgingUnit : lodgingUnitList) {
            var lodgingType = availableLodgingTypeMap.get(lodgingUnit.getLodgingTypeId());
            if (lodgingType == null) {
                continue;
            }

            // Merge allocated and tried lodgings to check if there is enough space
            Map<String, Integer> petAllocatedPerDay =
                    new HashMap<>(petCntPerLodgingPerDay.getOrDefault(lodgingUnit.getId(), Map.of()));
            petTryAllocatePerLodgingPerDay
                    .getOrDefault(lodgingUnit.getId(), Map.of())
                    .forEach((date, cnt) -> petAllocatedPerDay.merge(date, cnt, Integer::sum));

            boolean isLodgingAvailable =
                    LodgingService.isLodgingAvailable(lodgingType, petCntNeedPerDay, petAllocatedPerDay);
            if (isLodgingAvailable) {
                return Optional.of(lodgingUnit.getId());
            }
        }

        return Optional.empty();
    }

    public static List<PetToStaffDef> autoAssignToStaff(
            List<EvaluationTestDetailModel> petEvaluations,
            Map<Long, EvaluationBriefView> evaluations,
            List<StaffBasicView> staffs) {
        return petEvaluations.stream()
                .map(petEvaluation -> autoAssignToStaff(petEvaluation, evaluations, staffs))
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * auto assign staff to evaluation, in case of evaluation allow auto assign
     * @return EvaluationPetToStaffDef if auto assign success, otherwise null
     */
    @Nullable
    public static PetToStaffDef autoAssignToStaff(
            EvaluationTestDetailModel petEvaluation,
            Map<Long, EvaluationBriefView> evaluations,
            List<StaffBasicView> staffs) {

        var evaluation = evaluations.get(petEvaluation.getEvaluationId());
        if (evaluation == null) {
            return null;
        }

        // 不允许自动分配
        if (!evaluation.getAllowStaffAutoAssign()) {
            return null;
        }

        // 按 evaluation 配置过滤 staff
        if (!evaluation.getIsAllStaff()) {
            staffs = staffs.stream()
                    .filter(k -> evaluation.getAllowedStaffListList().contains(k.getId()))
                    .toList();
        }

        return staffs.stream()
                .max(Comparator.comparing(StaffBasicView::getSort))
                .map(staff -> PetToStaffDef.newBuilder()
                        .setPetId(petEvaluation.getPetId())
                        .setServiceId(petEvaluation.getEvaluationId())
                        .setStaffId(staff.getId())
                        .build())
                .orElse(null);
    }

    public static List<LodgingTypeModel> filterLodgingType(
            List<LodgingTypeModel> lodgingTypeList,
            List<GetAutoAssignResponse.AssignRequire> roomAssignRequires,
            Map<Long, BusinessCustomerPetInfoModel> petMap,
            List<BusinessPetSizeModel> petSizeList,
            Map<Long, ServiceBriefView> serviceMap) {
        Set<Long> petIds = new HashSet<>();
        Set<Long> serviceIds = new HashSet<>();
        for (var roomRequire : roomAssignRequires) {
            petIds.add(roomRequire.getPetId());
            serviceIds.add(roomRequire.getServiceId());
        }

        // 按 pet 过滤 lodging
        for (Long petId : petIds) {
            BusinessCustomerPetInfoModel pet = petMap.get(petId);
            if (pet == null) {
                return new ArrayList<>();
            }
            Long petSizeId = PetDetailService.getPetSizeId(pet.getWeight(), petSizeList);
            lodgingTypeList = LodgingService.filterLodgingTypeByPetSize(lodgingTypeList, petSizeId);
        }

        // 按 service 过滤 lodging
        for (Long serviceId : serviceIds) {
            ServiceBriefView service = serviceMap.get(serviceId);
            if (service == null) {
                return new ArrayList<>();
            }
            lodgingTypeList = LodgingService.filterLodgingTypeByService(lodgingTypeList, service);
        }
        return lodgingTypeList;
    }

    private static List<String> getAssignDates(GetAutoAssignResponse.AssignRequire assignRequire) {
        if (Objects.isNull(assignRequire)) {
            return List.of();
        }

        if (assignRequire.hasStartDate() && assignRequire.hasEndDate()) {
            return DateUtil.generateAllDatesBetween(assignRequire.getStartDate(), assignRequire.getEndDate());
        } else if (!CollectionUtils.isEmpty(assignRequire.getSpecificDatesList())) {
            return assignRequire.getSpecificDatesList();
        }

        return List.of();
    }
}
