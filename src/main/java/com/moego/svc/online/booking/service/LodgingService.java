package com.moego.svc.online.booking.service;

import static com.moego.lib.featureflag.features.FeatureFlags.ENABLE_NEW_LODGING_OCCUPANCY;

import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.LodgingAssignAppointmentInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetEvaluationInfo;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.LodgingAssignInfoRequest;
import com.moego.idl.service.appointment.v1.LodgingServiceGrpc;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.LodgingTypeServiceGrpc;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.MGetLodgingTypeRequest;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.svc.online.booking.helper.CompanyHelper;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class LodgingService {
    private final LodgingTypeServiceGrpc.LodgingTypeServiceBlockingStub lodgingTypeClient;
    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitClient;
    private final LodgingServiceGrpc.LodgingServiceBlockingStub lodgingClient;
    private final FeatureFlagApi featureFlagApi;
    private final CompanyHelper companyHelper;

    /**
     * 收集新增宠物寄养信息
     *
     * @return <key: date, value: petCnt>。时间信息不完整时，返回 null
     */
    @Nullable
    public static Map<String, Integer> calPetCntNeedPerDay(
            List<GetAutoAssignResponse.AssignRequire> roomAssignRequires, boolean excludeBoardingLastDay) {
        // 计算每一天需要寄养的 pet 数量
        Map<String, Integer> petCntNeedPerDay = new HashMap<>();
        Map<String, Set<Long>> petListNeedPerDay = new HashMap<>();
        if (!CollectionUtils.isEmpty(roomAssignRequires)) {
            for (var roomRequire : roomAssignRequires) {
                String startDate = roomRequire.getStartDate();
                String endDate = roomRequire.getEndDate();
                // 时间信息不完整
                if (!StringUtils.hasText(startDate) || !StringUtils.hasText(endDate)) {
                    if (!CollectionUtils.isEmpty(roomRequire.getSpecificDatesList())) {
                        roomRequire.getSpecificDatesList().forEach(date -> {
                            petListNeedPerDay
                                    .computeIfAbsent(date, k -> new HashSet<>())
                                    .add(roomRequire.getPetId());
                        });
                        continue;
                    } else {
                        return null;
                    }
                }
                LocalDate start = LocalDate.parse(startDate);
                LocalDate end = LocalDate.parse(endDate);
                for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                    // 如果是 boarding 的 last day，且需要排除最后一天，则跳过
                    if (isFilterBoardingCheckoutDay(roomRequire, date.toString(), excludeBoardingLastDay)) {
                        continue;
                    }
                    petListNeedPerDay
                            .computeIfAbsent(date.toString(), k -> new HashSet<>())
                            .add(roomRequire.getPetId());
                }
            }
        }

        petListNeedPerDay.forEach((date, petList) -> petCntNeedPerDay.put(date, petList.size()));
        return petCntNeedPerDay;
    }

    // 按 id 查询 lodging type，包含软删除记录
    public List<LodgingTypeModel> getLodgingTypeByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return lodgingTypeClient
                .mGetLodgingType(
                        MGetLodgingTypeRequest.newBuilder().addAllIdList(idList).build())
                .getLodgingTypeListList();
    }

    public List<LodgingAssignInfo> getLodgingAssignInfo(
            Long companyId, Long businessId, String startDate, String endDate) {
        return lodgingClient
                .lodgingAssignInfo(LodgingAssignInfoRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setStartDate(startDate)
                        .setEndDate(endDate)
                        .build())
                .getLodgingAssignInfoList();
    }

    // return <key: lodgingUnitId, value: <key: date, value: petCnt>>
    public static Map<Long, Map<String, Integer>> calPetCntPerLodgingPerDay(
            String startDate, String endDate, List<LodgingAssignInfo> assignInfoList, boolean excludeBoardingLastDay) {
        // <key: lodgingId, value: <key: date, value: Set<petId>>>
        Map<Long, Map<String, Set<Integer>>> petsPerLodgingPerDay = new HashMap<>();

        // 收集 pet 寄养信息
        Set<String> datesToCollect = new HashSet<>(DateUtil.generateAllDatesBetween(startDate, endDate));
        for (LodgingAssignInfo assignInfo : assignInfoList) {
            for (LodgingAssignAppointmentInfo appointmentInfo : assignInfo.getAppointmentsList()) {
                appointmentInfo
                        .getPetDetailsList()
                        .forEach(k -> collectPetDetailLodgingCnt(
                                assignInfo.getLodgingId(),
                                k,
                                datesToCollect,
                                petsPerLodgingPerDay,
                                excludeBoardingLastDay));

                appointmentInfo
                        .getPetEvaluationsList()
                        .forEach(k -> collectPetEvaluationLodgingCnt(
                                assignInfo.getLodgingId(), k, datesToCollect, petsPerLodgingPerDay));
            }
        }

        Map<Long, Map<String, Integer>> result = new HashMap<>();
        petsPerLodgingPerDay.forEach((lodgingId, petDateMap) -> {
            Map<String, Integer> petCntMap = new HashMap<>();
            petDateMap.forEach((date, petSet) -> petCntMap.put(date, petSet.size()));
            result.put(lodgingId, petCntMap);
        });
        return result;
    }

    // 是否过滤 boarding 的 checkout day
    private static Boolean isFilterBoardingCheckoutDay(
            LodgingAssignPetDetailInfo petDetailInfo, String date, boolean excludeBoardingLastDay) {
        if (!excludeBoardingLastDay || Objects.isNull(petDetailInfo)) {
            return false;
        }
        return Objects.equals(petDetailInfo.getServiceItemType(), ServiceItemType.BOARDING)
                && Objects.equals(date, petDetailInfo.getEndDate());
    }

    // 是否过滤 boarding 的 checkout day
    private static Boolean isFilterBoardingCheckoutDay(
            GetAutoAssignResponse.AssignRequire assignRequire, String date, boolean excludeBoardingLastDay) {
        if (!excludeBoardingLastDay || Objects.isNull(assignRequire)) {
            return false;
        }
        return Objects.equals(assignRequire.getServiceItemType(), ServiceItemType.BOARDING)
                && Objects.equals(date, assignRequire.getEndDate());
    }

    // 收集 pet service detail 占用的 lodging 排期信息
    private static void collectPetDetailLodgingCnt(
            Long lodgingUnitId,
            LodgingAssignPetDetailInfo petDetail,
            Set<String> datesToCollect,
            Map<Long, Map<String, Set<Integer>>> petsPerLodgingPerDay,
            boolean excludeBoardingLastDay) {
        List<String> dates = petDetail.getSpecificDatesList();
        if (CollectionUtils.isEmpty(dates)) {
            dates = DateUtil.generateAllDatesBetween(petDetail.getStartDate(), petDetail.getEndDate());
        }
        for (String date : dates) {
            if (!datesToCollect.contains(date)) {
                continue;
            }

            // 过滤 boarding 的 checkout day
            if (isFilterBoardingCheckoutDay(petDetail, date, excludeBoardingLastDay)) {
                continue;
            }

            petsPerLodgingPerDay
                    .computeIfAbsent(lodgingUnitId, k -> new HashMap<>())
                    .computeIfAbsent(date, k -> new HashSet<>())
                    .add(petDetail.getPetId());
        }
    }

    // 收集 pet service detail 占用的 lodging 排期信息
    private static void collectPetEvaluationLodgingCnt(
            Long lodgingUnitId,
            LodgingAssignPetEvaluationInfo petEvaluation,
            Set<String> allDates,
            Map<Long, Map<String, Set<Integer>>> petsPerLodgingPerDay) {
        List<String> dates = DateUtil.generateAllDatesBetween(petEvaluation.getStartDate(), petEvaluation.getEndDate());
        for (String date : dates) {
            if (!allDates.contains(date)) {
                continue;
            }
            petsPerLodgingPerDay
                    .computeIfAbsent(lodgingUnitId, k -> new HashMap<>())
                    .computeIfAbsent(date, k -> new HashSet<>())
                    .add(petEvaluation.getPetId());
        }
    }

    /**
     * 检查 lodging 容量是否足够寄养
     *
     * @param lodgingType     lodging type
     * @param datePetCntNeed  <key: date, value: petCnt>  每一天需要寄养的宠物数量。 petCnt 为 0 时表示不需要寄养，不检查容量
     * @param datePetCntExist <key: date, value: petCnt>  每一天已经寄养的宠物数量
     * @return 是否有足够的容量寄养
     */
    public static boolean isLodgingAvailable(
            LodgingTypeModel lodgingType, Map<String, Integer> datePetCntNeed, Map<String, Integer> datePetCntExist) {
        for (var entry : datePetCntNeed.entrySet()) {
            String date = entry.getKey();
            Integer needCnt = entry.getValue();
            if (needCnt == 0) {
                continue;
            }

            int petCntExist = datePetCntExist.getOrDefault(date, 0);
            // base on room 的情况下，如果 lodging 已被占用，不管是否已满都不允许再 assign
            if (lodgingType.getLodgingUnitType().equals(LodgingUnitType.ROOM) && petCntExist > 0) {
                return false;
            }

            if (needCnt + petCntExist > lodgingType.getMaxPetNum()) {
                return false;
            }
        }
        return true;
    }

    public static List<LodgingTypeModel> filterLodgingTypeByPetSize(
            List<LodgingTypeModel> lodgingTypeList, Long petSizeId) {
        if (CollectionUtils.isEmpty(lodgingTypeList)) {
            return new ArrayList<>();
        }
        return lodgingTypeList.stream()
                .filter(k -> {
                    if (!k.getPetSizeFilter()) {
                        return true;
                    }
                    if (petSizeId == null) {
                        return false;
                    }
                    return k.getPetSizeIdsList().contains(petSizeId);
                })
                .toList();
    }

    public static List<LodgingTypeModel> filterLodgingTypeByService(
            List<LodgingTypeModel> lodgingTypeList, ServiceBriefView service) {
        if (CollectionUtils.isEmpty(lodgingTypeList)) {
            return new ArrayList<>();
        }
        // daycare service 选中 all lodging types 时，不 auto assign
        if (!service.getLodgingFilter() && service.getServiceItemType() == ServiceItemType.DAYCARE) {
            return List.of();
        } else if (!service.getLodgingFilter()) {
            return lodgingTypeList;
        }
        return lodgingTypeList.stream()
                .filter(k -> service.getCustomizedLodgingsList().contains(k.getId()))
                .toList();
    }

    public List<LodgingTypeModel> getLodgingTypeByUnits(List<LodgingUnitModel> unitList) {
        if (CollectionUtils.isEmpty(unitList)) {
            return List.of();
        }
        return getLodgingTypeByIds(
                unitList.stream().map(LodgingUnitModel::getLodgingTypeId).toList());
    }

    public List<LodgingUnitModel> getLodgingUnit(long companyId, long businessId) {
        GetLodgingUnitListRequest.Builder getLodgingUnitBuilder =
                GetLodgingUnitListRequest.newBuilder().setCompanyId(companyId).setBusinessId(businessId);
        return lodgingUnitClient
                .getLodgingUnitList(getLodgingUnitBuilder.build())
                .getLodgingUnitListList();
    }

    public List<LodgingUnitModel> getLodgingUnitByUnitIds(long companyId, long businessId, Collection<Long> unitIds) {
        GetLodgingUnitListRequest.Builder getLodgingUnitBuilder = GetLodgingUnitListRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addAllUnitIds(unitIds);
        return lodgingUnitClient
                .getLodgingUnitList(getLodgingUnitBuilder.build())
                .getLodgingUnitListList();
    }

    // 根据 feature flag 判断是否启用新的 lodging occupancy 逻辑
    public boolean enableNewLodgingOccupancy(Long companyId) {
        FeatureFlagContext.FeatureFlagContextBuilder builder = FeatureFlagContext.builder();
        builder.company(companyId);
        var company = companyHelper.mustGetCompany(companyId);
        if (CommonUtil.isNormal(company.getEnterpriseId())) {
            builder.enterprise(company.getEnterpriseId());
        }
        return featureFlagApi.isOn(ENABLE_NEW_LODGING_OCCUPANCY, builder.build());
    }
}
