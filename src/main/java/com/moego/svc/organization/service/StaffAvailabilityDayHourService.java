package com.moego.svc.organization.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.type.DayOfWeek;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.online_booking.v1.TimeAvailabilityType;
import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.LimitationGroupDef;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.SlotAvailabilityDayDef;
import com.moego.idl.models.organization.v1.SlotDailySetting;
import com.moego.idl.models.organization.v1.SlotHourSetting;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.idl.models.organization.v1.TimeAvailabilityDayDef;
import com.moego.idl.models.organization.v1.TimeDailySetting;
import com.moego.idl.models.organization.v1.TimeHourSetting;
import com.moego.idl.service.online_booking.v1.GetGroomingServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.svc.organization.entity.DayHourLimit;
import com.moego.svc.organization.entity.DayHourLimitExample;
import com.moego.svc.organization.entity.DayHourLimitGroup;
import com.moego.svc.organization.entity.MoeStaffOverrideDateExample;
import com.moego.svc.organization.entity.MoeStaffWorkingHour;
import com.moego.svc.organization.entity.MoeStaffWorkingHourExample;
import com.moego.svc.organization.entity.StaffAvailabilityDayHour;
import com.moego.svc.organization.entity.StaffAvailabilityDayHourExample;
import com.moego.svc.organization.entity.StaffAvailabilitySlotDay;
import com.moego.svc.organization.entity.StaffAvailabilitySlotDayExample;
import com.moego.svc.organization.entity.StaffAvailabilityTimeDay;
import com.moego.svc.organization.entity.StaffAvailabilityTimeDayExample;
import com.moego.svc.organization.mapper.base.BaseDayHourLimit;
import com.moego.svc.organization.mapper.base.BaseMoeStaffOverrideDate;
import com.moego.svc.organization.mapper.base.BaseMoeStaffWorkingHourMapper;
import com.moego.svc.organization.mapper.base.BaseStaffAvailabilityDayHour;
import com.moego.svc.organization.mapper.base.BaseStaffAvailabilitySlotDay;
import com.moego.svc.organization.mapper.base.BaseStaffAvailabilityTimeDay;
import com.moego.svc.organization.utils.AvailabilityDayHourUtils;
import jakarta.annotation.Nonnull;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class StaffAvailabilityDayHourService {
    private final BaseStaffAvailabilitySlotDay staffAvailabilitySlotDayMapper;
    private final BaseStaffAvailabilityTimeDay staffAvailabilityTimeDayMapper;
    private final BaseStaffAvailabilityDayHour staffAvailabilityDayHourMapper;
    private final BaseMoeStaffOverrideDate staffOverrideDateMapper;
    private final BaseDayHourLimit limitationMapper;
    private final DayHourLimitService limitationService;

    private final com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc
                    .OBAvailabilitySettingServiceBlockingStub
            obAvailabilitySettingService;
    private final OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub obsStaffAvailabilityService;
    private final BaseMoeStaffWorkingHourMapper baseMoeStaffWorkingHourMapper;

    public static final String DEFAULT_TIME =
            "{\"monday\":[{\"startTime\":540,\"endTime\":1140}],\"tuesday\":[{\"startTime\":540,\"endTime\":1140}],\"wednesday\":[{\"startTime\":540,\"endTime\":1140}],\"thursday\":[{\"startTime\":540,\"endTime\":1140}],\"friday\":[{\"startTime\":540,\"endTime\":1140}],\"saturday\":[{\"startTime\":540,\"endTime\":1140}],\"sunday\":[{\"startTime\":540,\"endTime\":1140}]}";

    public List<StaffAvailabilitySlotDay> getStaffAvailabilitySlotDays(
            Long businessId, List<Long> staffIdList, boolean isOverride) {
        StaffAvailabilitySlotDayExample example = new StaffAvailabilitySlotDayExample();
        var criteria = example.createCriteria();
        if (isOverride) {
            // override类型的slotDay 这个字段不为null
            criteria.andBusinessIdEqualTo(businessId).andOverrideDateIsNotNull();
        } else {
            // 正常的slotDay(不是overrideDate类型）
            criteria.andBusinessIdEqualTo(businessId).andOverrideDateIsNull();
        }

        if (staffIdList != null && !staffIdList.isEmpty()) {
            criteria.andStaffIdIn(staffIdList);
        }
        example.setOrderByClause("day_of_week");

        return staffAvailabilitySlotDayMapper.selectByExampleWithBLOBs(example);
    }

    public List<StaffAvailabilityTimeDay> getStaffAvailabilityTimeDays(
            Long businessId, List<Long> staffIdList, boolean isOverride) {
        StaffAvailabilityTimeDayExample example = new StaffAvailabilityTimeDayExample();
        var criteria = example.createCriteria().andBusinessIdEqualTo(businessId);
        if (isOverride) {
            // override 类型的 timeDay 这个字段不为 null
            criteria.andOverrideDateIsNotNull();
        } else {
            // 正常的 timeDay(不是overrideDate类型）
            criteria.andOverrideDateIsNull();
        }

        if (staffIdList != null && !staffIdList.isEmpty()) {
            criteria.andStaffIdIn(staffIdList);
        }
        example.setOrderByClause("day_of_week");

        return staffAvailabilityTimeDayMapper.selectByExampleWithBLOBs(example);
    }

    public void initSlotDays(
            Long companyId,
            Long businessId,
            Long staffId,
            Map<String, StaffWorkingHourStartEndTime> workingHourStartEndTimeMap) {
        // 查找ob slot相关的配置，如果ob配置的是by time，返回空map
        var slotHoursMap = getOBSlotHoursMap(companyId, businessId, staffId);

        initSlotDaysByStaffWorkingHoursAndOBSettings(
                companyId, businessId, staffId, workingHourStartEndTimeMap, slotHoursMap);
    }

    public void initTimeDays(
            Long companyId, Long businessId, Long staffId, WorkingHoursSetting workingHourStartEndTimeMap) {
        // 查找ob slot相关的配置，如果ob配置的是by time，返回空map
        var slotHoursMap = getOBTimeHoursMap(companyId, businessId, staffId);

        initTimeDaysByStaffWorkingHoursAndOBSettings(
                companyId, businessId, staffId, workingHourStartEndTimeMap, slotHoursMap);
    }

    private static int weekday2Index(String weekDayName) {
        // 将输入的星期名称转换为小写
        String lowerCaseWeekDayName = weekDayName.toLowerCase();
        return switch (lowerCaseWeekDayName) {
            case "monday" -> 1;
            case "tuesday" -> 2;
            case "wednesday" -> 3;
            case "thursday" -> 4;
            case "friday" -> 5;
            case "saturday" -> 6;
            case "sunday" -> 7;
            default -> -1;
        };
    }

    public static String dayOfWeek2String(DayOfWeek dayOfWeek) {
        return switch (dayOfWeek.getNumber()) {
            case 1 -> "monday";
            case 2 -> "tuesday";
            case 3 -> "wednesday";
            case 4 -> "thursday";
            case 5 -> "friday";
            case 6 -> "saturday";
            case 7 -> "sunday";
            default -> throw new IllegalArgumentException("invalid day of week:" + dayOfWeek);
        };
    }

    public record StaffWorkingHourStartEndTime(int minStartTime, int maxEndTime) {}

    public static Map<String, List<Map<String, Integer>>> decodeWeekJson(String jsonString) {
        Gson gson = new Gson();
        TypeToken<Map<String, List<Map<String, Integer>>>> typeToken = new TypeToken<>() {};
        return gson.fromJson(jsonString, typeToken.getType());
    }

    public record WorkingHoursSetting(
            ScheduleType scheduleType,
            LocalDate startDateByTime,
            Map<String, StaffWorkingHourStartEndTime> workingHourStartEndTimeMapMap,
            Map<String, List<Map<String, Integer>>> timeMap) {}

    @NotNull
    public WorkingHoursSetting getStaffWorkingHour(Long businessId, Long staffId) {
        // moe_staff_working_hour
        MoeStaffWorkingHourExample timeDayExample = new MoeStaffWorkingHourExample();
        var criteria = timeDayExample.createCriteria();
        criteria.andBusinessIdEqualTo(businessId.intValue());
        criteria.andStaffIdEqualTo(staffId.intValue());
        var timeDaySettings = baseMoeStaffWorkingHourMapper.selectByExampleWithBLOBs(timeDayExample);
        // 通过timeDaySetting创建map，key为string: schedule_type+dayOfWeek
        Map<String, StaffWorkingHourStartEndTime> timeDayMap = new HashMap<>();
        Map<String, List<Map<String, Integer>>> timeMap = new HashMap<>();

        MoeStaffWorkingHour staffTimeDaySetting;
        if (CollectionUtils.isEmpty(timeDaySettings)) {
            staffTimeDaySetting = new MoeStaffWorkingHour();
            staffTimeDaySetting.setScheduleType((byte) 1);
            staffTimeDaySetting.setFirstWeek(DEFAULT_TIME);
            staffTimeDaySetting.setSecondWeek(DEFAULT_TIME);
            staffTimeDaySetting.setThirdWeek(DEFAULT_TIME);
            staffTimeDaySetting.setForthWeek(DEFAULT_TIME);
        } else {
            staffTimeDaySetting = timeDaySettings.get(0);
        }

        // 从by time取start date
        var startDateByTimeStr = staffTimeDaySetting.getStartDate();
        if (startDateByTimeStr == null) {
            startDateByTimeStr = LocalDate.now().toString();
        }
        LocalDate startDate = LocalDate.parse(startDateByTimeStr);

        var scheduleTypeByte = staffTimeDaySetting.getScheduleType();
        var scheduleType = ScheduleType.forNumber(scheduleTypeByte);

        var firstWeek = staffTimeDaySetting.getFirstWeek();
        var firstWeekJson = decodeWeekJson(firstWeek);
        timeWeeklyJsonPutDayStartEndTime2Map(1, firstWeekJson, timeDayMap);
        timeWeeklyJsonPutDayTime2Map(1, firstWeekJson, timeMap);

        var secondWeek = staffTimeDaySetting.getSecondWeek();
        var secondWeekJson = decodeWeekJson(secondWeek);
        timeWeeklyJsonPutDayStartEndTime2Map(2, secondWeekJson, timeDayMap);
        timeWeeklyJsonPutDayTime2Map(2, secondWeekJson, timeMap);

        var thirdWeek = staffTimeDaySetting.getThirdWeek();
        var thirdWeekJson = decodeWeekJson(thirdWeek);
        timeWeeklyJsonPutDayStartEndTime2Map(3, thirdWeekJson, timeDayMap);
        timeWeeklyJsonPutDayTime2Map(3, thirdWeekJson, timeMap);

        var fourthWeek = staffTimeDaySetting.getThirdWeek();
        var fourthWeekJson = decodeWeekJson(fourthWeek);
        timeWeeklyJsonPutDayStartEndTime2Map(4, fourthWeekJson, timeDayMap);
        timeWeeklyJsonPutDayTime2Map(4, fourthWeekJson, timeMap);

        return new WorkingHoursSetting(scheduleType, startDate, timeDayMap, timeMap);
    }

    private static void timeWeeklyJsonPutDayStartEndTime2Map(
            int weekIndex,
            Map<String, List<Map<String, Integer>>> weeklyJson,
            Map<String, StaffWorkingHourStartEndTime> timeDayMap) {
        for (Map.Entry<String, List<Map<String, Integer>>> entry : weeklyJson.entrySet()) {
            var weekDay = entry.getKey(); // monday
            // 找到一天中最开始和最晚的时间
            int minStartTime = 60 * 24;
            int maxEndTime = 0;
            if (Objects.isNull(entry.getValue())) {
                timeDayMap.put(
                        // week+weekDay
                        getSlotDayKey(weekIndex, weekday2Index(weekDay)), new StaffWorkingHourStartEndTime(0, 0));
                continue;
            }
            for (Map<String, Integer> map : entry.getValue()) {
                var startTime = map.get("startTime");
                if (startTime != null && startTime < minStartTime) {
                    minStartTime = startTime;
                }
                var endTime = map.get("endTime");
                if (endTime != null && endTime > maxEndTime) {
                    maxEndTime = endTime;
                }
            }
            if (minStartTime >= maxEndTime) {
                // 错误的值，初始化为0，在后面会弃用
                minStartTime = 0;
                maxEndTime = 0;
            }
            timeDayMap.put(
                    // week+weekDay
                    getSlotDayKey(weekIndex, weekday2Index(weekDay)),
                    new StaffWorkingHourStartEndTime(minStartTime, maxEndTime));
        }
    }

    private static void timeWeeklyJsonPutDayTime2Map(
            int weekIndex,
            Map<String, List<Map<String, Integer>>> weeklyJson,
            Map<String, List<Map<String, Integer>>> timeDayMap) {
        for (Map.Entry<String, List<Map<String, Integer>>> entry : weeklyJson.entrySet()) {
            var weekDay = entry.getKey(); // monday
            if (Objects.isNull(entry.getValue())) {
                timeDayMap.put(
                        // week+weekDay
                        getSlotDayKey(weekIndex, weekday2Index(weekDay)), List.of());
                continue;
            }
            timeDayMap.put(
                    // week+weekDay
                    getSlotDayKey(weekIndex, weekday2Index(weekDay)), entry.getValue());
        }
    }

    // 初始化4周的slotDay
    private void initSlotDaysByStaffWorkingHoursAndOBSettings(
            Long companyId,
            Long businessId,
            Long staffId,
            Map<String, StaffWorkingHourStartEndTime> staffWorkingHourStartEndTime,
            Map<String, List<SlotHourSetting>> obSlotHoursMap) {
        // 遍历scheduleType的枚举，生成week的记录
        Arrays.stream(ScheduleType.values()).forEach(scheduleType -> {
            if (scheduleType == ScheduleType.SCHEDULE_TYPE_UNSPECIFIED || scheduleType == ScheduleType.UNRECOGNIZED) {
                return;
            }
            // 遍历weekday的记录
            Arrays.stream(DayOfWeek.values()).forEach(dayOfWeek -> {
                if (dayOfWeek == DayOfWeek.DAY_OF_WEEK_UNSPECIFIED || dayOfWeek == DayOfWeek.UNRECOGNIZED) {
                    return;
                }
                int startTime = 9 * 60;
                int endTime = 19 * 60;
                int capacity = 1;
                boolean isAvailable = false;
                // 查找input pb对应的timeDayMap中有没有对应的Day的数据数据，如果有，把daily setting的时间配置上
                var startEndTime = getStartEndTimeByTimeSettings(staffWorkingHourStartEndTime, scheduleType, dayOfWeek);
                if (startEndTime.minStartTime() != 0) {
                    startTime = startEndTime.minStartTime();
                    endTime = startEndTime.maxEndTime;
                    isAvailable = true;
                }

                var slotDay = StaffAvailabilitySlotDay.builder()
                        .companyId(companyId)
                        .businessId(businessId)
                        .staffId(staffId)
                        // .overrideDate(LocalDate.now()) // 初始化不需要overrideDate
                        .isAvailable(isAvailable)
                        .scheduleType(scheduleType.getNumber())
                        .dayOfWeek(dayOfWeek.getNumber())
                        .startTime(startTime)
                        .endTime(endTime)
                        .capacity(capacity)
                        .limitIds(List.of()) // slotDay的limit暂时不初始化
                        .createdAt(LocalDateTime.now())
                        .updatedAt(LocalDateTime.now())
                        .build();
                staffAvailabilitySlotDayMapper.insert(slotDay);

                var slotDayKey = getStaffSlotDayKey(staffId, scheduleType, dayOfWeek);
                if (obSlotHoursMap.containsKey(slotDayKey)) {
                    // 说明ob有对应这天的配置
                    var slotHours = obSlotHoursMap.get(slotDayKey);
                    addSlotHoursByOB(slotHours, slotDay);
                } else {
                    // 插入一条capacity为1的值到数据库
                    insertDemoDayHour(slotDay, startTime);
                }
            });
        });
    }

    // 初始化4周的 timeDay
    private void initTimeDaysByStaffWorkingHoursAndOBSettings(
            Long companyId,
            Long businessId,
            Long staffId,
            WorkingHoursSetting workingHoursSetting,
            Map<String, TimeDailySetting> obSlotHoursMap) {
        // 遍历scheduleType的枚举，生成week的记录
        Arrays.stream(ScheduleType.values()).forEach(scheduleType -> {
            if (scheduleType == ScheduleType.SCHEDULE_TYPE_UNSPECIFIED || scheduleType == ScheduleType.UNRECOGNIZED) {
                return;
            }
            // 遍历weekday的记录
            Arrays.stream(DayOfWeek.values()).forEach(dayOfWeek -> {
                if (dayOfWeek == DayOfWeek.DAY_OF_WEEK_UNSPECIFIED || dayOfWeek == DayOfWeek.UNRECOGNIZED) {
                    return;
                }

                boolean isAvailable = false;
                var currentDayTimeMap = workingHoursSetting
                        .timeMap()
                        .getOrDefault(getSlotDayKey(scheduleType.getNumber(), dayOfWeek.getNumber()), List.of());
                if (!CollectionUtils.isEmpty(currentDayTimeMap)) {
                    isAvailable = true;
                }

                List<Long> limitIds = List.of();
                var slotDayKey = getStaffSlotDayKey(staffId, ScheduleType.ONE_WEEK, dayOfWeek);
                // 应用到 sm 配置的每一周
                if (obSlotHoursMap.containsKey(slotDayKey)
                        && scheduleType.getNumber()
                                <= workingHoursSetting.scheduleType().getNumber()) {
                    // 说明ob有对应这天的配置
                    var slotHours = obSlotHoursMap.get(slotDayKey);
                    if (Objects.nonNull(slotHours)) {
                        // 生成对应的limit ids
                        // var dayHourLimits = AvailabilityDayHourUtils.buildDayHourLimits(slotHours.getLimit());
                        // limitationService.createDayHourLimits(dayHourLimits);
                        // limitIds =
                        //         dayHourLimits.stream().map(DayHourLimit::getId).toList();

                        limitIds = limitationService.batchCreateByLimitation(slotHours.getLimitationGroupsList());
                    }
                }

                var timeDay = StaffAvailabilityTimeDay.builder()
                        .companyId(companyId)
                        .businessId(businessId)
                        .staffId(staffId)
                        // .overrideDate(LocalDate.now()) // 初始化不需要overrideDate
                        .isAvailable(isAvailable)
                        .scheduleType(scheduleType.getNumber())
                        .dayOfWeek(dayOfWeek.getNumber())
                        .limitIds(limitIds)
                        .createdAt(LocalDateTime.now())
                        .updatedAt(LocalDateTime.now())
                        .build();
                staffAvailabilityTimeDayMapper.insertSelective(timeDay);

                currentDayTimeMap.forEach(timeMap -> {
                    var timeHourSetting = StaffAvailabilityDayHour.builder()
                            .dayType(AvailabilityType.BY_TIME_VALUE)
                            .dayId(timeDay.getId())
                            .startTime(timeMap.get("startTime"))
                            .endTime(timeMap.get("endTime"))
                            .capacity(0)
                            .limitIds(List.of())
                            .createdAt(LocalDateTime.now())
                            .updatedAt(LocalDateTime.now())
                            .build();
                    staffAvailabilityDayHourMapper.insertSelective(timeHourSetting);
                });
            });
        });
    }

    private void insertDemoDayHour(StaffAvailabilitySlotDay slotDay, Integer startTime) {
        var slotHourSetting = StaffAvailabilityDayHour.builder()
                .dayType(AvailabilityType.BY_SLOT_VALUE)
                .dayId(slotDay.getId())
                .startTime(startTime) // 和day的start相同
                .endTime(0)
                .capacity(1)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        staffAvailabilityDayHourMapper.insertSelective(slotHourSetting);
    }

    private void addSlotHoursByOB(List<SlotHourSetting> slotHourSettings, StaffAvailabilitySlotDay slotDay) {
        slotHourSettings.forEach(slotHour -> {
            // 插入数据库
            var slotHourSetting = StaffAvailabilityDayHour.builder()
                    .dayType(AvailabilityType.BY_SLOT_VALUE)
                    .dayId(slotDay.getId())
                    .startTime(slotHour.getStartTime())
                    .endTime(0)
                    .capacity(slotHour.getCapacity())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            // 生成对应的limit ids
            // var dayHourLimits = AvailabilityDayHourUtils.buildDayHourLimits(slotHour.getLimit());
            // limitationService.createDayHourLimits(dayHourLimits);
            //
            // var limitIds = dayHourLimits.stream().map(DayHourLimit::getId).collect(Collectors.toList());

            var limitIds = limitationService.batchCreateByLimitation(slotHour.getLimitationGroupsList());

            slotHourSetting.setLimitIds(limitIds);

            staffAvailabilityDayHourMapper.insertSelective(slotHourSetting);
        });
    }

    private Map<String, List<SlotHourSetting>> getOBSlotHoursMap(Long companyId, Long businessId, Long staffId) {
        // 查找ob的设置，根据ob的配置，初始化staff的by slot配置
        var resp = obAvailabilitySettingService.getGroomingServiceAvailability(
                GetGroomingServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build());

        Map<String, List<SlotHourSetting>> slotHourSettingsMap = new HashMap<>();

        var obTimeAvailabilityType = resp.getAvailability().getTimeAvailabilityType();
        if (obTimeAvailabilityType == TimeAvailabilityType.TIME_SLOT) {
            var obAvailabilities =
                    obsStaffAvailabilityService.getStaffAvailability(GetStaffAvailabilityRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setBusinessId(businessId)
                            .addStaffIdList(staffId)
                            .setAvailabilityType(AvailabilityType.BY_SLOT)
                            .build());
            // 遍历OB的配置，将slotHourSetting的列表放入map中
            obAvailabilities.getStaffAvailabilityListList().forEach(obAvailability -> {
                var currentStaffId = obAvailability.getStaffId();
                obAvailability.getSlotAvailabilityDayListList().forEach(slotAvailabilityDay -> {
                    // 周 ob这边没这个配置，默认为1
                    //                    var scheduleType = slotAvailabilityDay.getScheduleType();
                    var scheduleType = ScheduleType.forNumber(1);
                    // 星期几
                    var dayOfWeek = slotAvailabilityDay.getDayOfWeek();
                    slotAvailabilityDay.getSlotHourSettingListList().forEach(slotHourSetting -> {
                        // key为staffId+dayId, value为slotHourSetting的列表
                        var key = getStaffSlotDayKey(currentStaffId, scheduleType, dayOfWeek);
                        if (!slotHourSettingsMap.containsKey(key)) {
                            slotHourSettingsMap.put(key, new ArrayList<>());
                        }
                        slotHourSettingsMap.get(key).add(slotHourSetting);
                    });
                });
            });
        }
        return slotHourSettingsMap;
    }

    private Map<String, TimeDailySetting> getOBTimeHoursMap(Long companyId, Long businessId, Long staffId) {
        // 查找ob的设置，根据ob的配置，初始化staff的by slot配置
        var resp = obAvailabilitySettingService.getGroomingServiceAvailability(
                GetGroomingServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build());

        Map<String, TimeDailySetting> slotHourSettingsMap = new HashMap<>();

        var obTimeAvailabilityType = resp.getAvailability().getTimeAvailabilityType();
        if (obTimeAvailabilityType == TimeAvailabilityType.WORKING_HOUR) {
            var obAvailabilities =
                    obsStaffAvailabilityService.getStaffAvailability(GetStaffAvailabilityRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setBusinessId(businessId)
                            .addStaffIdList(staffId)
                            .setAvailabilityType(AvailabilityType.BY_TIME)
                            .build());
            // 遍历OB的配置，将slotHourSetting的列表放入map中
            obAvailabilities.getStaffAvailabilityListList().forEach(obAvailability -> {
                var currentStaffId = obAvailability.getStaffId();
                obAvailability.getTimeAvailabilityDayListList().forEach(slotAvailabilityDay -> {
                    // 星期几
                    var dayOfWeek = slotAvailabilityDay.getDayOfWeek();
                    var key = getStaffSlotDayKey(currentStaffId, ScheduleType.ONE_WEEK, dayOfWeek);
                    slotHourSettingsMap.put(key, slotAvailabilityDay.getTimeDailySetting());
                });
            });
        }
        return slotHourSettingsMap;
    }

    @NotNull
    private static String getStaffSlotDayKey(
            long currentStaffId, ScheduleType scheduleType, com.google.type.DayOfWeek dayOfWeek) {
        return String.format("%d-%d-%d", currentStaffId, scheduleType.getNumber(), dayOfWeek.getNumber());
    }

    @NotNull
    private static String getSlotDayKey(Integer scheduleType, Integer dayOfWeek) {
        return String.format("%d-%d", scheduleType, dayOfWeek);
    }

    @NotNull
    private StaffWorkingHourStartEndTime getStartEndTimeByTimeSettings(
            Map<String, StaffWorkingHourStartEndTime> staffWorkingHours,
            ScheduleType scheduleType,
            DayOfWeek dayOfWeek) {
        int startTime = 0;
        int endTime = 0;
        var key = getSlotDayKey(scheduleType.getNumber(), dayOfWeek.getNumber());
        var timeDay = staffWorkingHours.get(key);
        if (timeDay != null && (timeDay.minStartTime < timeDay.maxEndTime)) {
            // 如果列表不为空，求出第一条的startTime和最后一条的endTime
            startTime = timeDay.minStartTime;
            endTime = timeDay.maxEndTime;
        }
        return new StaffWorkingHourStartEndTime(startTime, endTime);
    }

    @Transactional
    public void deleteOverrideDays(
            Long businessId, Long staffId, List<String> overrideDays, final AvailabilityType availabilityType) {
        if (Objects.isNull(availabilityType) || availabilityType == AvailabilityType.BY_SLOT) {
            deleteSlotDays(businessId, staffId, overrideDays);
        }
        if (Objects.isNull(availabilityType) || availabilityType == AvailabilityType.BY_TIME) {
            deleteTimeDays(businessId, staffId, overrideDays);
        }
    }

    // [{"endTime": 60, "startTime": 0}]
    private static List<Map<String, Integer>> decodeOverrideJson(String input) {
        if (input == null || input.isEmpty()) {
            return List.of();
        }
        Gson gson = new Gson();
        Type listType = new TypeToken<List<Map<String, Integer>>>() {}.getType();
        return gson.fromJson(input, listType);
    }

    public void initOverrideDates(Long companyId, Long businessId, Long staffId) {
        // 先读旧表的数据
        MoeStaffOverrideDateExample example = new MoeStaffOverrideDateExample();
        var criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(businessId.intValue()).andStaffIdEqualTo(staffId.intValue());
        var staffOverrideDates = staffOverrideDateMapper.selectByExampleWithBLOBs(example);
        LocalDate today = LocalDate.now();
        staffOverrideDates.forEach(overrideDay -> {
            var overrideDate = LocalDate.parse(overrideDay.getOverrideDate());
            if (overrideDate.isBefore(today)) {
                return;
            }
            var overrideTimeJson = decodeOverrideJson(overrideDay.getTimeData());
            // 如果overrideDays是空，说明是不启用的
            if (overrideTimeJson == null || overrideTimeJson.isEmpty()) {
                addOverrideDay(companyId, businessId, staffId, overrideDate, false);
                return;
            }
            addOverrideDay(companyId, businessId, staffId, overrideDate, true);
        });
    }

    public void initTimeOverrideDates(Long companyId, Long businessId, Long staffId) {
        // 先读旧表的数据
        MoeStaffOverrideDateExample example = new MoeStaffOverrideDateExample();
        var criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(businessId.intValue()).andStaffIdEqualTo(staffId.intValue());
        var staffOverrideDates = staffOverrideDateMapper.selectByExampleWithBLOBs(example);
        LocalDate today = LocalDate.now();
        staffOverrideDates.forEach(overrideDay -> {
            var overrideDate = LocalDate.parse(overrideDay.getOverrideDate());
            if (overrideDate.isBefore(today)) {
                return;
            }
            var overrideTimeJson = decodeOverrideJson(overrideDay.getTimeData());
            // 如果overrideDays是空，说明是不启用的
            if (overrideTimeJson == null || overrideTimeJson.isEmpty()) {
                addTimeOverrideDay(companyId, businessId, staffId, overrideDate, false, List.of());
                return;
            }
            addTimeOverrideDay(companyId, businessId, staffId, overrideDate, true, overrideTimeJson);
        });
    }

    public void addOverrideDay(
            Long companyId, Long businessId, Long staffId, LocalDate overrideDate, boolean isAvailable) {
        // 创建Day数据
        var slotDay = StaffAvailabilitySlotDay.builder()
                .companyId(companyId)
                .businessId(businessId)
                .staffId(staffId)
                .overrideDate(overrideDate)
                .isAvailable(isAvailable)
                .scheduleType(ScheduleType.SCHEDULE_TYPE_UNSPECIFIED_VALUE)
                .dayOfWeek(overrideDate.getDayOfWeek().getValue())
                .limitIds(List.of())
                .startTime(-1)
                .endTime(-1)
                .capacity(1)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        staffAvailabilitySlotDayMapper.insertSelective(slotDay);
    }

    public void addTimeOverrideDay(
            Long companyId,
            Long businessId,
            Long staffId,
            LocalDate overrideDate,
            boolean isAvailable,
            final List<Map<String, Integer>> overrideTimeJson) {
        // 创建Day数据
        var timeDay = StaffAvailabilityTimeDay.builder()
                .companyId(companyId)
                .businessId(businessId)
                .staffId(staffId)
                .overrideDate(overrideDate)
                .isAvailable(isAvailable)
                .scheduleType(ScheduleType.SCHEDULE_TYPE_UNSPECIFIED_VALUE)
                .dayOfWeek(overrideDate.getDayOfWeek().getValue())
                .limitIds(List.of())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        staffAvailabilityTimeDayMapper.insertSelective(timeDay);

        if (overrideTimeJson == null || overrideTimeJson.isEmpty()) {
            return;
        }

        overrideTimeJson.forEach(timeMap -> {
            var timeHourSetting = StaffAvailabilityDayHour.builder()
                    .dayType(AvailabilityType.BY_TIME_VALUE)
                    .dayId(timeDay.getId())
                    .startTime(timeMap.get("startTime"))
                    .endTime(timeMap.get("endTime"))
                    .capacity(0)
                    .limitIds(List.of())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            staffAvailabilityDayHourMapper.insertSelective(timeHourSetting);
        });
    }

    @Transactional
    public void rebuildOverrideDays(
            Long companyId, Long businessId, Long staffId, List<SlotAvailabilityDayDef> slotAvailabilityDayDefs) {
        if (CollectionUtils.isEmpty(slotAvailabilityDayDefs)) {
            return;
        }
        deleteSlotDays(
                businessId,
                staffId,
                slotAvailabilityDayDefs.stream()
                        .map(SlotAvailabilityDayDef::getOverrideDate)
                        .toList());

        // 遍历传入的days pb数据
        var localDateTimeNow = LocalDateTime.now();
        slotAvailabilityDayDefs.forEach(slotAvailabilityDayDef -> {
            // 创建Day数据
            var overrideDate = LocalDate.parse(slotAvailabilityDayDef.getOverrideDate());
            var slotDay = StaffAvailabilitySlotDay.builder()
                    .companyId(companyId)
                    .businessId(businessId)
                    .staffId(staffId)
                    .overrideDate(overrideDate)
                    .isAvailable(slotAvailabilityDayDef.getIsAvailable())
                    .scheduleType(ScheduleType.SCHEDULE_TYPE_UNSPECIFIED.getNumber())
                    .dayOfWeek(overrideDate.getDayOfWeek().getValue())
                    .startTime(slotAvailabilityDayDef.getSlotDailySetting().getStartTime())
                    .endTime(slotAvailabilityDayDef.getSlotDailySetting().getEndTime())
                    .capacity(slotAvailabilityDayDef.getSlotDailySetting().getCapacity())
                    .createdAt(localDateTimeNow)
                    .updatedAt(localDateTimeNow)
                    .build();
            // 处理day相关的limits
            // var newLimitIds = rebuildLimits(
            //         List.of(),
            //         AvailabilityDayHourUtils.buildDayHourLimitsByDef(
            //                 slotAvailabilityDayDef.getSlotDailySetting().getLimit()));
            var newLimitIds = rebuildLimitsByLimitationDef(
                    List.of(), slotAvailabilityDayDef.getSlotDailySetting().getLimitationGroupsList());
            slotDay.setLimitIds(newLimitIds);
            staffAvailabilitySlotDayMapper.insertSelective(slotDay);

            rebuildSlotHours(slotDay.getId(), slotAvailabilityDayDef);
        });
    }

    @Transactional
    public void rebuildTimeOverrideDays(
            Long companyId, Long businessId, Long staffId, List<TimeAvailabilityDayDef> timeAvailabilityDayDefs) {
        if (CollectionUtils.isEmpty(timeAvailabilityDayDefs)) {
            return;
        }
        deleteTimeDays(
                businessId,
                staffId,
                timeAvailabilityDayDefs.stream()
                        .map(TimeAvailabilityDayDef::getOverrideDate)
                        .toList());

        // 遍历传入的days pb数据
        var localDateTimeNow = LocalDateTime.now();
        timeAvailabilityDayDefs.forEach(timeAvailabilityDayDef -> {
            // 创建Day数据
            var overrideDate = LocalDate.parse(timeAvailabilityDayDef.getOverrideDate());
            var timeDay = StaffAvailabilityTimeDay.builder()
                    .companyId(companyId)
                    .businessId(businessId)
                    .staffId(staffId)
                    .overrideDate(overrideDate)
                    .isAvailable(timeAvailabilityDayDef.getIsAvailable())
                    .scheduleType(ScheduleType.SCHEDULE_TYPE_UNSPECIFIED.getNumber())
                    .dayOfWeek(overrideDate.getDayOfWeek().getValue())
                    .createdAt(localDateTimeNow)
                    .updatedAt(localDateTimeNow)
                    .build();
            // 处理day相关的limits
            // var newLimitIds = rebuildLimits(
            //         List.of(),
            //         AvailabilityDayHourUtils.buildDayHourLimitsByDef(
            //                 timeAvailabilityDayDef.getTimeDailySetting().getLimit()));
            var newLimitIds = rebuildLimitsByLimitationDef(
                    List.of(), timeAvailabilityDayDef.getTimeDailySetting().getLimitationGroupsList());
            timeDay.setLimitIds(newLimitIds);
            staffAvailabilityTimeDayMapper.insertSelective(timeDay);

            rebuildTimeHours(timeDay.getId(), timeAvailabilityDayDef);
        });
    }

    private void deleteSlotDays(Long businessId, Long staffId, List<String> overrideDays) {
        // 查找db中对应的days的信息
        StaffAvailabilitySlotDayExample example = new StaffAvailabilitySlotDayExample();
        var criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(businessId)
                .andOverrideDateIn(
                        overrideDays.stream().map(LocalDate::parse).toList()) // 如果override date不是null，则说明是有效配置
                .andStaffIdEqualTo(staffId);
        var slotDayList = staffAvailabilitySlotDayMapper.selectByExampleWithBLOBs(example);

        // 删除对应的Days的数据，这里和availability day的数据不同，需要删除
        if (!CollectionUtils.isEmpty(slotDayList)) {
            staffAvailabilitySlotDayMapper.deleteByExample(example);
        }

        // 删除day相关的limit的数据
        var limitIds = slotDayList.stream()
                .flatMap(day -> {
                    if (day.getLimitIds() != null) {
                        return day.getLimitIds().stream();
                    }
                    return Stream.of();
                })
                .toList();
        var limitExample = new DayHourLimitExample();
        var limitCriteria = limitExample.createCriteria();
        if (!CollectionUtils.isEmpty(limitIds)) {
            limitCriteria.andIdIn(limitIds);
            limitationMapper.deleteByExample(limitExample);
        }

        // 删除旧的day hours数据
        slotDayList.forEach(day -> {
            deleteSlotHoursAndLimits(day.getId());
        });
    }

    private void deleteTimeDays(Long businessId, Long staffId, List<String> overrideDays) {
        // 查找db中对应的days的信息
        StaffAvailabilityTimeDayExample example = new StaffAvailabilityTimeDayExample();
        var criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(businessId)
                .andOverrideDateIn(
                        overrideDays.stream().map(LocalDate::parse).toList()) // 如果override date不是null，则说明是有效配置
                .andStaffIdEqualTo(staffId);
        var slotDayList = staffAvailabilityTimeDayMapper.selectByExampleWithBLOBs(example);

        // 删除对应的Days的数据，这里和availability day的数据不同，需要删除
        if (!CollectionUtils.isEmpty(slotDayList)) {
            staffAvailabilityTimeDayMapper.deleteByExample(example);
        }

        // 删除day相关的limit的数据
        var limitIds = slotDayList.stream()
                .flatMap(day -> {
                    if (day.getLimitIds() != null) {
                        return day.getLimitIds().stream();
                    }
                    return Stream.of();
                })
                .toList();
        var limitExample = new DayHourLimitExample();
        var limitCriteria = limitExample.createCriteria();
        if (!CollectionUtils.isEmpty(limitIds)) {
            limitCriteria.andIdIn(limitIds);
            limitationMapper.deleteByExample(limitExample);
        }

        // 删除旧的day hours数据
        slotDayList.forEach(day -> {
            deleteTimeHoursAndLimits(day.getId());
        });
    }

    private void deleteSlotHoursAndLimits(Long dayId) {
        StaffAvailabilityDayHourExample example = new StaffAvailabilityDayHourExample();
        var criteria = example.createCriteria();
        criteria.andDayIdEqualTo(dayId);
        criteria.andDayTypeEqualTo(AvailabilityType.BY_SLOT.getNumber());
        var dayHourList = staffAvailabilityDayHourMapper.selectByExampleWithBLOBs(example);

        var totalLimitIds = dayHourList.stream()
                .map(StaffAvailabilityDayHour::getLimitIds)
                .flatMap(Collection::stream)
                .toList();
        // 删除所有的limit
        if (!CollectionUtils.isEmpty(totalLimitIds)) {
            limitationService.deleteByIds(totalLimitIds);
        }

        // 删除所有的旧的slotHours
        if (!CollectionUtils.isEmpty(dayHourList)) {
            staffAvailabilityDayHourMapper.deleteByExample(example);
        }
    }

    private void deleteTimeHoursAndLimits(Long dayId) {
        StaffAvailabilityDayHourExample example = new StaffAvailabilityDayHourExample();
        var criteria = example.createCriteria();
        criteria.andDayIdEqualTo(dayId);
        criteria.andDayTypeEqualTo(AvailabilityType.BY_TIME.getNumber());
        var dayHourList = staffAvailabilityDayHourMapper.selectByExampleWithBLOBs(example);

        var totalLimitIds = dayHourList.stream()
                .map(StaffAvailabilityDayHour::getLimitIds)
                .flatMap(Collection::stream)
                .toList();
        // 删除所有的limit
        if (!CollectionUtils.isEmpty(totalLimitIds)) {
            limitationService.deleteByIds(totalLimitIds);
        }

        // 删除所有的旧的slotHours
        if (!CollectionUtils.isEmpty(dayHourList)) {
            staffAvailabilityDayHourMapper.deleteByExample(example);
        }
    }

    // 更新某一天的所有slotHours，先将这一天所有的slotHours查出，然后将它们删除
    // 最后根据传入的参数，重建slotHours
    private void rebuildSlotHours(Long newDayId, SlotAvailabilityDayDef slotAvailabilityDayDef) {
        // 根据传入的slotHours数据，重建新的slot
        slotAvailabilityDayDef.getSlotHourSettingListList().forEach(slotHourSettingDef -> {
            StaffAvailabilityDayHour dayHour = StaffAvailabilityDayHour.builder()
                    .dayId(newDayId)
                    .dayType(AvailabilityType.BY_SLOT.getNumber())
                    .startTime(slotHourSettingDef.getStartTime())
                    .endTime(slotHourSettingDef.getEndTime())
                    .capacity(slotHourSettingDef.getCapacity())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            // 处理limit ids
            // var newLimitIds = rebuildLimits(
            //         List.of(), AvailabilityDayHourUtils.buildDayHourLimitsByDef(slotHourSettingDef.getLimit()));
            var newLimitIds = rebuildLimitsByLimitationDef(List.of(), slotHourSettingDef.getLimitationGroupsList());
            dayHour.setLimitIds(newLimitIds);

            staffAvailabilityDayHourMapper.insertSelective(dayHour);
        });
    }

    private void rebuildTimeHours(Long newDayId, TimeAvailabilityDayDef timeAvailabilityDayDef) {
        // 根据传入的slotHours数据，重建新的slot
        timeAvailabilityDayDef.getTimeHourSettingListList().forEach(timeHourSettingDef -> {
            StaffAvailabilityDayHour dayHour = StaffAvailabilityDayHour.builder()
                    .dayId(newDayId)
                    .dayType(AvailabilityType.BY_TIME.getNumber())
                    .startTime(timeHourSettingDef.getStartTime())
                    .endTime(timeHourSettingDef.getEndTime())
                    .capacity(0)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            // 处理limit ids
            // var newLimitIds = rebuildLimits(
            //         List.of(), AvailabilityDayHourUtils.buildDayHourLimitsByDef(timeHourSettingDef.getLimit()));
            var newLimitIds = rebuildLimitsByLimitationDef(List.of(), timeHourSettingDef.getLimitationGroupsList());
            dayHour.setLimitIds(newLimitIds);

            staffAvailabilityDayHourMapper.insertSelective(dayHour);
        });
    }

    @Transactional
    public void updateSlotDaysAndHours(
            List<StaffAvailabilityDef> availabilityDefList,
            Map<String, StaffAvailabilitySlotDay> dbSlotDayMap,
            final Map<String, StaffAvailabilityTimeDay> dbTimeDayMap,
            long businessId) {
        availabilityDefList.forEach(staffAvailabilityDef -> {
            // 遍历传入的slot day
            staffAvailabilityDef.getSlotAvailabilityDayListList().forEach(slotAvailabilityDayDef -> {
                // 找到db对应的那条记录
                var dbSlotDay = dbSlotDayMap.get(AvailabilityDayHourUtils.getStaffAvailabilityDayKey(
                        businessId,
                        staffAvailabilityDef.getStaffId(),
                        slotAvailabilityDayDef.getScheduleTypeValue(),
                        slotAvailabilityDayDef.getDayOfWeekValue()));
                // 更新daily settings
                if (dbSlotDay != null) {
                    updateSlotDayByInputs(slotAvailabilityDayDef, dbSlotDay);
                    // 重建dayHour的数据 通过dayId和dayType可定位到某一天的多个slot hours
                    deleteSlotHoursAndLimits(dbSlotDay.getId());
                    rebuildSlotHours(dbSlotDay.getId(), slotAvailabilityDayDef);
                }
            });
            // 遍历传入的time day
            staffAvailabilityDef.getTimeAvailabilityDayListList().forEach(timeAvailabilityDayDef -> {
                // 找到db对应的那条记录
                var dbTimeDay = dbTimeDayMap.get(AvailabilityDayHourUtils.getStaffAvailabilityDayKey(
                        businessId,
                        staffAvailabilityDef.getStaffId(),
                        timeAvailabilityDayDef.getScheduleTypeValue(),
                        timeAvailabilityDayDef.getDayOfWeekValue()));
                // 更新daily settings
                if (dbTimeDay != null) {
                    updateTimeDayByInputs(timeAvailabilityDayDef, dbTimeDay);
                    // 重建dayHour的数据 通过dayId和dayType可定位到某一天的多个slot hours
                    deleteTimeHoursAndLimits(dbTimeDay.getId());
                    rebuildTimeHours(dbTimeDay.getId(), timeAvailabilityDayDef);
                }
            });
        });
    }

    // 更新某一天的数据，包含关联的limits
    private void updateSlotDayByInputs(
            SlotAvailabilityDayDef slotAvailabilityDayDef, StaffAvailabilitySlotDay dbSlotDay) {
        // slotDay可更新的有available, startTime, endTime, capacity, limit_ids, updated
        // limit ids需要特殊处理，需要先删除，再插入
        dbSlotDay.setIsAvailable(slotAvailabilityDayDef.getIsAvailable());
        dbSlotDay.setStartTime(slotAvailabilityDayDef.getSlotDailySetting().getStartTime());
        dbSlotDay.setEndTime(slotAvailabilityDayDef.getSlotDailySetting().getEndTime());
        dbSlotDay.setCapacity(slotAvailabilityDayDef.getSlotDailySetting().getCapacity());
        dbSlotDay.setUpdatedAt(LocalDateTime.now());

        if (!slotAvailabilityDayDef.getOverrideDate().isEmpty()) {
            dbSlotDay.setOverrideDate(LocalDate.parse(slotAvailabilityDayDef.getOverrideDate()));
        }

        // 处理day 相关的limit
        // var newLimitIds = rebuildLimits(
        //         dbSlotDay.getLimitIds(),
        //         AvailabilityDayHourUtils.buildDayHourLimitsByDef(
        //                 slotAvailabilityDayDef.getSlotDailySetting().getLimit()));
        var newLimitIds = rebuildLimitsByLimitationDef(
                dbSlotDay.getLimitIds(),
                slotAvailabilityDayDef.getSlotDailySetting().getLimitationGroupsList());
        dbSlotDay.setLimitIds(newLimitIds);
        staffAvailabilitySlotDayMapper.updateByPrimaryKeyWithBLOBs(dbSlotDay);
    }

    private void updateTimeDayByInputs(
            TimeAvailabilityDayDef timeAvailabilityDayDef, StaffAvailabilityTimeDay dbSlotDay) {
        // timeDay 可更新的有 available, limit_ids, updated
        // limit ids需要特殊处理，需要先删除，再插入
        dbSlotDay.setIsAvailable(timeAvailabilityDayDef.getIsAvailable());
        dbSlotDay.setUpdatedAt(LocalDateTime.now());

        if (!timeAvailabilityDayDef.getOverrideDate().isEmpty()) {
            dbSlotDay.setOverrideDate(LocalDate.parse(timeAvailabilityDayDef.getOverrideDate()));
        }

        // 处理day 相关的limit
        // var newLimitIds = rebuildLimits(
        //         dbSlotDay.getLimitIds(),
        //         AvailabilityDayHourUtils.buildDayHourLimitsByDef(
        //                 timeAvailabilityDayDef.getTimeDailySetting().getLimit()));
        var newLimitIds = rebuildLimitsByLimitationDef(
                dbSlotDay.getLimitIds(),
                timeAvailabilityDayDef.getTimeDailySetting().getLimitationGroupsList());
        dbSlotDay.setLimitIds(newLimitIds);
        staffAvailabilityTimeDayMapper.updateByPrimaryKeyWithBLOBs(dbSlotDay);
    }

    private List<Long> rebuildLimits(List<Long> oldLimitIds, List<DayHourLimit> dayHourLimits) {
        if (oldLimitIds != null && !oldLimitIds.isEmpty()) {
            limitationService.deleteByIds(oldLimitIds);
        }
        if (dayHourLimits == null || dayHourLimits.isEmpty()) {
            return List.of();
        }
        limitationService.createDayHourLimits(dayHourLimits);
        return dayHourLimits.stream().map(DayHourLimit::getId).toList();
    }

    private List<Long> rebuildLimitsByLimitationDef(List<Long> oldLimitIds, List<LimitationGroupDef> limitationGroups) {
        if (oldLimitIds != null && !oldLimitIds.isEmpty()) {
            limitationService.deleteByIds(oldLimitIds);
        }

        return limitationService.batchCreateByLimitationDef(limitationGroups);
    }

    // 获取staff availability days，并包含hours的数据. Key:StaffId
    public Map<Long, List<SlotAvailabilityDay>> getStaffAvailabilitySlotDayDetails(
            Long businessId, List<Long> staffIdList, boolean isOverride) {
        // 如果staffIdList为空，则不做为条件，查询所有business的数据
        // 先获取slot day表
        List<StaffAvailabilitySlotDay> staffAvailabilitySlotDays =
                getStaffAvailabilitySlotDays(businessId, staffIdList, isOverride);

        List<Long> limitIds = new ArrayList<>();
        List<Long> dayIds = new ArrayList<>();
        staffAvailabilitySlotDays.forEach(staffAvailabilitySlotDay -> {
            limitIds.addAll(staffAvailabilitySlotDay.getLimitIds()); // 添加day关联的limit ids
            dayIds.add(staffAvailabilitySlotDay.getId());
        });

        // dayId -> StaffAvailabilityDayHourList
        // 通过day ids 获取day hour
        Map<Long, List<StaffAvailabilityDayHour>> staffAvailabilityDayHourMap =
                getStaffAvailabilityDayHourMapByDayIds(dayIds, AvailabilityType.BY_SLOT);

        // 获取hour 的limit ids
        staffAvailabilityDayHourMap.forEach(
                (dayId, staffAvailabilityDayHourList) -> staffAvailabilityDayHourList.forEach(
                        staffAvailabilityDayHour -> limitIds.addAll(staffAvailabilityDayHour.getLimitIds())));

        // limitId -> DayHourLimit
        // 通过limit ids，获取limit表的数据
        Map<Long, DayHourLimit> dayHourLimitMap = limitationService.getDayHourLimitMapByLimitIds(limitIds);
        var groupIds = dayHourLimitMap.values().stream()
                .map(DayHourLimit::getGroupId)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        Map<Long, DayHourLimitGroup> dayHourLimitGroupMap = limitationService.getDayHourLimitGroupMap(groupIds);

        return buildStaffSlotAvailabilityMap(
                staffAvailabilitySlotDays, dayHourLimitMap, staffAvailabilityDayHourMap, dayHourLimitGroupMap);
    }

    @Nonnull
    private static Map<Long, List<SlotAvailabilityDay>> buildStaffSlotAvailabilityMap(
            final List<StaffAvailabilitySlotDay> staffAvailabilitySlotDays,
            final Map<Long, DayHourLimit> dayHourLimitMap,
            final Map<Long, List<StaffAvailabilityDayHour>> staffAvailabilityDayHourMap,
            final Map<Long, DayHourLimitGroup> dayHourLimitGroupMap) {
        return staffAvailabilitySlotDays.stream()
                .collect(Collectors.groupingBy(
                        StaffAvailabilitySlotDay::getStaffId,
                        Collectors.mapping(
                                staffAvailabilitySlotDay -> {
                                    // 获取slotDayLimits
                                    List<DayHourLimit> slotDayLimits = staffAvailabilitySlotDay.getLimitIds().stream()
                                            .map(dayHourLimitMap::get)
                                            .toList();

                                    // 获取day hour & build slot hour setting
                                    var dayHour = staffAvailabilityDayHourMap.get(staffAvailabilitySlotDay.getId());
                                    List<SlotHourSetting> slotHourSettings;
                                    if (CollectionUtils.isEmpty(dayHour)) {
                                        slotHourSettings = List.of();
                                    } else {
                                        slotHourSettings = dayHour.stream()
                                                .map(staffAvailabilityDayHour -> {
                                                    List<DayHourLimit> dayHourLimits =
                                                            staffAvailabilityDayHour.getLimitIds().stream()
                                                                    .map(dayHourLimitMap::get)
                                                                    .toList();
                                                    return SlotHourSetting.newBuilder()
                                                            .setStartTime(staffAvailabilityDayHour.getStartTime())
                                                            .setCapacity(staffAvailabilityDayHour.getCapacity())
                                                            .addAllLimitationGroups(
                                                                    AvailabilityDayHourUtils
                                                                            .buildBookingLimitationPBModel(
                                                                                    dayHourLimits,
                                                                                    dayHourLimitGroupMap))
                                                            .build();
                                                })
                                                .toList();
                                    }

                                    // build slot day
                                    var builder = SlotAvailabilityDay.newBuilder()
                                            .setDayOfWeekValue(staffAvailabilitySlotDay.getDayOfWeek())
                                            .setIsAvailable(staffAvailabilitySlotDay.getIsAvailable())
                                            .setScheduleType(
                                                    ScheduleType.forNumber(staffAvailabilitySlotDay.getScheduleType()))
                                            .setSlotDailySetting(SlotDailySetting.newBuilder()
                                                    .setStartTime(staffAvailabilitySlotDay.getStartTime())
                                                    .setEndTime(staffAvailabilitySlotDay.getEndTime())
                                                    .setCapacity(staffAvailabilitySlotDay.getCapacity())
                                                    .addAllLimitationGroups(
                                                            AvailabilityDayHourUtils.buildBookingLimitationPBModel(
                                                                    slotDayLimits, dayHourLimitGroupMap))
                                                    .build())
                                            .setStaffId(staffAvailabilitySlotDay.getStaffId())
                                            .addAllSlotHourSettingList(slotHourSettings);
                                    if (staffAvailabilitySlotDay.getOverrideDate() != null) {
                                        builder.setOverrideDate(staffAvailabilitySlotDay
                                                .getOverrideDate()
                                                .toString());
                                    }
                                    return builder.build();
                                },
                                Collectors.toList())));
    }

    // dayId -> StaffAvailabilityDayHourList
    private Map<Long, List<StaffAvailabilityDayHour>> getStaffAvailabilityDayHourMapByDayIds(
            List<Long> dayIds, AvailabilityType dayType) {
        if (CollectionUtils.isEmpty(dayIds)) {
            return Map.of();
        }
        StaffAvailabilityDayHourExample example = new StaffAvailabilityDayHourExample();
        example.createCriteria().andDayIdIn(dayIds).andDayTypeEqualTo(dayType.getNumber());
        example.setOrderByClause("start_time");
        var staffAvailabilityDayHours = staffAvailabilityDayHourMapper.selectByExampleWithBLOBs(example);
        return staffAvailabilityDayHours.stream().collect(Collectors.groupingBy(StaffAvailabilityDayHour::getDayId));
    }

    public Map<Long, List<TimeAvailabilityDay>> getStaffAvailabilityTimeDayDetails(
            final long businessId, final List<Long> staffIdList, boolean isOverride) {
        // 如果staffIdList为空，则不做为条件，查询所有business的数据
        // 先获取 time day表
        var staffAvailabilityTimeDays = getStaffAvailabilityTimeDays(businessId, staffIdList, isOverride);

        List<Long> limitIds = new ArrayList<>();
        List<Long> dayIds = new ArrayList<>();
        staffAvailabilityTimeDays.forEach(staffAvailabilityTimeDay -> {
            limitIds.addAll(staffAvailabilityTimeDay.getLimitIds()); // 添加day关联的limit ids
            dayIds.add(staffAvailabilityTimeDay.getId());
        });

        // dayId -> StaffAvailabilityDayHourList
        // 通过day ids 获取day hour
        Map<Long, List<StaffAvailabilityDayHour>> staffAvailabilityDayHourMap =
                getStaffAvailabilityDayHourMapByDayIds(dayIds, AvailabilityType.BY_TIME);

        // 获取hour 的limit ids
        staffAvailabilityDayHourMap.forEach(
                (dayId, staffAvailabilityDayHourList) -> staffAvailabilityDayHourList.forEach(
                        staffAvailabilityDayHour -> limitIds.addAll(staffAvailabilityDayHour.getLimitIds())));

        // limitId -> DayHourLimit
        // 通过limit ids，获取limit表的数据
        Map<Long, DayHourLimit> dayHourLimitMap = limitationService.getDayHourLimitMapByLimitIds(limitIds);
        var groupIds = dayHourLimitMap.values().stream()
                .map(DayHourLimit::getGroupId)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        Map<Long, DayHourLimitGroup> dayHourLimitGroupMap = limitationService.getDayHourLimitGroupMap(groupIds);

        return buildStaffTimeAvailabilityMap(
                staffAvailabilityTimeDays, dayHourLimitMap, staffAvailabilityDayHourMap, dayHourLimitGroupMap);
    }

    @Nonnull
    private static Map<Long, List<TimeAvailabilityDay>> buildStaffTimeAvailabilityMap(
            final List<StaffAvailabilityTimeDay> staffAvailabilityTimeDays,
            final Map<Long, DayHourLimit> dayHourLimitMap,
            final Map<Long, List<StaffAvailabilityDayHour>> staffAvailabilityDayHourMap,
            final Map<Long, DayHourLimitGroup> dayHourLimitGroupMap) {
        return staffAvailabilityTimeDays.stream()
                .collect(Collectors.groupingBy(
                        StaffAvailabilityTimeDay::getStaffId,
                        Collectors.mapping(
                                staffAvailabilityTimeDay -> {
                                    // 获取 timeDayLimits
                                    List<DayHourLimit> timeDayLimits = staffAvailabilityTimeDay.getLimitIds().stream()
                                            .map(dayHourLimitMap::get)
                                            .toList();

                                    // 获取day hour & build time hour setting
                                    var dayHour = staffAvailabilityDayHourMap.get(staffAvailabilityTimeDay.getId());
                                    List<TimeHourSetting> timeHourSettings;
                                    if (CollectionUtils.isEmpty(dayHour)) {
                                        timeHourSettings = List.of();
                                    } else {
                                        timeHourSettings = dayHour.stream()
                                                .map(staffAvailabilityDayHour -> {
                                                    List<DayHourLimit> dayHourLimits =
                                                            staffAvailabilityDayHour.getLimitIds().stream()
                                                                    .map(dayHourLimitMap::get)
                                                                    .toList();
                                                    return TimeHourSetting.newBuilder()
                                                            .setStartTime(staffAvailabilityDayHour.getStartTime())
                                                            .setEndTime(staffAvailabilityDayHour.getEndTime())
                                                            .addAllLimitationGroups(
                                                                    AvailabilityDayHourUtils
                                                                            .buildBookingLimitationPBModel(
                                                                                    dayHourLimits,
                                                                                    dayHourLimitGroupMap))
                                                            .build();
                                                })
                                                .toList();
                                    }

                                    // build time day
                                    var builder = TimeAvailabilityDay.newBuilder()
                                            .setDayOfWeekValue(staffAvailabilityTimeDay.getDayOfWeek())
                                            .setIsAvailable(staffAvailabilityTimeDay.getIsAvailable())
                                            .setScheduleType(
                                                    ScheduleType.forNumber(staffAvailabilityTimeDay.getScheduleType()))
                                            .setTimeDailySetting(TimeDailySetting.newBuilder()
                                                    .addAllLimitationGroups(
                                                            AvailabilityDayHourUtils.buildBookingLimitationPBModel(
                                                                    timeDayLimits, dayHourLimitGroupMap))
                                                    .build())
                                            .setStaffId(staffAvailabilityTimeDay.getStaffId())
                                            .addAllTimeHourSettingList(timeHourSettings);
                                    if (staffAvailabilityTimeDay.getOverrideDate() != null) {
                                        builder.setOverrideDate(staffAvailabilityTimeDay
                                                .getOverrideDate()
                                                .toString());
                                    }
                                    return builder.build();
                                },
                                Collectors.toList())));
    }
}
