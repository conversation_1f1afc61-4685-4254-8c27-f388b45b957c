package com.moego.svc.organization.service;

import static com.moego.idl.models.errors.v1.Code.CODE_PARAMS_ERROR;

import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.organization.entity.StaffAvailability;
import com.moego.svc.organization.entity.StaffAvailabilityExample;
import com.moego.svc.organization.mapper.base.BaseStaffAvailability;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class StaffAvailabilityService {

    private final BaseStaffAvailability staffAvailabilityMapper;
    private final StaffAvailabilityDayHourService staffAvailabilityDayHourService;

    public void insert(StaffAvailability staffAvailability) {
        staffAvailabilityMapper.insertSelective(staffAvailability);
    }

    public void update(StaffAvailability staffAvailability) {
        staffAvailabilityMapper.updateByPrimaryKeySelective(staffAvailability);
    }

    // 这个方法看起来无用，因为slot场景下没有is_available字段，没有可更新的东西
    public void batchUpdate(List<StaffAvailability> staffAvailabilities) {
        staffAvailabilities.forEach(staffAvailabilityEntity -> {
            StaffAvailabilityExample example = new StaffAvailabilityExample();
            var criteria = example.createCriteria();
            criteria.andCompanyIdEqualTo(staffAvailabilityEntity.getCompanyId())
                    .andBusinessIdEqualTo(staffAvailabilityEntity.getBusinessId())
                    .andStaffIdEqualTo(staffAvailabilityEntity.getStaffId());

            staffAvailabilityMapper.updateByExampleSelective(staffAvailabilityEntity, example);
        });
    }

    /*
     * Get staff availability by business id
     * @param businessId not null
     * @param staffIds nullable
     * @return List<StaffAvailability>
     */
    public List<StaffAvailability> getStaffAvailabilities(Long businessId, List<Long> staffIds) {

        if (businessId == null) {
            throw ExceptionUtil.bizException(CODE_PARAMS_ERROR, "businessId is invalid");
        }

        StaffAvailabilityExample example = new StaffAvailabilityExample();
        var criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(businessId);
        // 如果staff为空，则获取所有business的数据
        if (staffIds != null && !staffIds.isEmpty()) {
            criteria.andStaffIdIn(staffIds);
        }
        return staffAvailabilityMapper.selectByExample(example);
    }

    @Transactional
    public List<Long> initStaffAvailability(
            Long companyId, Long businessId, List<Long> requestStaffIds, List<StaffAvailability> staffAvailabilities) {
        var dbStaffIds =
                staffAvailabilities.stream().map(StaffAvailability::getStaffId).collect(Collectors.toSet());
        // 获取不存在的staffIdList
        var notExistStaffIds = requestStaffIds.stream()
                .filter(staffId -> !dbStaffIds.contains(staffId))
                .toList();

        // 为不存在的staff创建availability
        if (!notExistStaffIds.isEmpty()) {
            notExistStaffIds.forEach(staffId -> {
                // 查找数据表staff_availability_day_hour获取time的数据
                var workingHourSetting = staffAvailabilityDayHourService.getStaffWorkingHour(businessId, staffId);

                // 计算今天所在周的周日
                var slotStartSunday = workingHourSetting
                        .startDateByTime()
                        .with(java.time.temporal.TemporalAdjusters.previousOrSame(java.time.DayOfWeek.SUNDAY));

                // 插入availability主记录
                StaffAvailability staffAvailability = StaffAvailability.builder()
                        .companyId(companyId)
                        .businessId(businessId)
                        .staffId(staffId)
                        .slotScheduleType(workingHourSetting.scheduleType().getNumber()) // 初始化从working hours的表中获取
                        .slotStartSunday(slotStartSunday)
                        .createdAt(LocalDateTime.now())
                        .updatedAt(LocalDateTime.now())
                        .build();
                staffAvailabilityMapper.insertSelective(staffAvailability);

                // 初始化对应的slotDays
                staffAvailabilityDayHourService.initSlotDays(
                        companyId, businessId, staffId, workingHourSetting.workingHourStartEndTimeMapMap());
                // 初始化对应的initOverrideDates
                staffAvailabilityDayHourService.initOverrideDates(companyId, businessId, staffId);
            });
        }
        return notExistStaffIds;
    }

    @Transactional
    public List<Long> initTimeStaffAvailability(
            Long companyId, Long businessId, List<Long> requestStaffIds, List<StaffAvailability> staffAvailabilities) {
        var dbStaffIds =
                staffAvailabilities.stream().map(StaffAvailability::getStaffId).toList();
        // 获取不存在的staffIdList
        var notExistStaffIds = requestStaffIds.stream()
                .filter(staffId -> !dbStaffIds.contains(staffId))
                .toList();

        var staffAvailabilityMap = getStaffAvailabilities(businessId, List.of()).stream()
                .collect(Collectors.toMap(StaffAvailability::getStaffId, Function.identity()));

        // 为不存在的staff创建availability
        if (!notExistStaffIds.isEmpty()) {
            notExistStaffIds.forEach(staffId -> {
                var existStaffAvailability = staffAvailabilityMap.get(staffId);
                if (Objects.isNull(existStaffAvailability)) {
                    initTimeStaffAvailabilityByTimeAndBySlot(companyId, businessId, List.of(staffId));
                    return;
                }

                if (existStaffAvailability.getTimeScheduleType() > 0) {
                    // 已经初始化
                    return;
                }

                // 查找数据表staff_availability_day_hour获取time的数据
                var workingHourSetting = staffAvailabilityDayHourService.getStaffWorkingHour(businessId, staffId);

                // 计算今天所在周的周日
                var timeStartSunday = workingHourSetting
                        .startDateByTime()
                        .with(java.time.temporal.TemporalAdjusters.previousOrSame(java.time.DayOfWeek.SUNDAY));
                if (timeStartSunday.isBefore(LocalDate.EPOCH)) {
                    timeStartSunday = workingHourSetting
                            .startDateByTime()
                            .with(java.time.temporal.TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY));
                }

                StaffAvailability staffAvailability = existStaffAvailability.toBuilder()
                        .timeScheduleType(workingHourSetting.scheduleType().getNumber()) // 初始化从working hours的表中获取
                        .timeStartSunday(timeStartSunday)
                        .updatedAt(LocalDateTime.now())
                        .build();
                staffAvailabilityMapper.updateByPrimaryKeySelective(staffAvailability);

                // 初始化对应的 time days
                staffAvailabilityDayHourService.initTimeDays(companyId, businessId, staffId, workingHourSetting);
                // 初始化对应的initOverrideDates
                staffAvailabilityDayHourService.initTimeOverrideDates(companyId, businessId, staffId);
            });
        }
        return notExistStaffIds;
    }

    @Transactional
    public void initTimeStaffAvailabilityByTimeAndBySlot(Long companyId, Long businessId, List<Long> requestStaffIds) {

        if (CollectionUtils.isEmpty(requestStaffIds)) {
            return;
        }

        var staffAvailabilityMap = getStaffAvailabilities(businessId, requestStaffIds).stream()
                .collect(Collectors.toMap(StaffAvailability::getStaffId, Function.identity()));

        // 为不存在的staff创建availability
        requestStaffIds.stream().distinct().forEach(staffId -> {
            var existStaffAvailability = staffAvailabilityMap.get(staffId);
            if (Objects.nonNull(existStaffAvailability)) {
                return;
            }

            // 查找数据表staff_availability_day_hour获取time的数据
            var workingHourSetting = staffAvailabilityDayHourService.getStaffWorkingHour(businessId, staffId);

            // 计算今天所在周的周日
            var startSunday = workingHourSetting
                    .startDateByTime()
                    .with(java.time.temporal.TemporalAdjusters.previousOrSame(java.time.DayOfWeek.SUNDAY));
            if (startSunday.isBefore(LocalDate.EPOCH)) {
                startSunday = workingHourSetting
                        .startDateByTime()
                        .with(java.time.temporal.TemporalAdjusters.next(java.time.DayOfWeek.SUNDAY));
            }

            // 插入availability主记录
            StaffAvailability staffAvailability = StaffAvailability.builder()
                    .companyId(companyId)
                    .businessId(businessId)
                    .staffId(staffId)
                    .slotScheduleType(workingHourSetting.scheduleType().getNumber()) // 初始化从working hours的表中获取
                    .slotStartSunday(startSunday)
                    .timeScheduleType(workingHourSetting.scheduleType().getNumber())
                    .timeStartSunday(startSunday)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            try {
                staffAvailabilityMapper.insertSelective(staffAvailability);
            } catch (DuplicateKeyException e) {
                // 处理并发插入导致的唯一键冲突，记录日志但不抛出异常
                log.info(
                        "Staff availability already exists for businessId: {}, staffId: {}",
                        staffAvailability.getBusinessId(),
                        staffAvailability.getStaffId());
                return;
            }

            // 初始化对应的slotDays
            staffAvailabilityDayHourService.initSlotDays(
                    companyId, businessId, staffId, workingHourSetting.workingHourStartEndTimeMapMap());
            // 初始化对应的initOverrideDates
            staffAvailabilityDayHourService.initOverrideDates(companyId, businessId, staffId);

            // 初始化对应的 time days
            staffAvailabilityDayHourService.initTimeDays(companyId, businessId, staffId, workingHourSetting);
            // 初始化对应的initOverrideDates
            staffAvailabilityDayHourService.initTimeOverrideDates(companyId, businessId, staffId);
        });
    }

    public StaffAvailability getStaffAvailability(Long businessId, Long staffId) {
        StaffAvailabilityExample example = new StaffAvailabilityExample();
        var criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(businessId).andStaffIdEqualTo(staffId);
        var list = staffAvailabilityMapper.selectByExample(example);
        if (list != null && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }
}
