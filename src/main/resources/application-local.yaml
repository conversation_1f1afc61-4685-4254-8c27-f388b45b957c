spring:
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:postgresql://postgres.t2.moego.dev:40132/moego_online_booking
    username: moego_developer_240310_eff7a0dc
    password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
  activemq:
    broker-url: ssl://b-f80a5cc9-31ab-4781-8914-ecb9fe93c94a-1.mq.us-west-2.amazonaws.com:61617
    password: +S+22Y8FlzqazglDiXbwIA
    user: moego-active-mq-dev-v1-master
    enabled: true
    destinationPrefix: refund
  data:
    redis:
      host: redis.t2.moego.dev
      port: 40179
      ssl:
        enabled: true
      timeout: 60000
      password: iMoReGoTdesstingeCache250310_7fec987d
      key:
        delimiter: ':'
        prefix: apiv2

mybatis:
  configuration:
    cache-enabled: false

logging:
  level:
    com.moego.svc.online.booking.mapper: debug
moego:
  feature-flag:
    growth-book:
      api-host: https://growthbook.moego.pet/growthbook-api
      client-key: sdk-qygeRRneunZQJxf
  event-bus:
    brokers:
      - name: default
        addresses:
          - kafka.kafka.svc.cluster.local:9092
    producer:
      enabled: true
      log-success: false
      log-failure: true
    consumer:
      enabled: false
