spring:
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:mysql://mysql.t2.moego.dev:40106/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: moego_developer_240310_eff7a0dc
    password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
  data:
    redis:
      host: redis.t2.moego.dev
      port: 40179
      ssl:
        enabled: true
      timeout: 60000
      password: iMoReGoTdesstingeCache250310_7fec987d
      key:
        delimiter: ':'
        prefix: local
moego:
  data-sources:
    - name: reader
      driver-class-name: software.amazon.jdbc.Driver
      url: jdbc:aws-wrapper:mysql://mysql.t2.moego.dev:40106/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
      username: moego_developer_240310_eff7a0dc
      password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
  invite-staff:
    sign-in: https://go.t2.moego.pet/sign_in?inviteCode={0}
    sign-up: https://go.t2.moego.pet/sign_up?inviteCode={0}

logging:
  level:
    com.moego.svc.organization.mapper: debug
camera:
  abckam:
    username:
    password:
  idogcam:
    key: