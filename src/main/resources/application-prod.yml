spring:
  config:
    import:
      - "aws-secretsmanager:moego/production/datasource?prefix=secret.datasource."
      - "aws-secretsmanager:moego/production/camera?prefix=secret.camera."
      - "aws-secretsmanager:moego/production/redis?prefix=secret.redis."
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:mysql://${secret.datasource.mysql.url.master}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.mysql.moego_svc_organization.username}
    password: ${secret.datasource.mysql.moego_svc_organization.password}
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
  data:
    redis:
      host: ${secret.redis.host}
      port: ${secret.redis.port}
      ssl:
        enabled: ${secret.redis.tls}
      timeout: 60000
      password: ${secret.redis.password}
      key:
        delimiter: ':'
        prefix: prod
moego:
  data-sources:
    - name: reader
      driver-class-name: software.amazon.jdbc.Driver
      url: jdbc:aws-wrapper:mysql://${secret.datasource.mysql.url.reader}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
      username: ${secret.datasource.mysql.moego_svc_organization.username}
      password: ${secret.datasource.mysql.moego_svc_organization.password}
  invite-staff:
    sign-in: https://go.moego.pet/sign_in?inviteCode={0}
    sign-up: https://go.moego.pet/sign_up?inviteCode={0}
camera:
  abckam:
    username: ${secret.camera.abckam.username}
    password: ${secret.camera.abckam.password}
  idogcam:
    key: ${secret.camera.idogcam.key}

# 这个是 Emma 的邮箱，新用户注册 company 之后会发送一个通知给这个邮箱
# TODO: 或许这段逻辑可以去掉了？
business:
  register:
    inform:
      email: <EMAIL>