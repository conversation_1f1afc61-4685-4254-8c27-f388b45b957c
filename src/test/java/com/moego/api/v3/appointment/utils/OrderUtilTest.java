package com.moego.api.v3.appointment.utils;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.google.type.Decimal;
import com.google.type.Money;
import com.moego.idl.models.order.v1.DiscountType;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderModelAppointmentView;
import com.moego.idl.models.order.v1.OrderPromotionModel;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

class OrderUtilTest {

    private static final String USD = "USD";

    @Nested
    @DisplayName("calculateTotalPaidAmount tests")
    class CalculateTotalPaidAmountTest {

        @Test
        @DisplayName("Should return default Money when orders list is empty")
        void shouldReturnDefaultMoneyWhenOrdersListIsEmpty() {
            // Arrange
            List<OrderModelAppointmentView> emptyOrders = Collections.emptyList();

            // Act
            Money result = OrderUtil.calculateTotalPaidAmount(emptyOrders);

            // Assert
            assertThat(result).isEqualTo(Money.getDefaultInstance());
        }

        @Test
        @DisplayName("Should correctly sum paid amounts from all orders")
        void shouldCorrectlySumPaidAmountsFromAllOrders() {
            // Arrange
            OrderModelAppointmentView order1 = mock(OrderModelAppointmentView.class);
            OrderModelAppointmentView order2 = mock(OrderModelAppointmentView.class);

            Money paidAmount1 = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(100)
                    .setNanos(500000000)
                    .build();
            Money paidAmount2 = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(50)
                    .setNanos(250000000)
                    .build();

            when(order1.getPaidAmount()).thenReturn(paidAmount1);
            when(order2.getPaidAmount()).thenReturn(paidAmount2);

            List<OrderModelAppointmentView> orders = List.of(order1, order2);

            // Expected: 100.50 + 50.25 = 150.75 USD
            Money expectedTotal = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(150)
                    .setNanos(750000000)
                    .build();

            // Act
            Money result = OrderUtil.calculateTotalPaidAmount(orders);

            // Assert
            assertThat(result.getCurrencyCode()).isEqualTo(expectedTotal.getCurrencyCode());
            assertThat(result.getUnits()).isEqualTo(expectedTotal.getUnits());
            assertThat(result.getNanos()).isEqualTo(expectedTotal.getNanos());
        }
    }

    @Nested
    @DisplayName("calculateDepositTotalAmount tests")
    class CalculateDepositTotalAmountTest {

        @Test
        @DisplayName("Should return default Money when orders list is empty")
        void shouldReturnDefaultMoneyWhenOrdersListIsEmpty() {
            // Arrange
            List<OrderModelAppointmentView> emptyOrders = Collections.emptyList();

            // Act
            Money result = OrderUtil.calculateDepositTotalAmount(emptyOrders);

            // Assert
            assertThat(result).isEqualTo(Money.getDefaultInstance());
        }

        @Test
        @DisplayName("Should correctly sum total amounts from deposit orders only")
        void shouldCorrectlySumTotalAmountsFromDepositOrdersOnly() {
            // Arrange
            OrderModelAppointmentView depositOrder = mock(OrderModelAppointmentView.class);
            OrderModelAppointmentView regularOrder = mock(OrderModelAppointmentView.class);

            Money totalAmount1 = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(200)
                    .setNanos(0)
                    .build();
            Money totalAmount2 = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(100)
                    .setNanos(0)
                    .build();

            when(depositOrder.getOrderType()).thenReturn(OrderModel.OrderType.DEPOSIT);
            when(regularOrder.getOrderType()).thenReturn(OrderModel.OrderType.ORIGIN);

            when(depositOrder.getTotalAmount()).thenReturn(totalAmount1);
            when(regularOrder.getTotalAmount()).thenReturn(totalAmount2);

            List<OrderModelAppointmentView> orders = List.of(depositOrder, regularOrder);

            // Expected: Only 200 USD from deposit order
            Money expectedTotal = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(200)
                    .setNanos(0)
                    .build();

            // Act
            Money result = OrderUtil.calculateDepositTotalAmount(orders);

            // Assert
            assertThat(result.getCurrencyCode()).isEqualTo(expectedTotal.getCurrencyCode());
            assertThat(result.getUnits()).isEqualTo(expectedTotal.getUnits());
            assertThat(result.getNanos()).isEqualTo(expectedTotal.getNanos());
        }
    }

    @Nested
    @DisplayName("calculateDepositPaidAmount tests")
    class CalculateDepositPaidAmountTest {

        @Test
        @DisplayName("Should return default Money when orders list is empty")
        void shouldReturnDefaultMoneyWhenOrdersListIsEmpty() {
            // Arrange
            List<OrderModelAppointmentView> emptyOrders = Collections.emptyList();

            // Act
            Money result = OrderUtil.calculateDepositPaidAmount(emptyOrders);

            // Assert
            assertThat(result).isEqualTo(Money.getDefaultInstance());
        }

        @Test
        @DisplayName("Should correctly sum paid amounts from deposit orders only")
        void shouldCorrectlySumPaidAmountsFromDepositOrdersOnly() {
            // Arrange
            OrderModelAppointmentView depositOrder = mock(OrderModelAppointmentView.class);
            OrderModelAppointmentView partiallyPaidDepositOrder = mock(OrderModelAppointmentView.class);
            OrderModelAppointmentView regularOrder = mock(OrderModelAppointmentView.class);

            Money paidAmount1 = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(200)
                    .setNanos(0)
                    .build();
            Money paidAmount2 = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(50)
                    .setNanos(0)
                    .build();
            Money paidAmount3 = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(100)
                    .setNanos(0)
                    .build();

            when(depositOrder.getOrderType()).thenReturn(OrderModel.OrderType.DEPOSIT);
            when(partiallyPaidDepositOrder.getOrderType()).thenReturn(OrderModel.OrderType.DEPOSIT);
            when(regularOrder.getOrderType()).thenReturn(OrderModel.OrderType.ORIGIN);

            when(depositOrder.getPaidAmount()).thenReturn(paidAmount1);
            when(partiallyPaidDepositOrder.getPaidAmount()).thenReturn(paidAmount2);
            when(regularOrder.getPaidAmount()).thenReturn(paidAmount3);

            List<OrderModelAppointmentView> orders = List.of(depositOrder, partiallyPaidDepositOrder, regularOrder);

            // Expected: 200 + 50 = 250 USD from deposit orders only
            Money expectedTotal = Money.newBuilder()
                    .setCurrencyCode(USD)
                    .setUnits(250)
                    .setNanos(0)
                    .build();

            // Act
            Money result = OrderUtil.calculateDepositPaidAmount(orders);

            // Assert
            assertThat(result.getCurrencyCode()).isEqualTo(expectedTotal.getCurrencyCode());
            assertThat(result.getUnits()).isEqualTo(expectedTotal.getUnits());
            assertThat(result.getNanos()).isEqualTo(expectedTotal.getNanos());
        }
    }

    @Nested
    @DisplayName("isUseStoreCredit tests")
    class IsUseStoreCreditTest {

        @Test
        @DisplayName("Should return false when orders list is empty")
        void shouldReturnFalseWhenOrdersListIsEmpty() {
            // Arrange
            List<OrderDetailModelV1> emptyOrders = Collections.emptyList();

            // Act
            boolean result = OrderUtil.hasUseStoreCredit(emptyOrders);

            // Assert
            assertThat(result).isFalse();
        }

        @Test
        @DisplayName("Should return true when order has store credit")
        void shouldReturnTrueWhenOrderHasStoreCredit() {
            // Arrange
            Money creditAmount =
                    Money.newBuilder().setCurrencyCode("USD").setUnits(50L).build();

            OrderPromotionModel.StoreCreditSubject storeCredit = OrderPromotionModel.StoreCreditSubject.newBuilder()
                    .setAmount(creditAmount)
                    .build();

            OrderPromotionModel promotion = OrderPromotionModel.newBuilder()
                    .setId(1L)
                    .setStoreCredit(storeCredit)
                    .build();

            OrderDetailModelV1 orderDetail = OrderDetailModelV1.newBuilder()
                    .addOrderPromotions(promotion)
                    .build();

            List<OrderDetailModelV1> orders = Arrays.asList(orderDetail);

            // Act
            boolean result = OrderUtil.hasUseStoreCredit(orders);

            // Assert
            assertThat(result).isTrue();
        }

        @Test
        @DisplayName("Should return false when order has no store credit")
        void shouldReturnFalseWhenOrderHasNoStoreCredit() {
            // Arrange
            OrderPromotionModel.DiscountSubject discount = OrderPromotionModel.DiscountSubject.newBuilder()
                    .setId(1L)
                    .setDiscountCode("SAVE10")
                    .setDiscountType(DiscountType.PERCENTAGE)
                    .setDiscountValue(Decimal.newBuilder().setValue("10").build())
                    .build();

            OrderPromotionModel promotion = OrderPromotionModel.newBuilder()
                    .setId(1L)
                    .setDiscount(discount)
                    .build();

            OrderDetailModelV1 orderDetail = OrderDetailModelV1.newBuilder()
                    .addOrderPromotions(promotion)
                    .build();

            List<OrderDetailModelV1> orders = Arrays.asList(orderDetail);

            // Act
            boolean result = OrderUtil.hasUseStoreCredit(orders);

            // Assert
            assertThat(result).isFalse();
        }
    }
}
