package com.moego.api.v3.order.convertor;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.RefundOrderDetailModel;
import com.moego.idl.models.order.v1.RefundOrderPaymentModel;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;

class InvoiceConvertorTest {

    @Test
    void getAllRefundOrderPayments_withEmptyLists_returnsEmptyList() {
        // Given
        List<OrderDetailModelV1> orderDetails = Collections.emptyList();
        List<RefundOrderDetailModel> refundOrderDetails = Collections.emptyList();

        // When
        List<RefundOrderPaymentModel> result =
                InvoiceConvertor.getAllRefundOrderPayments(orderDetails, refundOrderDetails);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getAllRefundOrderPayments_withNullLists_returnsEmptyList() {
        // When
        List<RefundOrderPaymentModel> result = InvoiceConvertor.getAllRefundOrderPayments(null, null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getAllRefundOrderPayments_withOnlyOrderDetails_returnsPaymentsFromOrderDetails() {
        // Given
        RefundOrderPaymentModel payment1 = createMockPayment(1);
        RefundOrderPaymentModel payment2 = createMockPayment(2);

        OrderDetailModelV1 orderDetail1 = mock(OrderDetailModelV1.class);
        OrderDetailModelV1 orderDetail2 = mock(OrderDetailModelV1.class);

        when(orderDetail1.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment1));
        when(orderDetail2.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment2));

        List<OrderDetailModelV1> orderDetails = Arrays.asList(orderDetail1, orderDetail2);
        List<RefundOrderDetailModel> refundOrderDetails = Collections.emptyList();

        // When
        List<RefundOrderPaymentModel> result =
                InvoiceConvertor.getAllRefundOrderPayments(orderDetails, refundOrderDetails);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(payment1));
        assertTrue(result.contains(payment2));
    }

    @Test
    void getAllRefundOrderPayments_withOnlyRefundOrderDetails_returnsPaymentsFromRefundOrderDetails() {
        // Given
        RefundOrderPaymentModel payment1 = createMockPayment(1);
        RefundOrderPaymentModel payment2 = createMockPayment(2);

        RefundOrderDetailModel refundDetail1 = mock(RefundOrderDetailModel.class);
        RefundOrderDetailModel refundDetail2 = mock(RefundOrderDetailModel.class);

        when(refundDetail1.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment1));
        when(refundDetail2.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment2));

        List<OrderDetailModelV1> orderDetails = Collections.emptyList();
        List<RefundOrderDetailModel> refundOrderDetails = Arrays.asList(refundDetail1, refundDetail2);

        // When
        List<RefundOrderPaymentModel> result =
                InvoiceConvertor.getAllRefundOrderPayments(orderDetails, refundOrderDetails);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(payment1));
        assertTrue(result.contains(payment2));
    }

    @Test
    void getAllRefundOrderPayments_withBothLists_returnsAllPayments() {
        // Given
        RefundOrderPaymentModel payment1 = createMockPayment(1);
        RefundOrderPaymentModel payment2 = createMockPayment(2);
        RefundOrderPaymentModel payment3 = createMockPayment(3);

        OrderDetailModelV1 orderDetail = mock(OrderDetailModelV1.class);
        RefundOrderDetailModel refundDetail = mock(RefundOrderDetailModel.class);

        when(orderDetail.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment1, payment2));
        when(refundDetail.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment3));

        List<OrderDetailModelV1> orderDetails = Arrays.asList(orderDetail);
        List<RefundOrderDetailModel> refundOrderDetails = Arrays.asList(refundDetail);

        // When
        List<RefundOrderPaymentModel> result =
                InvoiceConvertor.getAllRefundOrderPayments(orderDetails, refundOrderDetails);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains(payment1));
        assertTrue(result.contains(payment2));
        assertTrue(result.contains(payment3));
    }

    @Test
    void getAllRefundOrderPayments_withDuplicatePayments_removeDuplicatesKeepingFirst() {
        // Given
        RefundOrderPaymentModel payment1 = createMockPayment(1);
        RefundOrderPaymentModel payment2 = createMockPayment(1); // Same ID as payment1
        RefundOrderPaymentModel payment3 = createMockPayment(3);

        OrderDetailModelV1 orderDetail = mock(OrderDetailModelV1.class);
        RefundOrderDetailModel refundDetail = mock(RefundOrderDetailModel.class);

        when(orderDetail.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment1));
        when(refundDetail.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment2, payment3));

        List<OrderDetailModelV1> orderDetails = Arrays.asList(orderDetail);
        List<RefundOrderDetailModel> refundOrderDetails = Arrays.asList(refundDetail);

        // When
        List<RefundOrderPaymentModel> result =
                InvoiceConvertor.getAllRefundOrderPayments(orderDetails, refundOrderDetails);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(payment1)); // First occurrence should be kept
        assertTrue(result.contains(payment3));
        assertFalse(result.contains(payment2)); // Duplicate should be removed
    }

    @Test
    void getAllRefundOrderPayments_withMultipleOrderDetailsAndRefundDetails_handlesComplexScenario() {
        // Given
        RefundOrderPaymentModel payment1 = createMockPayment(1);
        RefundOrderPaymentModel payment2 = createMockPayment(2);
        RefundOrderPaymentModel payment3 = createMockPayment(3);
        RefundOrderPaymentModel payment4 = createMockPayment(4);

        OrderDetailModelV1 orderDetail1 = mock(OrderDetailModelV1.class);
        OrderDetailModelV1 orderDetail2 = mock(OrderDetailModelV1.class);
        RefundOrderDetailModel refundDetail1 = mock(RefundOrderDetailModel.class);
        RefundOrderDetailModel refundDetail2 = mock(RefundOrderDetailModel.class);

        when(orderDetail1.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment1));
        when(orderDetail2.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment2));
        when(refundDetail1.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment3));
        when(refundDetail2.getRefundOrderPaymentsList()).thenReturn(Arrays.asList(payment4));

        List<OrderDetailModelV1> orderDetails = Arrays.asList(orderDetail1, orderDetail2);
        List<RefundOrderDetailModel> refundOrderDetails = Arrays.asList(refundDetail1, refundDetail2);

        // When
        List<RefundOrderPaymentModel> result =
                InvoiceConvertor.getAllRefundOrderPayments(orderDetails, refundOrderDetails);

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        assertTrue(result.contains(payment1));
        assertTrue(result.contains(payment2));
        assertTrue(result.contains(payment3));
        assertTrue(result.contains(payment4));
    }

    @Test
    void getAllRefundOrderPayments_withEmptyPaymentLists_returnsEmptyList() {
        // Given
        OrderDetailModelV1 orderDetail = mock(OrderDetailModelV1.class);
        RefundOrderDetailModel refundDetail = mock(RefundOrderDetailModel.class);

        when(orderDetail.getRefundOrderPaymentsList()).thenReturn(Collections.emptyList());
        when(refundDetail.getRefundOrderPaymentsList()).thenReturn(Collections.emptyList());

        List<OrderDetailModelV1> orderDetails = Arrays.asList(orderDetail);
        List<RefundOrderDetailModel> refundOrderDetails = Arrays.asList(refundDetail);

        // When
        List<RefundOrderPaymentModel> result =
                InvoiceConvertor.getAllRefundOrderPayments(orderDetails, refundOrderDetails);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    private RefundOrderPaymentModel createMockPayment(long id) {
        RefundOrderPaymentModel payment = mock(RefundOrderPaymentModel.class);
        when(payment.getId()).thenReturn(id);
        return payment;
    }
}
