package com.moego.svc.online.booking.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.LodgingAssignAppointmentInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetEvaluationInfo;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.LodgingAssignInfoRequest;
import com.moego.idl.service.appointment.v1.LodgingAssignInfoResponse;
import com.moego.idl.service.appointment.v1.LodgingServiceGrpc;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.GetLodgingUnitListResponse;
import com.moego.idl.service.offering.v1.LodgingTypeServiceGrpc;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.MGetLodgingTypeRequest;
import com.moego.idl.service.offering.v1.MGetLodgingTypeResponse;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
class LodgingServiceTest {

    @Mock
    private LodgingTypeServiceGrpc.LodgingTypeServiceBlockingStub lodgingTypeClient;

    @Mock
    private LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitClient;

    @Mock
    private LodgingServiceGrpc.LodgingServiceBlockingStub lodgingClient;

    @InjectMocks
    private LodgingService lodgingService;

    @Test
    void testCalPetCntNeedPerDay() {
        List<GetAutoAssignResponse.AssignRequire> roomAssignRequires = Arrays.asList(
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(1L)
                        .setStartDate("2024-01-01")
                        .setEndDate("2024-01-03")
                        .build(),
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(2L)
                        .setStartDate("2024-01-02")
                        .setEndDate("2024-01-04")
                        .build(),
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(3L)
                        .addAllSpecificDates(Arrays.asList("2024-01-01", "2024-01-03"))
                        .build());

        Map<String, Integer> result = LodgingService.calPetCntNeedPerDay(roomAssignRequires, false);

        assertNotNull(result);
        assertEquals(2, result.get("2024-01-01"));
        assertEquals(2, result.get("2024-01-02"));
        assertEquals(3, result.get("2024-01-03"));
        assertEquals(1, result.get("2024-01-04"));
    }

    @Test
    void testCalPetCntNeedPerDay_withExcludeBoardingLastDayTrue() {
        List<GetAutoAssignResponse.AssignRequire> roomAssignRequires = Arrays.asList(
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(1L)
                        .setStartDate("2024-01-01")
                        .setEndDate("2024-01-03")
                        .setServiceItemType(ServiceItemType.BOARDING)
                        .build(),
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(2L)
                        .setStartDate("2024-01-02")
                        .setEndDate("2024-01-04")
                        .setServiceItemType(ServiceItemType.BOARDING)
                        .build(),
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(3L)
                        .addAllSpecificDates(Arrays.asList("2024-01-01", "2024-01-03"))
                        .setServiceItemType(ServiceItemType.DAYCARE)
                        .build());

        Map<String, Integer> result = LodgingService.calPetCntNeedPerDay(roomAssignRequires, true);

        assertNotNull(result);
        assertEquals(2, result.get("2024-01-01"));
        assertEquals(2, result.get("2024-01-02"));
        assertEquals(2, result.get("2024-01-03"));
        assertEquals(null, result.get("2024-01-04"));
    }

    @Test
    void testGetLodgingTypeByIds() {
        List<Long> idList = Arrays.asList(1L, 2L, 3L);
        List<LodgingTypeModel> expectedModels = Arrays.asList(
                LodgingTypeModel.newBuilder().setId(1L).build(),
                LodgingTypeModel.newBuilder().setId(2L).build(),
                LodgingTypeModel.newBuilder().setId(3L).build());

        when(lodgingTypeClient.mGetLodgingType(any(MGetLodgingTypeRequest.class)))
                .thenReturn(MGetLodgingTypeResponse.newBuilder()
                        .addAllLodgingTypeList(expectedModels)
                        .build());

        List<LodgingTypeModel> result = lodgingService.getLodgingTypeByIds(idList);

        assertEquals(expectedModels, result);
        verify(lodgingTypeClient).mGetLodgingType(any(MGetLodgingTypeRequest.class));
    }

    @Test
    void testGetLodgingAssignInfo() {
        Long companyId = 1L;
        Long businessId = 2L;
        String startDate = "2024-01-01";
        String endDate = "2024-01-07";

        List<LodgingAssignInfo> expectedAssignInfos = Arrays.asList(
                LodgingAssignInfo.newBuilder().setLodgingId(1L).build(),
                LodgingAssignInfo.newBuilder().setLodgingId(2L).build());

        when(lodgingClient.lodgingAssignInfo(any(LodgingAssignInfoRequest.class)))
                .thenReturn(LodgingAssignInfoResponse.newBuilder()
                        .addAllLodgingAssignInfo(expectedAssignInfos)
                        .build());

        List<LodgingAssignInfo> result = lodgingService.getLodgingAssignInfo(companyId, businessId, startDate, endDate);

        assertEquals(expectedAssignInfos, result);
        verify(lodgingClient).lodgingAssignInfo(any(LodgingAssignInfoRequest.class));
    }

    @Test
    void testCalPetCntPerLodgingPerDay() {
        String startDate = "2024-01-01";
        String endDate = "2024-01-03";
        List<LodgingAssignInfo> assignInfoList = Arrays.asList(
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(1L)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .setPetId(1)
                                        .setStartDate("2024-01-01")
                                        .setEndDate("2024-01-02")
                                        .build())
                                .build())
                        .build(),
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(1L)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetEvaluations(LodgingAssignPetEvaluationInfo.newBuilder()
                                        .setPetId(2)
                                        .setStartDate("2024-01-02")
                                        .setEndDate("2024-01-03")
                                        .build())
                                .build())
                        .build());

        Map<Long, Map<String, Integer>> result =
                LodgingService.calPetCntPerLodgingPerDay(startDate, endDate, assignInfoList, false);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1L));
        Map<String, Integer> lodgingCounts = result.get(1L);
        assertEquals(1, lodgingCounts.get("2024-01-01"));
        assertEquals(2, lodgingCounts.get("2024-01-02"));
        assertEquals(1, lodgingCounts.get("2024-01-03"));
    }

    @Test
    void testCalPetCntPerLodgingPerDay_withExcludeBoardingLastDayTrue() {
        String startDate = "2024-01-01";
        String endDate = "2024-01-03";
        List<LodgingAssignInfo> assignInfoList = Arrays.asList(
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(1L)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .setPetId(1)
                                        .setStartDate("2024-01-01")
                                        .setEndDate("2024-01-03")
                                        .setServiceItemType(ServiceItemType.BOARDING)
                                        .build())
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .setPetId(2)
                                        .setStartDate("2024-01-01")
                                        .setEndDate("2024-01-03")
                                        .setServiceItemType(ServiceItemType.BOARDING)
                                        .build())
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .setPetId(2)
                                        .addAllSpecificDates(List.of("2024-01-01", "2024-01-03"))
                                        .setServiceItemType(ServiceItemType.DAYCARE)
                                        .build())
                                .build())
                        .build(),
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(1L)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetEvaluations(LodgingAssignPetEvaluationInfo.newBuilder()
                                        .setPetId(3)
                                        .setStartDate("2024-01-02")
                                        .setEndDate("2024-01-03")
                                        .build())
                                .build())
                        .build());

        Map<Long, Map<String, Integer>> result =
                LodgingService.calPetCntPerLodgingPerDay(startDate, endDate, assignInfoList, true);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1L));
        Map<String, Integer> lodgingCounts = result.get(1L);
        assertEquals(2, lodgingCounts.get("2024-01-01"));
        assertEquals(3, lodgingCounts.get("2024-01-02"));
        assertEquals(2, lodgingCounts.get("2024-01-03"));
    }

    @Test
    void testIsLodgingAvailable() {
        LodgingTypeModel lodgingTypeModel = LodgingTypeModel.newBuilder()
                .setMaxPetNum(3)
                .setLodgingUnitType(LodgingUnitType.AREA)
                .build();
        Map<String, Integer> datePetCntNeed = Map.of(
                "2024-01-01", 2,
                "2024-01-02", 1,
                "2024-01-03", 3);
        Map<String, Integer> datePetCntExist = Map.of(
                "2024-01-01", 1,
                "2024-01-02", 2,
                "2024-01-03", 0);

        boolean result = LodgingService.isLodgingAvailable(lodgingTypeModel, datePetCntNeed, datePetCntExist);

        assertTrue(result);
    }

    @Test
    void testIsLodgingAvailable_baseOnRoom() {
        LodgingTypeModel lodgingTypeModel = LodgingTypeModel.newBuilder()
                .setMaxPetNum(3)
                .setLodgingUnitType(LodgingUnitType.ROOM)
                .build();
        Map<String, Integer> datePetCntNeed = Map.of(
                "2024-01-01", 2,
                "2024-01-02", 1,
                "2024-01-03", 3);
        Map<String, Integer> datePetCntExist = Map.of(
                "2024-01-01", 1,
                "2024-01-02", 2,
                "2024-01-03", 0);

        boolean result = LodgingService.isLodgingAvailable(lodgingTypeModel, datePetCntNeed, datePetCntExist);

        assertFalse(result);
    }

    @Test
    void testFilterLodgingTypeByPetSize() {
        List<LodgingTypeModel> lodgingTypeList = Arrays.asList(
                LodgingTypeModel.newBuilder()
                        .setId(1L)
                        .setPetSizeFilter(true)
                        .addPetSizeIds(1L)
                        .build(),
                LodgingTypeModel.newBuilder().setId(2L).setPetSizeFilter(false).build(),
                LodgingTypeModel.newBuilder()
                        .setId(3L)
                        .setPetSizeFilter(true)
                        .addPetSizeIds(2L)
                        .build());
        Long petSizeId = 1L;

        List<LodgingTypeModel> result = LodgingService.filterLodgingTypeByPetSize(lodgingTypeList, petSizeId);

        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(lt -> lt.getId() == 1L));
        assertTrue(result.stream().anyMatch(lt -> lt.getId() == 2L));
    }

    @Test
    void testFilterLodgingTypeByService() {
        List<LodgingTypeModel> lodgingTypeList = Arrays.asList(
                LodgingTypeModel.newBuilder().setId(1L).build(),
                LodgingTypeModel.newBuilder().setId(2L).build(),
                LodgingTypeModel.newBuilder().setId(3L).build());
        ServiceBriefView service = ServiceBriefView.newBuilder()
                .setLodgingFilter(true)
                .addCustomizedLodgings(1L)
                .addCustomizedLodgings(3L)
                .build();

        List<LodgingTypeModel> result = LodgingService.filterLodgingTypeByService(lodgingTypeList, service);

        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(lt -> lt.getId() == 1L));
        assertTrue(result.stream().anyMatch(lt -> lt.getId() == 3L));
    }

    @Test
    void testFilterLodgingTypeByDaycareService_NoLodgingFilter() {
        List<LodgingTypeModel> lodgingTypeList = Arrays.asList(
                LodgingTypeModel.newBuilder().setId(1L).build(),
                LodgingTypeModel.newBuilder().setId(2L).build(),
                LodgingTypeModel.newBuilder().setId(3L).build());
        ServiceBriefView service = ServiceBriefView.newBuilder()
                .setLodgingFilter(false)
                .setServiceItemType(ServiceItemType.DAYCARE)
                .build();

        List<LodgingTypeModel> result = LodgingService.filterLodgingTypeByService(lodgingTypeList, service);

        assertEquals(0, result.size());
    }

    @Test
    void testGetLodgingTypeByUnits() {
        List<LodgingUnitModel> unitList = Arrays.asList(
                LodgingUnitModel.newBuilder().setLodgingTypeId(1L).build(),
                LodgingUnitModel.newBuilder().setLodgingTypeId(2L).build());
        List<LodgingTypeModel> expectedTypes = Arrays.asList(
                LodgingTypeModel.newBuilder().setId(1L).build(),
                LodgingTypeModel.newBuilder().setId(2L).build());

        when(lodgingTypeClient.mGetLodgingType(any(MGetLodgingTypeRequest.class)))
                .thenReturn(MGetLodgingTypeResponse.newBuilder()
                        .addAllLodgingTypeList(expectedTypes)
                        .build());

        List<LodgingTypeModel> result = lodgingService.getLodgingTypeByUnits(unitList);

        assertEquals(expectedTypes, result);
        verify(lodgingTypeClient).mGetLodgingType(any(MGetLodgingTypeRequest.class));
    }

    @Test
    void testGetLodgingUnit() {
        Long companyId = 1L;
        Long businessId = 2L;
        List<LodgingUnitModel> expectedUnits = Arrays.asList(
                LodgingUnitModel.newBuilder().setId(1L).build(),
                LodgingUnitModel.newBuilder().setId(2L).build());

        when(lodgingUnitClient.getLodgingUnitList(any(GetLodgingUnitListRequest.class)))
                .thenReturn(GetLodgingUnitListResponse.newBuilder()
                        .addAllLodgingUnitList(expectedUnits)
                        .build());

        List<LodgingUnitModel> result = lodgingService.getLodgingUnit(companyId, businessId);

        assertEquals(expectedUnits, result);
        verify(lodgingUnitClient).getLodgingUnitList(any(GetLodgingUnitListRequest.class));
    }

    @Test
    void testGetLodgingUnitByUnitIds() {
        Long companyId = 1L;
        Long businessId = 2L;
        Collection<Long> unitIds = List.of(1L, 2L);
        List<LodgingUnitModel> expectedUnits = Arrays.asList(
                LodgingUnitModel.newBuilder().setId(1L).build(),
                LodgingUnitModel.newBuilder().setId(2L).build());

        when(lodgingUnitClient.getLodgingUnitList(any(GetLodgingUnitListRequest.class)))
                .thenReturn(GetLodgingUnitListResponse.newBuilder()
                        .addAllLodgingUnitList(expectedUnits)
                        .build());

        List<LodgingUnitModel> result = lodgingService.getLodgingUnitByUnitIds(companyId, businessId, unitIds);

        assertEquals(expectedUnits, result);
        verify(lodgingUnitClient).getLodgingUnitList(any(GetLodgingUnitListRequest.class));
    }
}
