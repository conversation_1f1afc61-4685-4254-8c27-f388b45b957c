spring:
  data:
    redis:
      host: redis.t2.moego.dev
      password: 7feo8GefoM_96sVidefRn_47ea2X0f2b
      port: 40179
      ssl:
        enabled: false
zendesk:
  jwtKid: kid
  jwtSecret: key

moego:
  feature-flag:
    growth-book:
      api-host: https://growthbook.moego.pet/growthbook-api
      client-key: sdk-qygeRRneunZQJxf
  session:
    sources:
      - name: customer
        cookie-name: MGSID-C-S1
        legacy-cookie-names:
          - MGSID-S1
        domains:
          - pattern: '^my\.s1\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: ob
        cookie-name: MGSID-OB-S1
        legacy-cookie-names:
          - Customer-Token
        domains:
          - pattern: '^(form|booking)\.s1\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
            # *.s1.moego.online
          - pattern: '^[^.]+\.s1\.moego\.online$'
            cookie-target-domain-level: 4
        max-age: 2592000
        sub-max-age: 2592000
      - name: mis
        cookie-name: MGSID-MIS-S1
        domains:
          - pattern: '^mis\.s1\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: business
        cookie-name: MGSID-B-S1
        domains:
          - pattern: '^.*$'
            cookie-target-domain-level: 3
        max-age: 2592000
